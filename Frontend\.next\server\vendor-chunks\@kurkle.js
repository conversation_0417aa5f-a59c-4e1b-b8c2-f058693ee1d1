"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@kurkle";
exports.ids = ["vendor-chunks/@kurkle"];
exports.modules = {

/***/ "(ssr)/./node_modules/@kurkle/color/dist/color.esm.js":
/*!******************************************************!*\
  !*** ./node_modules/@kurkle/color/dist/color.esm.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Color: () => (/* binding */ Color),\n/* harmony export */   b2n: () => (/* binding */ b2n),\n/* harmony export */   b2p: () => (/* binding */ b2p),\n/* harmony export */   \"default\": () => (/* binding */ index_esm),\n/* harmony export */   hexParse: () => (/* binding */ hexParse),\n/* harmony export */   hexString: () => (/* binding */ hexString),\n/* harmony export */   hsl2rgb: () => (/* binding */ hsl2rgb),\n/* harmony export */   hslString: () => (/* binding */ hslString),\n/* harmony export */   hsv2rgb: () => (/* binding */ hsv2rgb),\n/* harmony export */   hueParse: () => (/* binding */ hueParse),\n/* harmony export */   hwb2rgb: () => (/* binding */ hwb2rgb),\n/* harmony export */   lim: () => (/* binding */ lim),\n/* harmony export */   n2b: () => (/* binding */ n2b),\n/* harmony export */   n2p: () => (/* binding */ n2p),\n/* harmony export */   nameParse: () => (/* binding */ nameParse),\n/* harmony export */   p2b: () => (/* binding */ p2b),\n/* harmony export */   rgb2hsl: () => (/* binding */ rgb2hsl),\n/* harmony export */   rgbParse: () => (/* binding */ rgbParse),\n/* harmony export */   rgbString: () => (/* binding */ rgbString),\n/* harmony export */   rotate: () => (/* binding */ rotate),\n/* harmony export */   round: () => (/* binding */ round)\n/* harmony export */ });\n/*!\n * @kurkle/color v0.3.4\n * https://github.com/kurkle/color#readme\n * (c) 2024 Jukka Kurkela\n * Released under the MIT License\n */\nfunction round(v) {\n  return v + 0.5 | 0;\n}\nconst lim = (v, l, h) => Math.max(Math.min(v, h), l);\nfunction p2b(v) {\n  return lim(round(v * 2.55), 0, 255);\n}\nfunction b2p(v) {\n  return lim(round(v / 2.55), 0, 100);\n}\nfunction n2b(v) {\n  return lim(round(v * 255), 0, 255);\n}\nfunction b2n(v) {\n  return lim(round(v / 2.55) / 100, 0, 1);\n}\nfunction n2p(v) {\n  return lim(round(v * 100), 0, 100);\n}\n\nconst map$1 = {0: 0, 1: 1, 2: 2, 3: 3, 4: 4, 5: 5, 6: 6, 7: 7, 8: 8, 9: 9, A: 10, B: 11, C: 12, D: 13, E: 14, F: 15, a: 10, b: 11, c: 12, d: 13, e: 14, f: 15};\nconst hex = [...'0123456789ABCDEF'];\nconst h1 = b => hex[b & 0xF];\nconst h2 = b => hex[(b & 0xF0) >> 4] + hex[b & 0xF];\nconst eq = b => ((b & 0xF0) >> 4) === (b & 0xF);\nconst isShort = v => eq(v.r) && eq(v.g) && eq(v.b) && eq(v.a);\nfunction hexParse(str) {\n  var len = str.length;\n  var ret;\n  if (str[0] === '#') {\n    if (len === 4 || len === 5) {\n      ret = {\n        r: 255 & map$1[str[1]] * 17,\n        g: 255 & map$1[str[2]] * 17,\n        b: 255 & map$1[str[3]] * 17,\n        a: len === 5 ? map$1[str[4]] * 17 : 255\n      };\n    } else if (len === 7 || len === 9) {\n      ret = {\n        r: map$1[str[1]] << 4 | map$1[str[2]],\n        g: map$1[str[3]] << 4 | map$1[str[4]],\n        b: map$1[str[5]] << 4 | map$1[str[6]],\n        a: len === 9 ? (map$1[str[7]] << 4 | map$1[str[8]]) : 255\n      };\n    }\n  }\n  return ret;\n}\nconst alpha = (a, f) => a < 255 ? f(a) : '';\nfunction hexString(v) {\n  var f = isShort(v) ? h1 : h2;\n  return v\n    ? '#' + f(v.r) + f(v.g) + f(v.b) + alpha(v.a, f)\n    : undefined;\n}\n\nconst HUE_RE = /^(hsla?|hwb|hsv)\\(\\s*([-+.e\\d]+)(?:deg)?[\\s,]+([-+.e\\d]+)%[\\s,]+([-+.e\\d]+)%(?:[\\s,]+([-+.e\\d]+)(%)?)?\\s*\\)$/;\nfunction hsl2rgbn(h, s, l) {\n  const a = s * Math.min(l, 1 - l);\n  const f = (n, k = (n + h / 30) % 12) => l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);\n  return [f(0), f(8), f(4)];\n}\nfunction hsv2rgbn(h, s, v) {\n  const f = (n, k = (n + h / 60) % 6) => v - v * s * Math.max(Math.min(k, 4 - k, 1), 0);\n  return [f(5), f(3), f(1)];\n}\nfunction hwb2rgbn(h, w, b) {\n  const rgb = hsl2rgbn(h, 1, 0.5);\n  let i;\n  if (w + b > 1) {\n    i = 1 / (w + b);\n    w *= i;\n    b *= i;\n  }\n  for (i = 0; i < 3; i++) {\n    rgb[i] *= 1 - w - b;\n    rgb[i] += w;\n  }\n  return rgb;\n}\nfunction hueValue(r, g, b, d, max) {\n  if (r === max) {\n    return ((g - b) / d) + (g < b ? 6 : 0);\n  }\n  if (g === max) {\n    return (b - r) / d + 2;\n  }\n  return (r - g) / d + 4;\n}\nfunction rgb2hsl(v) {\n  const range = 255;\n  const r = v.r / range;\n  const g = v.g / range;\n  const b = v.b / range;\n  const max = Math.max(r, g, b);\n  const min = Math.min(r, g, b);\n  const l = (max + min) / 2;\n  let h, s, d;\n  if (max !== min) {\n    d = max - min;\n    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n    h = hueValue(r, g, b, d, max);\n    h = h * 60 + 0.5;\n  }\n  return [h | 0, s || 0, l];\n}\nfunction calln(f, a, b, c) {\n  return (\n    Array.isArray(a)\n      ? f(a[0], a[1], a[2])\n      : f(a, b, c)\n  ).map(n2b);\n}\nfunction hsl2rgb(h, s, l) {\n  return calln(hsl2rgbn, h, s, l);\n}\nfunction hwb2rgb(h, w, b) {\n  return calln(hwb2rgbn, h, w, b);\n}\nfunction hsv2rgb(h, s, v) {\n  return calln(hsv2rgbn, h, s, v);\n}\nfunction hue(h) {\n  return (h % 360 + 360) % 360;\n}\nfunction hueParse(str) {\n  const m = HUE_RE.exec(str);\n  let a = 255;\n  let v;\n  if (!m) {\n    return;\n  }\n  if (m[5] !== v) {\n    a = m[6] ? p2b(+m[5]) : n2b(+m[5]);\n  }\n  const h = hue(+m[2]);\n  const p1 = +m[3] / 100;\n  const p2 = +m[4] / 100;\n  if (m[1] === 'hwb') {\n    v = hwb2rgb(h, p1, p2);\n  } else if (m[1] === 'hsv') {\n    v = hsv2rgb(h, p1, p2);\n  } else {\n    v = hsl2rgb(h, p1, p2);\n  }\n  return {\n    r: v[0],\n    g: v[1],\n    b: v[2],\n    a: a\n  };\n}\nfunction rotate(v, deg) {\n  var h = rgb2hsl(v);\n  h[0] = hue(h[0] + deg);\n  h = hsl2rgb(h);\n  v.r = h[0];\n  v.g = h[1];\n  v.b = h[2];\n}\nfunction hslString(v) {\n  if (!v) {\n    return;\n  }\n  const a = rgb2hsl(v);\n  const h = a[0];\n  const s = n2p(a[1]);\n  const l = n2p(a[2]);\n  return v.a < 255\n    ? `hsla(${h}, ${s}%, ${l}%, ${b2n(v.a)})`\n    : `hsl(${h}, ${s}%, ${l}%)`;\n}\n\nconst map = {\n\tx: 'dark',\n\tZ: 'light',\n\tY: 're',\n\tX: 'blu',\n\tW: 'gr',\n\tV: 'medium',\n\tU: 'slate',\n\tA: 'ee',\n\tT: 'ol',\n\tS: 'or',\n\tB: 'ra',\n\tC: 'lateg',\n\tD: 'ights',\n\tR: 'in',\n\tQ: 'turquois',\n\tE: 'hi',\n\tP: 'ro',\n\tO: 'al',\n\tN: 'le',\n\tM: 'de',\n\tL: 'yello',\n\tF: 'en',\n\tK: 'ch',\n\tG: 'arks',\n\tH: 'ea',\n\tI: 'ightg',\n\tJ: 'wh'\n};\nconst names$1 = {\n\tOiceXe: 'f0f8ff',\n\tantiquewEte: 'faebd7',\n\taqua: 'ffff',\n\taquamarRe: '7fffd4',\n\tazuY: 'f0ffff',\n\tbeige: 'f5f5dc',\n\tbisque: 'ffe4c4',\n\tblack: '0',\n\tblanKedOmond: 'ffebcd',\n\tXe: 'ff',\n\tXeviTet: '8a2be2',\n\tbPwn: 'a52a2a',\n\tburlywood: 'deb887',\n\tcaMtXe: '5f9ea0',\n\tKartYuse: '7fff00',\n\tKocTate: 'd2691e',\n\tcSO: 'ff7f50',\n\tcSnflowerXe: '6495ed',\n\tcSnsilk: 'fff8dc',\n\tcrimson: 'dc143c',\n\tcyan: 'ffff',\n\txXe: '8b',\n\txcyan: '8b8b',\n\txgTMnPd: 'b8860b',\n\txWay: 'a9a9a9',\n\txgYF: '6400',\n\txgYy: 'a9a9a9',\n\txkhaki: 'bdb76b',\n\txmagFta: '8b008b',\n\txTivegYF: '556b2f',\n\txSange: 'ff8c00',\n\txScEd: '9932cc',\n\txYd: '8b0000',\n\txsOmon: 'e9967a',\n\txsHgYF: '8fbc8f',\n\txUXe: '483d8b',\n\txUWay: '2f4f4f',\n\txUgYy: '2f4f4f',\n\txQe: 'ced1',\n\txviTet: '9400d3',\n\tdAppRk: 'ff1493',\n\tdApskyXe: 'bfff',\n\tdimWay: '696969',\n\tdimgYy: '696969',\n\tdodgerXe: '1e90ff',\n\tfiYbrick: 'b22222',\n\tflSOwEte: 'fffaf0',\n\tfoYstWAn: '228b22',\n\tfuKsia: 'ff00ff',\n\tgaRsbSo: 'dcdcdc',\n\tghostwEte: 'f8f8ff',\n\tgTd: 'ffd700',\n\tgTMnPd: 'daa520',\n\tWay: '808080',\n\tgYF: '8000',\n\tgYFLw: 'adff2f',\n\tgYy: '808080',\n\thoneyMw: 'f0fff0',\n\thotpRk: 'ff69b4',\n\tRdianYd: 'cd5c5c',\n\tRdigo: '4b0082',\n\tivSy: 'fffff0',\n\tkhaki: 'f0e68c',\n\tlavFMr: 'e6e6fa',\n\tlavFMrXsh: 'fff0f5',\n\tlawngYF: '7cfc00',\n\tNmoncEffon: 'fffacd',\n\tZXe: 'add8e6',\n\tZcSO: 'f08080',\n\tZcyan: 'e0ffff',\n\tZgTMnPdLw: 'fafad2',\n\tZWay: 'd3d3d3',\n\tZgYF: '90ee90',\n\tZgYy: 'd3d3d3',\n\tZpRk: 'ffb6c1',\n\tZsOmon: 'ffa07a',\n\tZsHgYF: '20b2aa',\n\tZskyXe: '87cefa',\n\tZUWay: '778899',\n\tZUgYy: '778899',\n\tZstAlXe: 'b0c4de',\n\tZLw: 'ffffe0',\n\tlime: 'ff00',\n\tlimegYF: '32cd32',\n\tlRF: 'faf0e6',\n\tmagFta: 'ff00ff',\n\tmaPon: '800000',\n\tVaquamarRe: '66cdaa',\n\tVXe: 'cd',\n\tVScEd: 'ba55d3',\n\tVpurpN: '9370db',\n\tVsHgYF: '3cb371',\n\tVUXe: '7b68ee',\n\tVsprRggYF: 'fa9a',\n\tVQe: '48d1cc',\n\tVviTetYd: 'c71585',\n\tmidnightXe: '191970',\n\tmRtcYam: 'f5fffa',\n\tmistyPse: 'ffe4e1',\n\tmoccasR: 'ffe4b5',\n\tnavajowEte: 'ffdead',\n\tnavy: '80',\n\tTdlace: 'fdf5e6',\n\tTive: '808000',\n\tTivedBb: '6b8e23',\n\tSange: 'ffa500',\n\tSangeYd: 'ff4500',\n\tScEd: 'da70d6',\n\tpOegTMnPd: 'eee8aa',\n\tpOegYF: '98fb98',\n\tpOeQe: 'afeeee',\n\tpOeviTetYd: 'db7093',\n\tpapayawEp: 'ffefd5',\n\tpHKpuff: 'ffdab9',\n\tperu: 'cd853f',\n\tpRk: 'ffc0cb',\n\tplum: 'dda0dd',\n\tpowMrXe: 'b0e0e6',\n\tpurpN: '800080',\n\tYbeccapurpN: '663399',\n\tYd: 'ff0000',\n\tPsybrown: 'bc8f8f',\n\tPyOXe: '4169e1',\n\tsaddNbPwn: '8b4513',\n\tsOmon: 'fa8072',\n\tsandybPwn: 'f4a460',\n\tsHgYF: '2e8b57',\n\tsHshell: 'fff5ee',\n\tsiFna: 'a0522d',\n\tsilver: 'c0c0c0',\n\tskyXe: '87ceeb',\n\tUXe: '6a5acd',\n\tUWay: '708090',\n\tUgYy: '708090',\n\tsnow: 'fffafa',\n\tsprRggYF: 'ff7f',\n\tstAlXe: '4682b4',\n\ttan: 'd2b48c',\n\tteO: '8080',\n\ttEstN: 'd8bfd8',\n\ttomato: 'ff6347',\n\tQe: '40e0d0',\n\tviTet: 'ee82ee',\n\tJHt: 'f5deb3',\n\twEte: 'ffffff',\n\twEtesmoke: 'f5f5f5',\n\tLw: 'ffff00',\n\tLwgYF: '9acd32'\n};\nfunction unpack() {\n  const unpacked = {};\n  const keys = Object.keys(names$1);\n  const tkeys = Object.keys(map);\n  let i, j, k, ok, nk;\n  for (i = 0; i < keys.length; i++) {\n    ok = nk = keys[i];\n    for (j = 0; j < tkeys.length; j++) {\n      k = tkeys[j];\n      nk = nk.replace(k, map[k]);\n    }\n    k = parseInt(names$1[ok], 16);\n    unpacked[nk] = [k >> 16 & 0xFF, k >> 8 & 0xFF, k & 0xFF];\n  }\n  return unpacked;\n}\n\nlet names;\nfunction nameParse(str) {\n  if (!names) {\n    names = unpack();\n    names.transparent = [0, 0, 0, 0];\n  }\n  const a = names[str.toLowerCase()];\n  return a && {\n    r: a[0],\n    g: a[1],\n    b: a[2],\n    a: a.length === 4 ? a[3] : 255\n  };\n}\n\nconst RGB_RE = /^rgba?\\(\\s*([-+.\\d]+)(%)?[\\s,]+([-+.e\\d]+)(%)?[\\s,]+([-+.e\\d]+)(%)?(?:[\\s,/]+([-+.e\\d]+)(%)?)?\\s*\\)$/;\nfunction rgbParse(str) {\n  const m = RGB_RE.exec(str);\n  let a = 255;\n  let r, g, b;\n  if (!m) {\n    return;\n  }\n  if (m[7] !== r) {\n    const v = +m[7];\n    a = m[8] ? p2b(v) : lim(v * 255, 0, 255);\n  }\n  r = +m[1];\n  g = +m[3];\n  b = +m[5];\n  r = 255 & (m[2] ? p2b(r) : lim(r, 0, 255));\n  g = 255 & (m[4] ? p2b(g) : lim(g, 0, 255));\n  b = 255 & (m[6] ? p2b(b) : lim(b, 0, 255));\n  return {\n    r: r,\n    g: g,\n    b: b,\n    a: a\n  };\n}\nfunction rgbString(v) {\n  return v && (\n    v.a < 255\n      ? `rgba(${v.r}, ${v.g}, ${v.b}, ${b2n(v.a)})`\n      : `rgb(${v.r}, ${v.g}, ${v.b})`\n  );\n}\n\nconst to = v => v <= 0.0031308 ? v * 12.92 : Math.pow(v, 1.0 / 2.4) * 1.055 - 0.055;\nconst from = v => v <= 0.04045 ? v / 12.92 : Math.pow((v + 0.055) / 1.055, 2.4);\nfunction interpolate(rgb1, rgb2, t) {\n  const r = from(b2n(rgb1.r));\n  const g = from(b2n(rgb1.g));\n  const b = from(b2n(rgb1.b));\n  return {\n    r: n2b(to(r + t * (from(b2n(rgb2.r)) - r))),\n    g: n2b(to(g + t * (from(b2n(rgb2.g)) - g))),\n    b: n2b(to(b + t * (from(b2n(rgb2.b)) - b))),\n    a: rgb1.a + t * (rgb2.a - rgb1.a)\n  };\n}\n\nfunction modHSL(v, i, ratio) {\n  if (v) {\n    let tmp = rgb2hsl(v);\n    tmp[i] = Math.max(0, Math.min(tmp[i] + tmp[i] * ratio, i === 0 ? 360 : 1));\n    tmp = hsl2rgb(tmp);\n    v.r = tmp[0];\n    v.g = tmp[1];\n    v.b = tmp[2];\n  }\n}\nfunction clone(v, proto) {\n  return v ? Object.assign(proto || {}, v) : v;\n}\nfunction fromObject(input) {\n  var v = {r: 0, g: 0, b: 0, a: 255};\n  if (Array.isArray(input)) {\n    if (input.length >= 3) {\n      v = {r: input[0], g: input[1], b: input[2], a: 255};\n      if (input.length > 3) {\n        v.a = n2b(input[3]);\n      }\n    }\n  } else {\n    v = clone(input, {r: 0, g: 0, b: 0, a: 1});\n    v.a = n2b(v.a);\n  }\n  return v;\n}\nfunction functionParse(str) {\n  if (str.charAt(0) === 'r') {\n    return rgbParse(str);\n  }\n  return hueParse(str);\n}\nclass Color {\n  constructor(input) {\n    if (input instanceof Color) {\n      return input;\n    }\n    const type = typeof input;\n    let v;\n    if (type === 'object') {\n      v = fromObject(input);\n    } else if (type === 'string') {\n      v = hexParse(input) || nameParse(input) || functionParse(input);\n    }\n    this._rgb = v;\n    this._valid = !!v;\n  }\n  get valid() {\n    return this._valid;\n  }\n  get rgb() {\n    var v = clone(this._rgb);\n    if (v) {\n      v.a = b2n(v.a);\n    }\n    return v;\n  }\n  set rgb(obj) {\n    this._rgb = fromObject(obj);\n  }\n  rgbString() {\n    return this._valid ? rgbString(this._rgb) : undefined;\n  }\n  hexString() {\n    return this._valid ? hexString(this._rgb) : undefined;\n  }\n  hslString() {\n    return this._valid ? hslString(this._rgb) : undefined;\n  }\n  mix(color, weight) {\n    if (color) {\n      const c1 = this.rgb;\n      const c2 = color.rgb;\n      let w2;\n      const p = weight === w2 ? 0.5 : weight;\n      const w = 2 * p - 1;\n      const a = c1.a - c2.a;\n      const w1 = ((w * a === -1 ? w : (w + a) / (1 + w * a)) + 1) / 2.0;\n      w2 = 1 - w1;\n      c1.r = 0xFF & w1 * c1.r + w2 * c2.r + 0.5;\n      c1.g = 0xFF & w1 * c1.g + w2 * c2.g + 0.5;\n      c1.b = 0xFF & w1 * c1.b + w2 * c2.b + 0.5;\n      c1.a = p * c1.a + (1 - p) * c2.a;\n      this.rgb = c1;\n    }\n    return this;\n  }\n  interpolate(color, t) {\n    if (color) {\n      this._rgb = interpolate(this._rgb, color._rgb, t);\n    }\n    return this;\n  }\n  clone() {\n    return new Color(this.rgb);\n  }\n  alpha(a) {\n    this._rgb.a = n2b(a);\n    return this;\n  }\n  clearer(ratio) {\n    const rgb = this._rgb;\n    rgb.a *= 1 - ratio;\n    return this;\n  }\n  greyscale() {\n    const rgb = this._rgb;\n    const val = round(rgb.r * 0.3 + rgb.g * 0.59 + rgb.b * 0.11);\n    rgb.r = rgb.g = rgb.b = val;\n    return this;\n  }\n  opaquer(ratio) {\n    const rgb = this._rgb;\n    rgb.a *= 1 + ratio;\n    return this;\n  }\n  negate() {\n    const v = this._rgb;\n    v.r = 255 - v.r;\n    v.g = 255 - v.g;\n    v.b = 255 - v.b;\n    return this;\n  }\n  lighten(ratio) {\n    modHSL(this._rgb, 2, ratio);\n    return this;\n  }\n  darken(ratio) {\n    modHSL(this._rgb, 2, -ratio);\n    return this;\n  }\n  saturate(ratio) {\n    modHSL(this._rgb, 1, ratio);\n    return this;\n  }\n  desaturate(ratio) {\n    modHSL(this._rgb, 1, -ratio);\n    return this;\n  }\n  rotate(deg) {\n    rotate(this._rgb, deg);\n    return this;\n  }\n}\n\nfunction index_esm(input) {\n  return new Color(input);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@kurkle/color/dist/color.esm.js\n");

/***/ })

};
;