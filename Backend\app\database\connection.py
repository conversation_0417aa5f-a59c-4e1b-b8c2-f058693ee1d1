"""
Database connection and setup utilities
"""

import os
import logging
from pathlib import Path
from urllib.parse import quote_plus
from dotenv import load_dotenv

from sqlalchemy import create_engine, text, inspect
from sqlalchemy.orm import sessionmaker, declarative_base, Session

from supabase import create_client, Client

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

# ============================================================================
# 🗄️ DATABASE CONFIGURATION
# ============================================================================

# Get database configuration from environment variables
DB_HOST = os.getenv("DB_HOST", "localhost")
DB_PORT = os.getenv("DB_PORT", "5433")
DB_NAME = os.getenv("DB_NAME", "postgres")
DB_USER = os.getenv("DB_USER", "postgres")
DB_PASSWORD = os.getenv("DB_PASSWORD", "Yashwanth@567")
DB_POOL_SIZE = int(os.getenv("DB_POOL_SIZE", "5"))
DB_MAX_OVERFLOW = int(os.getenv("DB_MAX_OVERFLOW", "10"))

# Build database URL dynamically
password = quote_plus(DB_PASSWORD)
DATABASE_URL = f"postgresql://{DB_USER}:{password}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

engine = create_engine(DATABASE_URL, pool_size=DB_POOL_SIZE, max_overflow=DB_MAX_OVERFLOW)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# Upload directory configuration
UPLOAD_DIR_NAME = os.getenv("UPLOAD_DIR", "uploads")
UPLOAD_DIR = Path(UPLOAD_DIR_NAME)
UPLOAD_DIR.mkdir(exist_ok=True)

# ============================================================================
# 🔐 SUPABASE CONFIGURATION
# ============================================================================

# Supabase Configuration
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_ANON_KEY = os.getenv("SUPABASE_ANON_KEY")

# Initialize Supabase client
try:
    if SUPABASE_URL and SUPABASE_ANON_KEY:
        supabase: Client = create_client(SUPABASE_URL, SUPABASE_ANON_KEY)
        logger.info(f"✅ Supabase client initialized successfully: {SUPABASE_URL}")
    else:
        supabase = None
        logger.warning("⚠️ Supabase configuration missing - sync features disabled")
        logger.warning(f"SUPABASE_URL: {'✓' if SUPABASE_URL else '✗'}")
        logger.warning(f"SUPABASE_ANON_KEY: {'✓' if SUPABASE_ANON_KEY else '✗'}")
except Exception as e:
    supabase = None
    logger.error(f"❌ Failed to initialize Supabase client: {str(e)}")

# ============================================================================
# 🛠️ UTILITY FUNCTIONS
# ============================================================================

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def setup_database():
    """Ensure database schema exists with proper structure"""
    try:
        logger.info("🔄 Setting up database schema...")
        
        # Import all models to ensure they're registered
        import app.models.candidate  # noqa: F401
        import app.models.feedback   # noqa: F401
        
        # Create all tables with the updated schema
        Base.metadata.create_all(bind=engine)
        logger.info("✅ Database schema setup completed successfully")

        # Safely check and add candidate_id column to address_proofs if needed
        with engine.connect() as conn:
            inspector = inspect(engine)
            if "address_proofs" in inspector.get_table_names():
                columns = [col['name'] for col in inspector.get_columns("address_proofs")]
                if 'candidate_id' not in columns:
                    logger.info("🔧 Adding candidate_id column to address_proofs table...")
                    try:
                        conn.execute(text("ALTER TABLE address_proofs ADD COLUMN candidate_id VARCHAR"))
                        conn.commit()

                        # Use ORM to safely update existing records with candidate_id
                        from app.models.candidate import AddressProof, BackgroundVerification
                        with SessionLocal() as db:
                            # Get all address proofs without candidate_id
                            address_proofs_to_update = db.query(AddressProof).filter(
                                AddressProof.candidate_id.is_(None)
                            ).all()

                            for address_proof in address_proofs_to_update:
                                # Get the background verification record
                                bg_verification = db.query(BackgroundVerification).filter(
                                    BackgroundVerification.id == address_proof.background_verification_id
                                ).first()

                                if bg_verification:
                                    address_proof.candidate_id = bg_verification.candidate_id

                            db.commit()
                        logger.info("✅ Added candidate_id column to address_proofs table")
                    except Exception as e:
                        logger.warning(f"⚠️ Could not add candidate_id column: {str(e)}")
                        conn.rollback()
                else:
                    logger.info("✅ address_proofs table already has candidate_id column")

    except Exception as e:
        logger.error(f"Database setup error: {str(e)}")

def verify_file_database_sync():
    """Verify that uploaded files have corresponding database records"""
    try:
        logger.info("🔍 Verifying file-database synchronization...")

        if not UPLOAD_DIR.exists():
            logger.info("📁 No uploads directory found")
            return

        # Get all uploaded files
        uploaded_files = list(UPLOAD_DIR.glob("*"))
        logger.info(f"📁 Found {len(uploaded_files)} files in uploads directory")

        # Check database connection
        with SessionLocal() as db:
            # Import models for counting
            from app.models.candidate import Resume, Payslip, AddressProof, BackgroundVerification
            
            # Count database records
            resume_count = db.query(Resume).count()
            payslip_count = db.query(Payslip).count()
            address_proof_count = db.query(AddressProof).count()
            bg_verification_count = db.query(BackgroundVerification).count()

            logger.info(f"📊 Database records - Resumes: {resume_count}, Payslips: {payslip_count}, Address Proofs: {address_proof_count}, Background Verifications: {bg_verification_count}")

        logger.info("✅ File-database verification completed")

    except Exception as e:
        logger.error(f"❌ Error during file-database verification: {str(e)}")
