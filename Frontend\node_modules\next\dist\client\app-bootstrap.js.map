{"version": 3, "sources": ["../../src/client/app-bootstrap.ts"], "names": ["appBootstrap", "version", "process", "env", "__NEXT_VERSION", "window", "next", "appDir", "loadScriptsInSequence", "scripts", "hydrate", "length", "reduce", "promise", "src", "props", "then", "Promise", "resolve", "reject", "el", "document", "createElement", "key", "setAttribute", "onload", "onerror", "innerHTML", "children", "setTimeout", "head", "append<PERSON><PERSON><PERSON>", "catch", "err", "console", "error", "callback", "self", "__next_s"], "mappings": "AAAA;;;;;CAKC;;;;+BAqDeA;;;eAAAA;;;AAnDhB,MAAMC,UAAUC,QAAQC,GAAG,CAACC,cAAc;AAE1CC,OAAOC,IAAI,GAAG;IACZL;IACAM,QAAQ;AACV;AAEA,SAASC,sBACPC,OAAwD,EACxDC,OAAmB;IAEnB,IAAI,CAACD,WAAW,CAACA,QAAQE,MAAM,EAAE;QAC/B,OAAOD;IACT;IAEA,OAAOD,QACJG,MAAM,CAAC,CAACC;YAAS,CAACC,KAAKC,MAAM;QAC5B,OAAOF,QAAQG,IAAI,CAAC;YAClB,OAAO,IAAIC,QAAc,CAACC,SAASC;gBACjC,MAAMC,KAAKC,SAASC,aAAa,CAAC;gBAElC,IAAIP,OAAO;oBACT,IAAK,MAAMQ,OAAOR,MAAO;wBACvB,IAAIQ,QAAQ,YAAY;4BACtBH,GAAGI,YAAY,CAACD,KAAKR,KAAK,CAACQ,IAAI;wBACjC;oBACF;gBACF;gBAEA,IAAIT,KAAK;oBACPM,GAAGN,GAAG,GAAGA;oBACTM,GAAGK,MAAM,GAAG,IAAMP;oBAClBE,GAAGM,OAAO,GAAGP;gBACf,OAAO,IAAIJ,OAAO;oBAChBK,GAAGO,SAAS,GAAGZ,MAAMa,QAAQ;oBAC7BC,WAAWX;gBACb;gBAEAG,SAASS,IAAI,CAACC,WAAW,CAACX;YAC5B;QACF;IACF,GAAGH,QAAQC,OAAO,IACjBc,KAAK,CAAC,CAACC;QACNC,QAAQC,KAAK,CAACF;IACd,iDAAiD;IACnD,GACCjB,IAAI,CAAC;QACJN;IACF;AACJ;AAEO,SAASV,aAAaoC,QAAoB;IAC/C5B,sBAAsB,AAAC6B,KAAaC,QAAQ,EAAE;QAC5CF;IACF;AACF"}