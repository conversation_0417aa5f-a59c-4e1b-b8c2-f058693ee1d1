{"version": 3, "sources": ["../../../../src/client/components/router-reducer/fill-cache-with-new-subtree-data.ts"], "names": ["fillCacheWithNewSubTreeData", "newCache", "existingCache", "flightDataPath", "prefetchEntry", "isLastEntry", "length", "parallelRouteKey", "segment", "cache<PERSON>ey", "createRouterCache<PERSON>ey", "existingChildSegmentMap", "parallelRoutes", "get", "childSegmentMap", "Map", "set", "existingChildCacheNode", "childCacheNode", "lazyData", "seedData", "rsc", "loading", "prefetchRsc", "head", "prefetchHead", "lazyDataResolved", "invalidateCacheByRouterState", "fillLazyItemsTillLeafWithHead", "slice"], "mappings": ";;;;+<PERSON>ag<PERSON>;;;eAAAA;;;8CAR6B;+CACC;sCACT;AAM9B,SAASA,4BACdC,QAAmB,EACnBC,aAAwB,EACxBC,cAA8B,EAC9BC,aAAkC;IAElC,MAAMC,cAAcF,eAAeG,MAAM,IAAI;IAC7C,MAAM,CAACC,kBAAkBC,QAAQ,GAAGL;IAEpC,MAAMM,WAAWC,IAAAA,0CAAoB,EAACF;IAEtC,MAAMG,0BACJT,cAAcU,cAAc,CAACC,GAAG,CAACN;IAEnC,IAAI,CAACI,yBAAyB;QAC5B,6EAA6E;QAC7E,sEAAsE;QACtE;IACF;IAEA,IAAIG,kBAAkBb,SAASW,cAAc,CAACC,GAAG,CAACN;IAClD,IAAI,CAACO,mBAAmBA,oBAAoBH,yBAAyB;QACnEG,kBAAkB,IAAIC,IAAIJ;QAC1BV,SAASW,cAAc,CAACI,GAAG,CAACT,kBAAkBO;IAChD;IAEA,MAAMG,yBAAyBN,wBAAwBE,GAAG,CAACJ;IAC3D,IAAIS,iBAAiBJ,gBAAgBD,GAAG,CAACJ;IAEzC,IAAIJ,aAAa;QACf,IACE,CAACa,kBACD,CAACA,eAAeC,QAAQ,IACxBD,mBAAmBD,wBACnB;YACA,MAAMG,WAA8BjB,cAAc,CAAC,EAAE;YACrD,MAAMkB,MAAMD,QAAQ,CAAC,EAAE;YACvB,MAAME,UAAUF,QAAQ,CAAC,EAAE;YAC3BF,iBAAiB;gBACfC,UAAU;gBACVE;gBACAE,aAAa;gBACbC,MAAM;gBACNC,cAAc;gBACdH;gBACA,oEAAoE;gBACpEV,gBAAgBK,yBACZ,IAAIF,IAAIE,uBAAuBL,cAAc,IAC7C,IAAIG;gBACRW,kBAAkB;YACpB;YAEA,IAAIT,wBAAwB;gBAC1BU,IAAAA,0DAA4B,EAC1BT,gBACAD,wBACAd,cAAc,CAAC,EAAE;YAErB;YAEAyB,IAAAA,4DAA6B,EAC3BV,gBACAD,wBACAd,cAAc,CAAC,EAAE,EACjBiB,UACAjB,cAAc,CAAC,EAAE,EACjBC;YAGFU,gBAAgBE,GAAG,CAACP,UAAUS;QAChC;QACA;IACF;IAEA,IAAI,CAACA,kBAAkB,CAACD,wBAAwB;QAC9C,6EAA6E;QAC7E,sEAAsE;QACtE;IACF;IAEA,IAAIC,mBAAmBD,wBAAwB;QAC7CC,iBAAiB;YACfC,UAAUD,eAAeC,QAAQ;YACjCE,KAAKH,eAAeG,GAAG;YACvBE,aAAaL,eAAeK,WAAW;YACvCC,MAAMN,eAAeM,IAAI;YACzBC,cAAcP,eAAeO,YAAY;YACzCb,gBAAgB,IAAIG,IAAIG,eAAeN,cAAc;YACrDc,kBAAkB;YAClBJ,SAASJ,eAAeI,OAAO;QACjC;QACAR,gBAAgBE,GAAG,CAACP,UAAUS;IAChC;IAEAlB,4BACEkB,gBACAD,wBACAd,eAAe0B,KAAK,CAAC,IACrBzB;AAEJ"}