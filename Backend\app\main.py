#!/usr/bin/env python3
"""
🚀 FASTJOB - Clean Job Application Dashboard API

📋 ESSENTIAL FLOWS:
1. 👤 Candidate Management
2. 📄 Resume Management (Upload, view, update, download, show/hide)
3. 🤖 LLM Interview Feedback (Questions + Answers → AI generates scores & feedback)

🗄️ Database: PostgreSQL (Port 5433, Password: <PERSON><PERSON><PERSON><PERSON>@567)
"""

import logging
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database.connection import setup_database, verify_file_database_sync
from app.routes.candidates import router as candidates_router
from app.routes.feedback import router as feedback_router

# Import models to ensure they're registered with SQLAlchemy
import app.models.candidate  # noqa: F401
import app.models.feedback   # noqa: F401

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ============================================================================
# 🚀 FASTAPI APP
# ============================================================================

app = FastAPI(
    title="Job Application Dashboard",
    description="Complete job application management system with candidate tracking, resume management, interview feedback, and more",
    version="1.0.0",
    docs_url="/docs",  # Swagger UI
    redoc_url="/redoc",  # ReDoc
    openapi_url="/openapi.json"  # OpenAPI schema
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(candidates_router, prefix="/candidates", tags=["candidates"])
app.include_router(feedback_router, prefix="/feedback", tags=["feedback"])

@app.get("/", tags=["Root"])
async def root():
    """
    🏠 Welcome to Job Application Dashboard API

    This is the main endpoint that provides information about the API.
    """
    return {
        "message": "🚀 Job Application Dashboard API",
        "version": "1.0.0",
        "status": "✅ Active",
        "documentation": {
            "swagger_ui": "/docs",
            "redoc": "/redoc",
            "openapi_schema": "/openapi.json"
        },
        "main_endpoints": {
            "candidates": "/candidates",
            "feedback": "/feedback"
        },
        "features": [
            "👤 Candidate Management",
            "📄 Resume Management",
            "💰 Payslip Management",
            "🛡️ Background Verification",
            "🤖 Interview Feedback",
            "📅 Interview Scheduling"
        ]
    }

@app.get("/health", tags=["Health"])
async def health_check():
    """
    🏥 Health check endpoint

    Returns the current status of the API and database connection.
    """
    try:
        from app.database.connection import engine
        # Test database connection
        with engine.connect() as conn:
            conn.execute("SELECT 1")
        db_status = "✅ Connected"
    except Exception as e:
        db_status = f"❌ Error: {str(e)}"

    return {
        "status": "✅ Healthy",
        "timestamp": "2025-06-24T17:55:00Z",
        "database": db_status,
        "version": "1.0.0"
    }

if __name__ == "__main__":
    # Run database setup on startup
    try:
        logger.info("🔄 Setting up database schema...")
        setup_database()
        verify_file_database_sync()
        logger.info("✅ Database setup completed")
    except Exception as e:
        logger.error(f"❌ Database setup error: {str(e)}")
        # Continue anyway - tables might already exist

    # Print startup information
    print("\n" + "="*60)
    print("🚀 FASTJOB - Job Application Dashboard API")
    print("="*60)
    print("📍 Server will start on: http://localhost:8001")
    print("📚 Swagger UI (API Docs): http://localhost:8001/docs")
    print("📖 ReDoc Documentation: http://localhost:8001/redoc")
    print("🏥 Health Check: http://localhost:8001/health")
    print("="*60)
    print("🔄 Starting server...")

    uvicorn.run("app.main:app", host="0.0.0.0", port=8001, reload=True)
