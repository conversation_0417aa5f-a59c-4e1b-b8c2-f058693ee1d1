#!/usr/bin/env python3
"""
🚀 FASTJOB - Clean Job Application Dashboard API

📋 ESSENTIAL FLOWS:
1. 👤 Candidate Management
2. 📄 Resume Management (Upload, view, update, download, show/hide)
3. 🤖 LLM Interview Feedback (Questions + Answers → AI generates scores & feedback)

🗄️ Database: PostgreSQL (Port 5433, Password: <PERSON><PERSON><PERSON><PERSON>@567)
"""

import logging
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database.connection import setup_database, verify_file_database_sync
from app.routes.candidates import router as candidates_router
from app.routes.feedback import router as feedback_router
from app.routes.availability import router as availability_router
from app.routes.employment_documents import router as employment_documents_router
from app.utils.enums import (
    API_TITLE, API_VERSION, API_DESCRIPTION,
    DEFAULT_SERVER_HOST, DEFAULT_SERVER_PORT
)

# Import models to ensure they're registered with SQLAlchemy
import app.models.candidate  # noqa: F401
import app.models.feedback   # noqa: F401

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ============================================================================
# 🚀 FASTAPI APP
# ============================================================================

app = FastAPI(
    title=API_TITLE,
    description=API_DESCRIPTION,
    version=API_VERSION,
    docs_url="/docs",  # Swagger UI
    redoc_url="/redoc",  # ReDoc
    openapi_url="/openapi.json"  # OpenAPI schema
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(candidates_router, prefix="/candidates", tags=["candidates"])
app.include_router(feedback_router, prefix="/feedback", tags=["feedback"])
app.include_router(availability_router, tags=["availability"])
app.include_router(employment_documents_router, prefix="/candidates", tags=["employment-documents"])

@app.get("/", tags=["Root"])
async def root():
    """
    🏠 Welcome to Job Application Dashboard API

    This is the main endpoint that provides information about the API.
    """
    return {
        "message": f"🚀 {API_TITLE}",
        "version": API_VERSION,
        "status": "✅ Active",
        "documentation": {
            "swagger_ui": "/docs",
            "redoc": "/redoc",
            "openapi_schema": "/openapi.json"
        },
        "main_endpoints": {
            "candidates": "/candidates",
            "feedback": "/feedback",
            "availability": "/candidates/{id}/availability",
            "bookings": "/candidates/{id}/bookings",
            "employment_documents": "/candidates/{id}/offer-letter | /contract-agreement | /salary-negotiation-terms"
        },
        "features": [
            "👤 Candidate Management",
            "📄 Resume Management",
            "💰 Payslip Management",
            "🛡️ Background Verification",
            "🤖 Interview Feedback",
            "📅 Interview Scheduling",
            "📄 Employment Documents (Offer Letter, Contract, Salary Terms)"
        ]
    }

@app.get("/health", tags=["Health"])
async def health_check():
    """
    🏥 Health check endpoint

    Returns the current status of the API and database connection.
    """
    try:
        from app.database.connection import engine
        # Test database connection
        with engine.connect() as conn:
            conn.execute("SELECT 1")
        db_status = "✅ Connected"
    except Exception as e:
        db_status = f" Error: {str(e)}"

    from datetime import datetime
    current_timestamp = datetime.now().isoformat() + "Z"

    return {
        "status": "✅ Healthy",
        "timestamp": current_timestamp,
        "database": db_status,
        "version": API_VERSION
    }

if __name__ == "__main__":
    # Run database setup on startup
    try:
        logger.info("🔄 Setting up database schema...")
        setup_database()
        verify_file_database_sync()
        logger.info("✅ Database setup completed")
    except Exception as e:
        logger.error(f" Database setup error: {str(e)}")
        # Continue anyway - tables might already exist

    # Print startup information
    print("\n" + "="*60)
    print(f"🚀 {API_TITLE}")
    print("="*60)
    print(f"📍 Server will start on: http://{DEFAULT_SERVER_HOST}:{DEFAULT_SERVER_PORT}")
    print(f"📚 Swagger UI (API Docs): http://localhost:{DEFAULT_SERVER_PORT}/docs")
    print(f"📖 ReDoc Documentation: http://localhost:{DEFAULT_SERVER_PORT}/redoc")
    print(f"🏥 Health Check: http://localhost:{DEFAULT_SERVER_PORT}/health")
    print("="*60)
    print("🔄 Starting server...")

    uvicorn.run("app.main:app", host=DEFAULT_SERVER_HOST, port=DEFAULT_SERVER_PORT, reload=True)
