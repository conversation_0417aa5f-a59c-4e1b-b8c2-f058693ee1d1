"""
Candidate management routes
"""

import os
import logging
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session

from app.database.connection import get_db
from app.models.candidate import (
    Candidate, Resume, NoticePeriod, Payslip, 
    BackgroundVerification, AddressProof, ProvidentFund
)
from app.schemas.candidate import (
    CandidateCreate, CandidateResponse, ResumeResponse,
    NoticePeriodCreate, NoticePeriodResponse, PayslipResponse,
    BackgroundVerificationCreate, BackgroundVerificationResponse,
    AddressProofResponse, ProvidentFundCreate, ProvidentFundResponse
)
from app.utils.file_utils import (
    save_uploaded_file, save_payslip_file, save_address_proof_file,
    validate_resume_file, validate_payslip_file, validate_address_proof_file
)
from app.utils.validators import (
    validate_document_type, validate_payslip_count, validate_month_year
)

logger = logging.getLogger(__name__)
router = APIRouter()

# ============================================================================
# 👤 CANDIDATE ROUTES
# ============================================================================

@router.post("/", response_model=CandidateResponse, status_code=status.HTTP_201_CREATED)
async def create_candidate(candidate: CandidateCreate, db: Session = Depends(get_db)):
    try:
        existing = db.query(Candidate).filter(Candidate.candidate_id == candidate.candidate_id).first()
        if existing:
            raise HTTPException(status_code=400, detail=f"Candidate {candidate.candidate_id} already exists")

        db_candidate = Candidate(**candidate.model_dump())
        db.add(db_candidate)
        db.commit()
        db.refresh(db_candidate)

        logger.info(f"✅ Created candidate: {candidate.candidate_id}")
        return db_candidate
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to create candidate: {str(e)}")

@router.get("/", response_model=List[CandidateResponse])
async def get_all_candidates(db: Session = Depends(get_db)):
    """👥 Get all candidates"""
    try:
        candidates = db.query(Candidate).all()
        return candidates
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get candidates: {str(e)}")

@router.get("/{candidate_id}", response_model=CandidateResponse)
async def get_candidate(candidate_id: str, db: Session = Depends(get_db)):
    """👤 Get a specific candidate"""
    try:
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")
        return candidate
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get candidate: {str(e)}")

# ============================================================================
# 📄 RESUME ROUTES
# ============================================================================

@router.post("/{candidate_id}/resume", response_model=ResumeResponse, status_code=status.HTTP_201_CREATED)
async def upload_resume(candidate_id: str, file: UploadFile = File(...), db: Session = Depends(get_db)):
    """📄 Upload resume for a candidate (replaces existing if any)"""
    file_path = None
    try:
        # Check if candidate exists
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        # Validate file type and size
        validate_resume_file(file)

        # Handle existing resume
        existing_resume = db.query(Resume).filter(Resume.candidate_id == candidate_id).first()
        if existing_resume:
            # Remove old file from filesystem
            if os.path.exists(existing_resume.file_path):
                os.remove(existing_resume.file_path)
            # Delete old resume record
            db.delete(existing_resume)
            db.flush()  # Ensure deletion is committed

        # Save new file to filesystem
        file_path, unique_filename = save_uploaded_file(file, candidate_id)

        # Create new resume record in database
        resume = Resume(
            candidate_id=candidate_id,
            filename=unique_filename,
            original_filename=file.filename,
            file_path=file_path,
            file_size=file.size,
            content_type=file.content_type,
            is_visible=True
        )

        db.add(resume)
        db.commit()
        db.refresh(resume)

        logger.info(f"📤 Successfully uploaded resume for {candidate_id}: {unique_filename}")
        return resume

    except HTTPException:
        # Clean up file if database operation failed
        if file_path and os.path.exists(file_path):
            os.remove(file_path)
        raise
    except Exception as e:
        # Clean up file if database operation failed
        if file_path and os.path.exists(file_path):
            os.remove(file_path)
        db.rollback()
        logger.error(f"❌ Failed to upload resume for {candidate_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to upload resume: {str(e)}")

@router.get("/{candidate_id}/resume/view")
async def view_resume(candidate_id: str, db: Session = Depends(get_db)):
    try:
        resume = db.query(Resume).filter(Resume.candidate_id == candidate_id).first()
        if not resume:
            raise HTTPException(status_code=404, detail=f"No resume found for candidate {candidate_id}")

        if not resume.is_visible:
            raise HTTPException(status_code=403, detail="Resume is currently hidden")

        if not os.path.exists(resume.file_path):
            raise HTTPException(status_code=404, detail="Resume file not found on server")

        return FileResponse(
            path=resume.file_path,
            filename=resume.original_filename,
            media_type=resume.content_type,
            headers={"Content-Disposition": f"inline; filename={resume.original_filename}"}
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to view resume: {str(e)}")

@router.get("/{candidate_id}/resume/download")
async def download_resume(candidate_id: str, db: Session = Depends(get_db)):
    try:
        resume = db.query(Resume).filter(Resume.candidate_id == candidate_id).first()
        if not resume:
            raise HTTPException(status_code=404, detail=f"No resume found for candidate {candidate_id}")

        if not os.path.exists(resume.file_path):
            raise HTTPException(status_code=404, detail="Resume file not found on server")

        return FileResponse(
            path=resume.file_path,
            filename=resume.original_filename,
            media_type=resume.content_type,
            headers={"Content-Disposition": f"attachment; filename={resume.original_filename}"}
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to download resume: {str(e)}")

@router.put("/{candidate_id}/resume/toggle-visibility")
async def toggle_resume_visibility(candidate_id: str, db: Session = Depends(get_db)):
    """👁️ Toggle resume visibility (show/hide)"""
    try:
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        resume = db.query(Resume).filter(Resume.candidate_id == candidate_id).first()
        if not resume:
            raise HTTPException(status_code=404, detail=f"Resume not found for candidate {candidate_id}")

        # Toggle visibility
        resume.is_visible = not resume.is_visible
        db.commit()

        logger.info(f"✅ Toggled resume visibility for {candidate_id}: {resume.is_visible}")
        return {
            "message": f"Resume visibility {'enabled' if resume.is_visible else 'disabled'}",
            "candidate_id": candidate_id,
            "is_visible": resume.is_visible
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"❌ Error toggling resume visibility for {candidate_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to toggle resume visibility: {str(e)}")

# ============================================================================
# 📋 NOTICE PERIOD ROUTES
# ============================================================================

@router.post("/{candidate_id}/notice-period", response_model=NoticePeriodResponse, status_code=status.HTTP_201_CREATED)
async def create_or_update_notice_period(
    candidate_id: str,
    notice_data: NoticePeriodCreate,
    db: Session = Depends(get_db)
):
    """📋 Create or update candidate notice period information"""
    try:
        # Check if candidate exists
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        # Check if notice period already exists
        existing_notice = db.query(NoticePeriod).filter(NoticePeriod.candidate_id == candidate_id).first()

        if existing_notice:
            # Update existing notice period
            for field, value in notice_data.model_dump(exclude_unset=True).items():
                setattr(existing_notice, field, value)
            from datetime import datetime, timezone
            existing_notice.updated_at = datetime.now(timezone.utc)

            db.commit()
            db.refresh(existing_notice)

            logger.info(f"📋 Updated notice period for {candidate_id}")
            return existing_notice
        else:
            # Create new notice period
            notice_period = NoticePeriod(
                candidate_id=candidate_id,
                **notice_data.model_dump()
            )

            db.add(notice_period)
            db.commit()
            db.refresh(notice_period)

            logger.info(f"📋 Created notice period for {candidate_id}")
            return notice_period

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to create/update notice period: {str(e)}")

@router.get("/{candidate_id}/notice-period", response_model=NoticePeriodResponse)
async def get_notice_period(candidate_id: str, db: Session = Depends(get_db)):
    """📋 Get candidate notice period information"""
    try:
        notice_period = db.query(NoticePeriod).filter(NoticePeriod.candidate_id == candidate_id).first()
        if not notice_period:
            raise HTTPException(status_code=404, detail=f"No notice period found for candidate {candidate_id}")

        return notice_period
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get notice period: {str(e)}")

# ============================================================================
# 💰 PAYSLIP ROUTES
# ============================================================================

@router.post("/{candidate_id}/payslips", response_model=PayslipResponse, status_code=status.HTTP_201_CREATED)
async def upload_payslip(
    candidate_id: str,
    month_year: str,  # MM/YYYY format
    file: UploadFile = File(...),
    salary_amount: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """💰 Upload payslip for a specific month/year"""
    file_path = None
    try:
        # Check if candidate exists
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        # Skip month_year validation to allow multiple payslips with timestamps

        # Validate file
        validate_payslip_file(file)

        # Validate payslip count (max 6 files)
        validate_payslip_count(db, candidate_id, month_year)

        # Allow multiple payslips - no need to check for existing month/year
        # Each payslip upload creates a new record

        # Save new file to filesystem
        file_path, unique_filename = save_payslip_file(file, candidate_id, month_year)

        # Create new payslip record in database
        payslip = Payslip(
            candidate_id=candidate_id,
            filename=unique_filename,
            original_filename=file.filename,
            file_path=file_path,
            file_size=file.size,
            content_type=file.content_type,
            month_year=month_year,
            salary_amount=salary_amount
        )

        db.add(payslip)
        db.commit()
        db.refresh(payslip)

        logger.info(f"💰 Successfully uploaded payslip for {candidate_id} - {month_year}: {unique_filename}")
        return payslip

    except HTTPException:
        # Clean up file if database operation failed
        if file_path and os.path.exists(file_path):
            os.remove(file_path)
        raise
    except Exception as e:
        # Clean up file if database operation failed
        if file_path and os.path.exists(file_path):
            os.remove(file_path)
        db.rollback()
        logger.error(f"❌ Failed to upload payslip for {candidate_id} - {month_year}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to upload payslip: {str(e)}")

@router.get("/{candidate_id}/payslips", response_model=List[PayslipResponse])
async def get_payslips(candidate_id: str, db: Session = Depends(get_db)):
    """💰 Get all payslips for a candidate"""
    try:
        payslips = db.query(Payslip).filter(
            Payslip.candidate_id == candidate_id
        ).order_by(Payslip.month_year.desc()).all()

        return payslips
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get payslips: {str(e)}")

@router.get("/{candidate_id}/payslips/summary")
async def get_payslips_summary(candidate_id: str, db: Session = Depends(get_db)):
    """💰 Get payslips summary for UI display"""
    try:
        payslips = db.query(Payslip).filter(
            Payslip.candidate_id == candidate_id
        ).order_by(Payslip.month_year.desc()).all()

        return {
            "total_payslips": len(payslips),
            "max_allowed": 6,
            "remaining_slots": max(0, 6 - len(payslips)),
            "payslips": [
                {
                    "id": p.id,
                    "month_year": p.month_year,
                    "filename": p.original_filename,
                    "file_size_mb": round(p.file_size / (1024 * 1024), 2),
                    "uploaded_at": p.created_at.strftime("%d/%m/%Y")
                } for p in payslips
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get payslips summary: {str(e)}")

@router.get("/{candidate_id}/payslips/{payslip_id}/download")
async def download_payslip(candidate_id: str, payslip_id: int, db: Session = Depends(get_db)):
    """💰 Download a specific payslip"""
    try:
        payslip = db.query(Payslip).filter(
            Payslip.id == payslip_id,
            Payslip.candidate_id == candidate_id
        ).first()

        if not payslip:
            raise HTTPException(status_code=404, detail="Payslip not found")

        if not os.path.exists(payslip.file_path):
            raise HTTPException(status_code=404, detail="Payslip file not found on server")

        return FileResponse(
            path=payslip.file_path,
            filename=payslip.original_filename,
            media_type=payslip.content_type,
            headers={"Content-Disposition": f"attachment; filename={payslip.original_filename}"}
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to download payslip: {str(e)}")

@router.delete("/{candidate_id}/payslips/{payslip_id}")
async def delete_payslip(candidate_id: str, payslip_id: int, db: Session = Depends(get_db)):
    """💰 Delete a specific payslip"""
    try:
        payslip = db.query(Payslip).filter(
            Payslip.id == payslip_id,
            Payslip.candidate_id == candidate_id
        ).first()

        if not payslip:
            raise HTTPException(status_code=404, detail="Payslip not found")

        # Remove file from filesystem
        if os.path.exists(payslip.file_path):
            os.remove(payslip.file_path)

        # Remove from database
        db.delete(payslip)
        db.commit()

        logger.info(f"💰 Deleted payslip {payslip_id} for {candidate_id}")
        return {"message": f"Payslip for {payslip.month_year} deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to delete payslip: {str(e)}")

# ============================================================================
# 🛡️ BACKGROUND VERIFICATION ROUTES
# ============================================================================

@router.post("/{candidate_id}/background-verification", response_model=BackgroundVerificationResponse, status_code=status.HTTP_201_CREATED)
async def create_background_verification(candidate_id: str, verification_data: BackgroundVerificationCreate, db: Session = Depends(get_db)):
    """🛡️ Create or update background verification status"""
    try:
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        # Check if background verification already exists
        existing_verification = db.query(BackgroundVerification).filter(
            BackgroundVerification.candidate_id == candidate_id
        ).first()

        if existing_verification:
            # Update existing verification
            for key, value in verification_data.model_dump().items():
                setattr(existing_verification, key, value)
            from datetime import datetime, timezone
            existing_verification.updated_at = datetime.now(timezone.utc)
            db.commit()
            db.refresh(existing_verification)
            logger.info(f"🛡️ Updated background verification for {candidate_id}")
            return existing_verification
        else:
            # Create new verification
            verification = BackgroundVerification(
                candidate_id=candidate_id,
                **verification_data.model_dump()
            )
            db.add(verification)
            db.commit()
            db.refresh(verification)
            logger.info(f"🛡️ Created background verification for {candidate_id}")
            return verification

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to create background verification: {str(e)}")

@router.get("/{candidate_id}/background-verification")
async def get_background_verification(candidate_id: str, db: Session = Depends(get_db)):
    """🛡️ Get background verification status with detailed address proof information"""
    try:
        # Get candidate information
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        verification = db.query(BackgroundVerification).filter(
            BackgroundVerification.candidate_id == candidate_id
        ).first()

        if not verification:
            raise HTTPException(status_code=404, detail=f"No background verification found for candidate {candidate_id}")

        # Get address proofs with enhanced information
        address_proofs = db.query(AddressProof).filter(
            AddressProof.candidate_id == candidate_id
        ).all()

        # Enhanced response with candidate details and address proof analysis
        result = {
            "id": verification.id,
            "candidate_id": verification.candidate_id,
            "candidate_name": f"{candidate.first_name} {candidate.last_name}",
            "candidate_email": candidate.email,
            "status": verification.status,
            "verification_notes": verification.verification_notes,
            "employment_verified": verification.employment_verified,
            "education_verified": verification.education_verified,
            "address_verified": verification.address_verified,
            "created_at": verification.created_at,
            "updated_at": verification.updated_at,
            "address_proofs": [
                {
                    "id": proof.id,
                    "candidate_id": proof.candidate_id,  # Now available for easier analysis
                    "document_type": proof.document_type,
                    "filename": proof.original_filename,
                    "file_size_mb": round(proof.file_size / (1024 * 1024), 2),
                    "is_verified": proof.is_verified,
                    "uploaded_date": proof.created_at.strftime("%d/%m/%Y %H:%M"),
                    "file_exists": os.path.exists(proof.file_path)
                } for proof in address_proofs
            ],
            "address_proof_summary": {
                "total_documents": len(address_proofs),
                "verified_documents": sum(1 for p in address_proofs if p.is_verified),
                "document_types": list(set(p.document_type for p in address_proofs))
            }
        }

        return result

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get background verification: {str(e)}")

@router.post("/{candidate_id}/background-verification/address-proof")
async def upload_address_proof(
    candidate_id: str,
    document_type: str,
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """🛡️ Upload address proof document"""
    file_path = None
    try:
        # Check if candidate exists
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        # Validate document type and file
        validate_document_type(document_type)
        validate_address_proof_file(file)

        # Get or create background verification
        verification = db.query(BackgroundVerification).filter(
            BackgroundVerification.candidate_id == candidate_id
        ).first()

        if not verification:
            verification = BackgroundVerification(candidate_id=candidate_id)
            db.add(verification)
            db.flush()  # Get the ID for foreign key

        # Handle existing document of same type
        existing_proof = db.query(AddressProof).filter(
            AddressProof.background_verification_id == verification.id,
            AddressProof.document_type == document_type
        ).first()

        if existing_proof:
            # Remove old file from filesystem
            if os.path.exists(existing_proof.file_path):
                os.remove(existing_proof.file_path)
            # Delete old address proof record
            db.delete(existing_proof)
            db.flush()  # Ensure deletion is committed

        # Save new file to filesystem
        file_path, unique_filename = save_address_proof_file(file, candidate_id, document_type)

        # Create new address proof record in database
        address_proof = AddressProof(
            candidate_id=candidate_id,  # Direct candidate reference for easier analysis
            background_verification_id=verification.id,
            document_type=document_type,
            filename=unique_filename,
            original_filename=file.filename,
            file_path=file_path,
            file_size=file.size,
            content_type=file.content_type,
            is_verified=False
        )

        db.add(address_proof)
        db.commit()
        db.refresh(address_proof)

        logger.info(f"🛡️ Successfully uploaded {document_type} for {candidate_id}: {unique_filename}")
        return {
            "message": f"{document_type} uploaded successfully",
            "candidate_id": candidate_id,
            "document_type": document_type,
            "filename": unique_filename,
            "file_size_mb": round(file.size / (1024 * 1024), 2)
        }

    except HTTPException:
        # Clean up file if database operation failed
        if file_path and os.path.exists(file_path):
            os.remove(file_path)
        raise
    except Exception as e:
        # Clean up file if database operation failed
        if file_path and os.path.exists(file_path):
            os.remove(file_path)
        db.rollback()
        logger.error(f"❌ Failed to upload {document_type} for {candidate_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to upload address proof: {str(e)}")
