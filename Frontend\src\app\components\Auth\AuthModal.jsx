"use client";
import { useState, useEffect } from 'react';
import { X, Mail, Phone, Shield, CheckCircle, AlertCircle } from 'lucide-react';
import { authHelpers } from '../../../lib/supabase';

const AuthModal = ({ isOpen, onClose, onSuccess, purpose = "DigiLocker authentication" }) => {
  const [authMethod, setAuthMethod] = useState('email'); // 'email', 'phone', 'magic'
  const [step, setStep] = useState('method'); // 'method', 'credentials', 'otp', 'success'
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Form data
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [password, setPassword] = useState('');
  const [otp, setOtp] = useState('');
  const [isSignUp, setIsSignUp] = useState(false);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setStep('method');
      setError('');
      setSuccess('');
      setEmail('');
      setPhone('');
      setPassword('');
      setOtp('');
    }
  }, [isOpen]);

  const handleEmailAuth = async () => {
    setLoading(true);
    setError('');

    try {
      let result;
      if (isSignUp) {
        result = await authHelpers.signUpWithEmail(email, password, {
          purpose: purpose
        });
      } else {
        result = await authHelpers.signInWithEmail(email, password);
      }

      if (result.error) {
        setError(result.error.message);
      } else {
        setSuccess(isSignUp ? 'Account created! Please check your email to verify.' : 'Signed in successfully!');
        setStep('success');
        setTimeout(() => {
          onSuccess(result.data.user);
          onClose();
        }, 2000);
      }
    } catch (err) {
      setError('Authentication failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleMagicLink = async () => {
    setLoading(true);
    setError('');

    try {
      const result = await authHelpers.signInWithMagicLink(email);
      if (result.error) {
        setError(result.error.message);
      } else {
        setSuccess('Magic link sent! Please check your email.');
        setStep('success');
      }
    } catch (err) {
      setError('Failed to send magic link. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handlePhoneAuth = async () => {
    setLoading(true);
    setError('');

    try {
      const result = await authHelpers.signInWithPhone(phone);
      if (result.error) {
        setError(result.error.message);
      } else {
        setSuccess('OTP sent to your phone!');
        setStep('otp');
      }
    } catch (err) {
      setError('Failed to send OTP. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleOtpVerification = async () => {
    setLoading(true);
    setError('');

    try {
      const result = await authHelpers.verifyPhoneOtp(phone, otp);
      if (result.error) {
        setError(result.error.message);
      } else {
        setSuccess('Phone verified successfully!');
        setStep('success');
        setTimeout(() => {
          onSuccess(result.data.user);
          onClose();
        }, 2000);
      }
    } catch (err) {
      setError('Invalid OTP. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-2">
            <Shield className="w-6 h-6 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-900">Authenticate</h2>
          </div>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X className="w-6 h-6" />
          </button>
        </div>

        <p className="text-sm text-gray-600 mb-6">
          Please authenticate to continue with {purpose}
        </p>

        {/* Error/Success Messages */}
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-center space-x-2">
            <AlertCircle className="w-4 h-4 text-red-600" />
            <span className="text-sm text-red-700">{error}</span>
          </div>
        )}

        {success && (
          <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md flex items-center space-x-2">
            <CheckCircle className="w-4 h-4 text-green-600" />
            <span className="text-sm text-green-700">{success}</span>
          </div>
        )}

        {/* Method Selection */}
        {step === 'method' && (
          <div className="space-y-4">
            <div className="grid grid-cols-3 gap-2">
              <button
                onClick={() => { setAuthMethod('email'); setStep('credentials'); }}
                className={`p-3 border rounded-lg text-center ${
                  authMethod === 'email' ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                }`}
              >
                <Mail className="w-6 h-6 mx-auto mb-1 text-blue-600" />
                <span className="text-xs">Email</span>
              </button>
              <button
                onClick={() => { setAuthMethod('phone'); setStep('credentials'); }}
                className={`p-3 border rounded-lg text-center ${
                  authMethod === 'phone' ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                }`}
              >
                <Phone className="w-6 h-6 mx-auto mb-1 text-blue-600" />
                <span className="text-xs">Phone</span>
              </button>
              <button
                onClick={() => { setAuthMethod('magic'); setStep('credentials'); }}
                className={`p-3 border rounded-lg text-center ${
                  authMethod === 'magic' ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                }`}
              >
                <Shield className="w-6 h-6 mx-auto mb-1 text-blue-600" />
                <span className="text-xs">Magic Link</span>
              </button>
            </div>
          </div>
        )}

        {/* Credentials Input */}
        {step === 'credentials' && (
          <div className="space-y-4">
            {authMethod === 'email' && (
              <>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter your email"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Password</label>
                  <input
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter your password"
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="signup"
                    checked={isSignUp}
                    onChange={(e) => setIsSignUp(e.target.checked)}
                    className="rounded"
                  />
                  <label htmlFor="signup" className="text-sm text-gray-600">
                    Create new account
                  </label>
                </div>
                <button
                  onClick={handleEmailAuth}
                  disabled={loading || !email || !password}
                  className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  {loading ? 'Authenticating...' : (isSignUp ? 'Sign Up' : 'Sign In')}
                </button>
              </>
            )}

            {authMethod === 'magic' && (
              <>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter your email"
                  />
                </div>
                <button
                  onClick={handleMagicLink}
                  disabled={loading || !email}
                  className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  {loading ? 'Sending...' : 'Send Magic Link'}
                </button>
              </>
            )}

            {authMethod === 'phone' && (
              <>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                  <input
                    type="tel"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="+1234567890"
                  />
                </div>
                <button
                  onClick={handlePhoneAuth}
                  disabled={loading || !phone}
                  className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  {loading ? 'Sending...' : 'Send OTP'}
                </button>
              </>
            )}

            <button
              onClick={() => setStep('method')}
              className="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
            >
              Back
            </button>
          </div>
        )}

        {/* OTP Verification */}
        {step === 'otp' && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Enter OTP sent to {phone}
              </label>
              <input
                type="text"
                value={otp}
                onChange={(e) => setOtp(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="123456"
                maxLength={6}
              />
            </div>
            <button
              onClick={handleOtpVerification}
              disabled={loading || !otp}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? 'Verifying...' : 'Verify OTP'}
            </button>
            <button
              onClick={() => setStep('credentials')}
              className="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
            >
              Back
            </button>
          </div>
        )}

        {/* Success */}
        {step === 'success' && (
          <div className="text-center py-8">
            <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Authentication Successful!</h3>
            <p className="text-gray-600">Redirecting to DigiLocker...</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default AuthModal;
