"""
Interview feedback routes
"""

import logging
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.database.connection import get_db
from app.models.feedback import InterviewFeedback, InterviewQuestion
from app.models.candidate import Candidate
from app.schemas.feedback import InterviewFeedbackResponse

logger = logging.getLogger(__name__)
router = APIRouter()

# ============================================================================
# 🤖 INTERVIEW FEEDBACK ROUTES
# ============================================================================

@router.get("/{candidate_id}", response_model=InterviewFeedbackResponse)
async def get_interview_feedback(candidate_id: str, db: Session = Depends(get_db)):
    """🤖 Get interview feedback for a candidate"""
    try:
        # Check if candidate exists
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        # Get feedback with questions
        feedback = db.query(InterviewFeedback).filter(
            InterviewFeedback.candidate_id == candidate_id
        ).first()

        if not feedback:
            raise HTTPException(status_code=404, detail=f"No interview feedback found for candidate {candidate_id}")

        # Get all questions for this feedback
        questions = db.query(InterviewQuestion).filter(
            InterviewQuestion.feedback_id == feedback.id
        ).all()

        # Format questions for response
        questions_data = [
            {
                "id": q.id,
                "question": q.question,
                "answer": q.answer,
                "score": q.score,
                "max_score": q.max_score,
                "question_feedback": q.question_feedback,
                "created_at": q.created_at
            } for q in questions
        ]

        return InterviewFeedbackResponse(
            candidate_id=feedback.candidate_id,
            overall_score=feedback.overall_score,
            max_score=feedback.max_score,
            general_comments=feedback.general_comments,
            questions=questions_data,
            created_at=feedback.created_at,
            updated_at=feedback.updated_at
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get interview feedback: {str(e)}")

@router.post("/{candidate_id}")
async def create_interview_feedback(
    candidate_id: str,
    feedback_data: dict,
    db: Session = Depends(get_db)
):
    """🤖 Create or update interview feedback for a candidate"""
    try:
        # Check if candidate exists
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        # Check if feedback already exists
        existing_feedback = db.query(InterviewFeedback).filter(
            InterviewFeedback.candidate_id == candidate_id
        ).first()

        if existing_feedback:
            # Delete existing questions
            db.query(InterviewQuestion).filter(
                InterviewQuestion.feedback_id == existing_feedback.id
            ).delete()
            
            # Update existing feedback
            existing_feedback.overall_score = feedback_data.get("overall_score", 0)
            existing_feedback.general_comments = feedback_data.get("general_comments", "")
            from datetime import datetime, timezone
            existing_feedback.updated_at = datetime.now(timezone.utc)
            
            feedback = existing_feedback
        else:
            # Create new feedback
            feedback = InterviewFeedback(
                candidate_id=candidate_id,
                overall_score=feedback_data.get("overall_score", 0),
                general_comments=feedback_data.get("general_comments", "")
            )
            db.add(feedback)
            db.flush()  # Get the ID

        # Add questions
        questions_data = feedback_data.get("questions", [])
        for q_data in questions_data:
            question = InterviewQuestion(
                feedback_id=feedback.id,
                question=q_data.get("question", ""),
                answer=q_data.get("answer", ""),
                score=q_data.get("score", 0),
                max_score=q_data.get("max_score", 100),
                question_feedback=q_data.get("question_feedback", "")
            )
            db.add(question)

        db.commit()
        db.refresh(feedback)

        logger.info(f"🤖 Successfully created/updated interview feedback for {candidate_id}")
        return {
            "message": "Interview feedback saved successfully",
            "candidate_id": candidate_id,
            "overall_score": feedback.overall_score,
            "total_questions": len(questions_data)
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"❌ Failed to create interview feedback for {candidate_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create interview feedback: {str(e)}")

@router.delete("/{candidate_id}")
async def delete_interview_feedback(candidate_id: str, db: Session = Depends(get_db)):
    """🤖 Delete interview feedback for a candidate"""
    try:
        # Check if candidate exists
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        # Get feedback
        feedback = db.query(InterviewFeedback).filter(
            InterviewFeedback.candidate_id == candidate_id
        ).first()

        if not feedback:
            raise HTTPException(status_code=404, detail=f"No interview feedback found for candidate {candidate_id}")

        # Delete questions first (cascade should handle this, but being explicit)
        db.query(InterviewQuestion).filter(
            InterviewQuestion.feedback_id == feedback.id
        ).delete()

        # Delete feedback
        db.delete(feedback)
        db.commit()

        logger.info(f"🤖 Deleted interview feedback for {candidate_id}")
        return {"message": f"Interview feedback deleted successfully for candidate {candidate_id}"}

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to delete interview feedback: {str(e)}")
