{"version": 3, "sources": ["../../../../src/client/components/router-reducer/create-initial-router-state.ts"], "names": ["createInitialRouterState", "buildId", "initialTree", "initialSeedData", "urlParts", "initialParallelRoutes", "location", "initialHead", "couldBeIntercepted", "initialCanonicalUrl", "join", "isServer", "rsc", "cache", "lazyData", "prefetchRsc", "head", "prefetchHead", "parallelRoutes", "Map", "lazyDataResolved", "loading", "canonicalUrl", "createHrefFromUrl", "addRefreshMarkerToActiveParallelSegments", "prefetchCache", "size", "fillLazyItemsTillLeafWithHead", "undefined", "extractPathFromFlightRouterState", "initialState", "tree", "pushRef", "pendingPush", "mpaNavigation", "preserveCustomHistoryState", "focusAndScrollRef", "apply", "onlyHashChange", "hashFragment", "segmentPaths", "nextUrl", "pathname", "url", "URL", "search", "origin", "initialFlightData", "createPrefetchCacheEntryForInitialLoad", "kind", "PrefetchKind", "AUTO", "data"], "mappings": ";;;;+BA0BgBA;;;eAAAA;;;mCAlBkB;+CACY;oCACG;oCACM;oCACD;iDACG;AAalD,SAASA,yBAAyB,KASV;IATU,IAAA,EACvCC,OAAO,EACPC,WAAW,EACXC,eAAe,EACfC,QAAQ,EACRC,qBAAqB,EACrBC,QAAQ,EACRC,WAAW,EACXC,kBAAkB,EACW,GATU;IAUvC,sFAAsF;IACtF,kGAAkG;IAClG,mCAAmC;IACnC,MAAMC,sBAAsBL,SAASM,IAAI,CAAC;IAC1C,MAAMC,WAAW,CAACL;IAClB,MAAMM,MAAMT,eAAe,CAAC,EAAE;IAE9B,MAAMU,QAAmB;QACvBC,UAAU;QACVF,KAAKA;QACLG,aAAa;QACbC,MAAM;QACNC,cAAc;QACd,oJAAoJ;QACpJC,gBAAgBP,WAAW,IAAIQ,QAAQd;QACvCe,kBAAkB;QAClBC,SAASlB,eAAe,CAAC,EAAE;IAC7B;IAEA,MAAMmB,eACJ,6EAA6E;IAC7E,kJAAkJ;IAClJhB,WAEIiB,IAAAA,oCAAiB,EAACjB,YAClBG;IAENe,IAAAA,yEAAwC,EAACtB,aAAaoB;IAEtD,MAAMG,gBAAgB,IAAIN;IAE1B,yEAAyE;IACzE,IAAId,0BAA0B,QAAQA,sBAAsBqB,IAAI,KAAK,GAAG;QACtEC,IAAAA,4DAA6B,EAC3Bd,OACAe,WACA1B,aACAC,iBACAI;IAEJ;QAsBI,sEAAsE;IACrEsB;IArBL,MAAMC,eAAe;QACnB7B;QACA8B,MAAM7B;QACNW;QACAY;QACAO,SAAS;YACPC,aAAa;YACbC,eAAe;YACf,mEAAmE;YACnE,gFAAgF;YAChFC,4BAA4B;QAC9B;QACAC,mBAAmB;YACjBC,OAAO;YACPC,gBAAgB;YAChBC,cAAc;YACdC,cAAc,EAAE;QAClB;QACAlB;QACAmB,SAEE,CAACZ,OAAAA,IAAAA,oDAAgC,EAAC3B,iBAAgBI,4BAAAA,SAAUoC,QAAQ,aAAnEb,OACD;IACJ;IAEA,IAAIvB,UAAU;QACZ,iDAAiD;QACjD,gFAAgF;QAChF,+FAA+F;QAC/F,MAAMqC,MAAM,IAAIC,IACd,AAAC,KAAEtC,SAASoC,QAAQ,GAAGpC,SAASuC,MAAM,EACtCvC,SAASwC,MAAM;QAGjB,MAAMC,oBAAgC;YAAC;gBAAC;gBAAI7C;gBAAa;gBAAM;aAAK;SAAC;QACrE8C,IAAAA,0DAAsC,EAAC;YACrCL;YACAM,MAAMC,gCAAY,CAACC,IAAI;YACvBC,MAAM;gBAACL;gBAAmBnB;gBAAW;gBAAOpB;aAAmB;YAC/DuB,MAAMD,aAAaC,IAAI;YACvBN,eAAeK,aAAaL,aAAa;YACzCgB,SAASX,aAAaW,OAAO;QAC/B;IACF;IAEA,OAAOX;AACT"}