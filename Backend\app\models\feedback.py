"""
Interview feedback database models
"""

from datetime import datetime, timezone
from sqlalchemy import <PERSON>umn, Integer, String, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship

from app.database.connection import Base

class InterviewFeedback(Base):
    __tablename__ = "interview_feedback"
    id = Column(Integer, primary_key=True, index=True)
    candidate_id = Column(String, ForeignKey("candidates.candidate_id"), nullable=False, unique=True)
    overall_score = Column(Integer, nullable=False)
    max_score = Column(Integer, default=100)
    general_comments = Column(Text, nullable=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    candidate = relationship("Candidate")
    questions = relationship("InterviewQuestion", back_populates="feedback", cascade="all, delete-orphan")

class InterviewQuestion(Base):
    __tablename__ = "interview_questions"
    id = Column(Integer, primary_key=True, index=True)
    feedback_id = Column(Integer, ForeignKey("interview_feedback.id"), nullable=False)
    question = Column(Text, nullable=False)
    answer = Column(Text, nullable=True)
    score = Column(Integer, nullable=False)
    max_score = Column(Integer, default=100)
    question_feedback = Column(Text, nullable=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    feedback = relationship("InterviewFeedback", back_populates="questions")
