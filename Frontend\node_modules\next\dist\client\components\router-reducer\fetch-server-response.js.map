{"version": 3, "sources": ["../../../../src/client/components/router-reducer/fetch-server-response.ts"], "names": ["fetchServerResponse", "createFromFetch", "process", "env", "NEXT_RUNTIME", "require", "doMpaNavigation", "url", "urlToUrlWithoutFlightMarker", "toString", "undefined", "flightRouterState", "nextUrl", "currentBuildId", "prefetchKind", "headers", "RSC_HEADER", "NEXT_ROUTER_STATE_TREE", "encodeURIComponent", "JSON", "stringify", "PrefetchKind", "AUTO", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_URL", "NEXT_DEPLOYMENT_ID", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hexHash", "join", "res", "fetchUrl", "URL", "NODE_ENV", "__NEXT_CONFIG_OUTPUT", "pathname", "endsWith", "searchParams", "set", "NEXT_RSC_UNION_QUERY", "fetch", "credentials", "responseUrl", "canonicalUrl", "redirected", "contentType", "get", "postponed", "NEXT_DID_POSTPONE_HEADER", "interception", "includes", "isFlightResponse", "RSC_CONTENT_TYPE_HEADER", "startsWith", "ok", "hash", "buildId", "flightData", "Promise", "resolve", "callServer", "err", "console", "error"], "mappings": "AAAA;;;;;+BA8<PERSON><PERSON>;;;eAAAA;;;kCApBf;2BACqC;+BACjB;oCACE;sBACL;AA5BxB,aAAa;AACb,6DAA6D;AAC7D,oEAAoE;AACpE,MAAM,EAAEC,eAAe,EAAE,GACvB,CAAC,CAACC,QAAQC,GAAG,CAACC,YAAY,GAEtBC,QAAQ,0CAERA,QAAQ;AA6Bd,SAASC,gBAAgBC,GAAW;IAClC,OAAO;QAACC,IAAAA,sCAA2B,EAACD,KAAKE,QAAQ;QAAIC;QAAW;QAAO;KAAM;AAC/E;AAKO,eAAeV,oBACpBO,GAAQ,EACRI,iBAAoC,EACpCC,OAAsB,EACtBC,cAAsB,EACtBC,YAA2B;IAE3B,MAAMC,UAMF;QACF,yBAAyB;QACzB,CAACC,4BAAU,CAAC,EAAE;QACd,mCAAmC;QACnC,CAACC,wCAAsB,CAAC,EAAEC,mBACxBC,KAAKC,SAAS,CAACT;IAEnB;IAEA;;;;;GAKC,GACD,IAAIG,iBAAiBO,gCAAY,CAACC,IAAI,EAAE;QACtCP,OAAO,CAACQ,6CAA2B,CAAC,GAAG;IACzC;IAEA,IAAIX,SAAS;QACXG,OAAO,CAACS,0BAAQ,CAAC,GAAGZ;IACtB;IAEA,IAAIV,QAAQC,GAAG,CAACsB,kBAAkB,EAAE;QAClCV,OAAO,CAAC,kBAAkB,GAAGb,QAAQC,GAAG,CAACsB,kBAAkB;IAC7D;IAEA,MAAMC,mBAAmBC,IAAAA,aAAO,EAC9B;QACEZ,OAAO,CAACQ,6CAA2B,CAAC,IAAI;QACxCR,OAAO,CAACE,wCAAsB,CAAC;QAC/BF,OAAO,CAACS,0BAAQ,CAAC;KAClB,CAACI,IAAI,CAAC;IAGT,IAAI;YA0BqBC;QAzBvB,IAAIC,WAAW,IAAIC,IAAIxB;QACvB,IAAIL,QAAQC,GAAG,CAAC6B,QAAQ,KAAK,cAAc;YACzC,IAAI9B,QAAQC,GAAG,CAAC8B,oBAAoB,KAAK,UAAU;gBACjD,IAAIH,SAASI,QAAQ,CAACC,QAAQ,CAAC,MAAM;oBACnCL,SAASI,QAAQ,IAAI;gBACvB,OAAO;oBACLJ,SAASI,QAAQ,IAAI;gBACvB;YACF;QACF;QAEA,8FAA8F;QAC9FJ,SAASM,YAAY,CAACC,GAAG,CAACC,sCAAoB,EAAEZ;QAEhD,MAAMG,MAAM,MAAMU,MAAMT,UAAU;YAChC,wFAAwF;YACxFU,aAAa;YACbzB;QACF;QAEA,MAAM0B,cAAcjC,IAAAA,sCAA2B,EAACqB,IAAItB,GAAG;QACvD,MAAMmC,eAAeb,IAAIc,UAAU,GAAGF,cAAc/B;QAEpD,MAAMkC,cAAcf,IAAId,OAAO,CAAC8B,GAAG,CAAC,mBAAmB;QACvD,MAAMC,YAAY,CAAC,CAACjB,IAAId,OAAO,CAAC8B,GAAG,CAACE,0CAAwB;QAC5D,MAAMC,eAAe,CAAC,GAACnB,mBAAAA,IAAId,OAAO,CAAC8B,GAAG,CAAC,4BAAhBhB,iBAAyBoB,QAAQ,CAACzB,0BAAQ;QACjE,IAAI0B,mBAAmBN,gBAAgBO,yCAAuB;QAE9D,IAAIjD,QAAQC,GAAG,CAAC6B,QAAQ,KAAK,cAAc;YACzC,IAAI9B,QAAQC,GAAG,CAAC8B,oBAAoB,KAAK,UAAU;gBACjD,IAAI,CAACiB,kBAAkB;oBACrBA,mBAAmBN,YAAYQ,UAAU,CAAC;gBAC5C;YACF;QACF;QAEA,4FAA4F;QAC5F,oEAAoE;QACpE,IAAI,CAACF,oBAAoB,CAACrB,IAAIwB,EAAE,EAAE;YAChC,2FAA2F;YAC3F,IAAI9C,IAAI+C,IAAI,EAAE;gBACZb,YAAYa,IAAI,GAAG/C,IAAI+C,IAAI;YAC7B;YAEA,OAAOhD,gBAAgBmC,YAAYhC,QAAQ;QAC7C;QAEA,2EAA2E;QAC3E,MAAM,CAAC8C,SAASC,WAAW,GAAuB,MAAMvD,gBACtDwD,QAAQC,OAAO,CAAC7B,MAChB;YACE8B,YAAAA,yBAAU;QACZ;QAGF,IAAI9C,mBAAmB0C,SAAS;YAC9B,OAAOjD,gBAAgBuB,IAAItB,GAAG;QAChC;QAEA,OAAO;YAACiD;YAAYd;YAAcI;YAAWE;SAAa;IAC5D,EAAE,OAAOY,KAAK;QACZC,QAAQC,KAAK,CACX,AAAC,qCAAkCvD,MAAI,yCACvCqD;QAEF,iDAAiD;QACjD,qHAAqH;QACrH,iGAAiG;QACjG,OAAO;YAACrD,IAAIE,QAAQ;YAAIC;YAAW;YAAO;SAAM;IAClD;AACF"}