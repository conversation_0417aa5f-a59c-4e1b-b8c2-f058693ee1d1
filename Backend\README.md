# FastJob Backend - Modular Architecture

## 📁 Folder Structure

```
Backend/
├── app/
│   ├── __init__.py
│   ├── main.py                    # FastAPI application entry point
│   ├── database/
│   │   ├── __init__.py
│   │   └── connection.py          # Database connection and setup
│   ├── models/
│   │   ├── __init__.py
│   │   ├── candidate.py           # Candidate and related models
│   │   └── feedback.py            # Interview feedback models
│   ├── routes/
│   │   ├── __init__.py
│   │   ├── candidates.py          # Candidate management routes
│   │   └── feedback.py            # Interview feedback routes
│   ├── schemas/
│   │   ├── __init__.py
│   │   ├── candidate.py           # Candidate Pydantic schemas
│   │   └── feedback.py            # Feedback Pydantic schemas
│   └── utils/
│       ├── __init__.py
│       ├── enums.py               # Enums and constants
│       ├── file_utils.py          # File handling utilities
│       └── validators.py          # Validation functions
├── uploads/                       # File upload directory
├── requirements.txt               # Python dependencies
├── run.py                        # Application runner script
└── README.md                     # This file
```

## 🚀 Getting Started

### Prerequisites
- Python 3.9+
- PostgreSQL (running on port 5433)
- Virtual environment (recommended)

### Installation

1. **Create and activate virtual environment:**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables:**
   Create a `.env` file in the Backend directory:
   ```env
   SUPABASE_URL=your_supabase_url
   SUPABASE_ANON_KEY=your_supabase_anon_key
   DIGILOCKER_CLIENT_ID=your_digilocker_client_id
   DIGILOCKER_CLIENT_SECRET=your_digilocker_client_secret
   DIGILOCKER_REDIRECT_URI=your_redirect_uri
   DIGILOCKER_AUTH_URL=https://api.digitallocker.gov.in/public/oauth2/1/authorize
   DIGILOCKER_TOKEN_URL=https://api.digitallocker.gov.in/public/oauth2/1/token
   DIGILOCKER_BASE_URL=https://api.digitallocker.gov.in
   ```

4. **Run the application:**
   ```bash
   python run.py
   ```
   
   Or using uvicorn directly:
   ```bash
   uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
   ```

## 📋 API Endpoints

### Candidates
- `POST /candidates` - Create a new candidate
- `GET /candidates` - Get all candidates
- `GET /candidates/{candidate_id}` - Get specific candidate

### Resume Management
- `POST /candidates/{candidate_id}/resume` - Upload resume
- `GET /candidates/{candidate_id}/resume/view` - View resume
- `GET /candidates/{candidate_id}/resume/download` - Download resume
- `PUT /candidates/{candidate_id}/resume/toggle-visibility` - Toggle resume visibility

### Notice Period
- `POST /candidates/{candidate_id}/notice-period` - Create/update notice period
- `GET /candidates/{candidate_id}/notice-period` - Get notice period

### Payslips
- `POST /candidates/{candidate_id}/payslips` - Upload payslip
- `GET /candidates/{candidate_id}/payslips` - Get all payslips
- `GET /candidates/{candidate_id}/payslips/summary` - Get payslips summary
- `GET /candidates/{candidate_id}/payslips/{payslip_id}/download` - Download payslip
- `DELETE /candidates/{candidate_id}/payslips/{payslip_id}` - Delete payslip

### Background Verification
- `POST /candidates/{candidate_id}/background-verification` - Create/update verification
- `GET /candidates/{candidate_id}/background-verification` - Get verification status
- `POST /candidates/{candidate_id}/background-verification/address-proof` - Upload address proof

### Interview Feedback
- `GET /feedback/{candidate_id}` - Get interview feedback
- `POST /feedback/{candidate_id}` - Create/update interview feedback
- `DELETE /feedback/{candidate_id}` - Delete interview feedback

## 🏗️ Architecture Benefits

### Modular Design
- **Separation of Concerns**: Each module has a specific responsibility
- **Maintainability**: Easy to locate and modify specific functionality
- **Scalability**: Easy to add new features without affecting existing code
- **Testing**: Each module can be tested independently

### File Organization
- **Models**: Database models are separated by domain (candidate, feedback)
- **Routes**: API endpoints are grouped by functionality
- **Schemas**: Pydantic models for request/response validation
- **Utils**: Reusable utility functions and constants

### Database Management
- **Connection Management**: Centralized database configuration
- **Model Registration**: Automatic model discovery and registration
- **Migration Support**: Ready for Alembic database migrations

## 🔧 Development

### Adding New Features
1. **Models**: Add new database models in `app/models/`
2. **Schemas**: Define Pydantic schemas in `app/schemas/`
3. **Routes**: Create API endpoints in `app/routes/`
4. **Utils**: Add utility functions in `app/utils/`

### Code Style
- Follow PEP 8 guidelines
- Use type hints where possible
- Add docstrings to functions and classes
- Use meaningful variable and function names

## 📊 Database

The application uses PostgreSQL with the following main tables:
- `candidates` - Candidate information
- `resumes` - Resume files and metadata
- `payslips` - Payslip documents
- `background_verification` - Verification status
- `address_proofs` - Address proof documents
- `interview_feedback` - Interview feedback and scores
- `interview_questions` - Individual interview questions and answers

## 🔐 Security

- File upload validation (type, size)
- SQL injection prevention through ORM
- Input validation using Pydantic
- CORS middleware for cross-origin requests

## 📝 Logging

The application includes comprehensive logging:
- Database operations
- File uploads/downloads
- Error tracking
- API request/response logging
