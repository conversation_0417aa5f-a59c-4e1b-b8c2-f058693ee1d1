"""
Employment Documents Routes - Offer Letter, Contract Agreement, Salary Negotiation Terms
"""

import os
import logging
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session

from app.database.connection import get_db
from app.models.candidate import (
    Candidate, OfferLetter, ContractAgreement, SalaryNegotiationTerms
)
from app.schemas.candidate import (
    OfferLetterResponse, ContractAgreementResponse, SalaryNegotiationTermsResponse
)
from app.utils.file_utils import save_uploaded_file, validate_resume_file

logger = logging.getLogger(__name__)
router = APIRouter()

# ============================================================================
# 📄 OFFER LETTER ROUTES
# ============================================================================

@router.post("/{candidate_id}/offer-letter", response_model=OfferLetterResponse, status_code=status.HTTP_201_CREATED)
async def upload_offer_letter(
    candidate_id: str,
    company_name: str = Form(...),
    job_position: str = Form(...),
    file: UploadFile = File(...),
    notes: Optional[str] = Form(None),
    db: Session = Depends(get_db)
):
    """📄 Upload offer letter document"""
    file_path = None
    try:
        # Check if candidate exists
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        # Validate file
        validate_resume_file(file)  # Reuse existing validation

        # Save file
        upload_dir = f"uploads/employment_documents/offer_letters/{candidate_id}"
        os.makedirs(upload_dir, exist_ok=True)
        
        unique_filename = f"offer_letter_{company_name.replace(' ', '_')}_{file.filename}"
        file_path = await save_uploaded_file(file, upload_dir, unique_filename)

        # Create offer letter record
        offer_letter = OfferLetter(
            candidate_id=candidate_id,
            company_name=company_name,
            job_position=job_position,
            filename=unique_filename,
            original_filename=file.filename,
            file_path=file_path,
            file_size=file.size,
            content_type=file.content_type,
            upload_status="uploaded",
            notes=notes
        )

        db.add(offer_letter)
        db.commit()
        db.refresh(offer_letter)

        logger.info(f"📄 Successfully uploaded offer letter for {candidate_id}: {unique_filename}")
        return offer_letter

    except HTTPException:
        if file_path and os.path.exists(file_path):
            os.remove(file_path)
        raise
    except Exception as e:
        if file_path and os.path.exists(file_path):
            os.remove(file_path)
        logger.error(f"❌ Error uploading offer letter for {candidate_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to upload offer letter: {str(e)}")

@router.get("/{candidate_id}/offer-letter", response_model=List[OfferLetterResponse])
async def get_offer_letters(candidate_id: str, db: Session = Depends(get_db)):
    """📄 Get all offer letters for a candidate"""
    try:
        # Check if candidate exists
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        offer_letters = db.query(OfferLetter).filter(OfferLetter.candidate_id == candidate_id).all()
        logger.info(f"📄 Retrieved {len(offer_letters)} offer letters for {candidate_id}")
        return offer_letters

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Error fetching offer letters for {candidate_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch offer letters: {str(e)}")

# ============================================================================
# 📋 CONTRACT AGREEMENT ROUTES
# ============================================================================

@router.post("/{candidate_id}/contract-agreement", response_model=ContractAgreementResponse, status_code=status.HTTP_201_CREATED)
async def upload_contract_agreement(
    candidate_id: str,
    company_name: str = Form(...),
    job_position: str = Form(...),
    file: UploadFile = File(...),
    notes: Optional[str] = Form(None),
    db: Session = Depends(get_db)
):
    """📋 Upload contract agreement document"""
    file_path = None
    try:
        # Check if candidate exists
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        # Validate file
        validate_resume_file(file)  # Reuse existing validation

        # Save file
        upload_dir = f"uploads/employment_documents/contract_agreements/{candidate_id}"
        os.makedirs(upload_dir, exist_ok=True)
        
        unique_filename = f"contract_{company_name.replace(' ', '_')}_{file.filename}"
        file_path = await save_uploaded_file(file, upload_dir, unique_filename)

        # Create contract agreement record
        contract = ContractAgreement(
            candidate_id=candidate_id,
            company_name=company_name,
            job_position=job_position,
            filename=unique_filename,
            original_filename=file.filename,
            file_path=file_path,
            file_size=file.size,
            content_type=file.content_type,
            upload_status="uploaded",
            notes=notes
        )

        db.add(contract)
        db.commit()
        db.refresh(contract)

        logger.info(f"📋 Successfully uploaded contract agreement for {candidate_id}: {unique_filename}")
        return contract

    except HTTPException:
        if file_path and os.path.exists(file_path):
            os.remove(file_path)
        raise
    except Exception as e:
        if file_path and os.path.exists(file_path):
            os.remove(file_path)
        logger.error(f"❌ Error uploading contract agreement for {candidate_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to upload contract agreement: {str(e)}")

@router.get("/{candidate_id}/contract-agreement", response_model=List[ContractAgreementResponse])
async def get_contract_agreements(candidate_id: str, db: Session = Depends(get_db)):
    """📋 Get all contract agreements for a candidate"""
    try:
        # Check if candidate exists
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        contracts = db.query(ContractAgreement).filter(ContractAgreement.candidate_id == candidate_id).all()
        logger.info(f"📋 Retrieved {len(contracts)} contract agreements for {candidate_id}")
        return contracts

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Error fetching contract agreements for {candidate_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch contract agreements: {str(e)}")

# ============================================================================
# 💰 SALARY NEGOTIATION TERMS ROUTES
# ============================================================================

@router.post("/{candidate_id}/salary-negotiation-terms", response_model=SalaryNegotiationTermsResponse, status_code=status.HTTP_201_CREATED)
async def upload_salary_negotiation_terms(
    candidate_id: str,
    company_name: str = Form(...),
    job_position: str = Form(...),
    file: UploadFile = File(...),
    notes: Optional[str] = Form(None),
    db: Session = Depends(get_db)
):
    """💰 Upload salary negotiation terms document"""
    file_path = None
    try:
        # Check if candidate exists
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        # Validate file
        validate_resume_file(file)  # Reuse existing validation

        # Save file
        upload_dir = f"uploads/employment_documents/salary_terms/{candidate_id}"
        os.makedirs(upload_dir, exist_ok=True)
        
        unique_filename = f"salary_terms_{company_name.replace(' ', '_')}_{file.filename}"
        file_path = await save_uploaded_file(file, upload_dir, unique_filename)

        # Create salary negotiation terms record
        salary_terms = SalaryNegotiationTerms(
            candidate_id=candidate_id,
            company_name=company_name,
            job_position=job_position,
            filename=unique_filename,
            original_filename=file.filename,
            file_path=file_path,
            file_size=file.size,
            content_type=file.content_type,
            upload_status="uploaded",
            notes=notes
        )

        db.add(salary_terms)
        db.commit()
        db.refresh(salary_terms)

        logger.info(f"💰 Successfully uploaded salary negotiation terms for {candidate_id}: {unique_filename}")
        return salary_terms

    except HTTPException:
        if file_path and os.path.exists(file_path):
            os.remove(file_path)
        raise
    except Exception as e:
        if file_path and os.path.exists(file_path):
            os.remove(file_path)
        logger.error(f"❌ Error uploading salary negotiation terms for {candidate_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to upload salary negotiation terms: {str(e)}")

@router.get("/{candidate_id}/salary-negotiation-terms", response_model=List[SalaryNegotiationTermsResponse])
async def get_salary_negotiation_terms(candidate_id: str, db: Session = Depends(get_db)):
    """💰 Get all salary negotiation terms for a candidate"""
    try:
        # Check if candidate exists
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        salary_terms = db.query(SalaryNegotiationTerms).filter(SalaryNegotiationTerms.candidate_id == candidate_id).all()
        logger.info(f"💰 Retrieved {len(salary_terms)} salary negotiation terms for {candidate_id}")
        return salary_terms

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Error fetching salary negotiation terms for {candidate_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch salary negotiation terms: {str(e)}")

# ============================================================================
# 📥 DOWNLOAD ROUTES
# ============================================================================

@router.get("/{candidate_id}/offer-letter/{document_id}/download")
async def download_offer_letter(candidate_id: str, document_id: int, db: Session = Depends(get_db)):
    """📥 Download offer letter document"""
    try:
        offer_letter = db.query(OfferLetter).filter(
            OfferLetter.id == document_id,
            OfferLetter.candidate_id == candidate_id
        ).first()

        if not offer_letter:
            raise HTTPException(status_code=404, detail="Offer letter not found")

        if not os.path.exists(offer_letter.file_path):
            raise HTTPException(status_code=404, detail="File not found on server")

        return FileResponse(
            path=offer_letter.file_path,
            filename=offer_letter.original_filename,
            media_type=offer_letter.content_type
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Error downloading offer letter {document_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to download offer letter: {str(e)}")

@router.get("/{candidate_id}/contract-agreement/{document_id}/download")
async def download_contract_agreement(candidate_id: str, document_id: int, db: Session = Depends(get_db)):
    """📥 Download contract agreement document"""
    try:
        contract = db.query(ContractAgreement).filter(
            ContractAgreement.id == document_id,
            ContractAgreement.candidate_id == candidate_id
        ).first()

        if not contract:
            raise HTTPException(status_code=404, detail="Contract agreement not found")

        if not os.path.exists(contract.file_path):
            raise HTTPException(status_code=404, detail="File not found on server")

        return FileResponse(
            path=contract.file_path,
            filename=contract.original_filename,
            media_type=contract.content_type
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Error downloading contract agreement {document_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to download contract agreement: {str(e)}")

@router.get("/{candidate_id}/salary-negotiation-terms/{document_id}/download")
async def download_salary_negotiation_terms(candidate_id: str, document_id: int, db: Session = Depends(get_db)):
    """📥 Download salary negotiation terms document"""
    try:
        salary_terms = db.query(SalaryNegotiationTerms).filter(
            SalaryNegotiationTerms.id == document_id,
            SalaryNegotiationTerms.candidate_id == candidate_id
        ).first()

        if not salary_terms:
            raise HTTPException(status_code=404, detail="Salary negotiation terms not found")

        if not os.path.exists(salary_terms.file_path):
            raise HTTPException(status_code=404, detail="File not found on server")

        return FileResponse(
            path=salary_terms.file_path,
            filename=salary_terms.original_filename,
            media_type=salary_terms.content_type
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Error downloading salary negotiation terms {document_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to download salary negotiation terms: {str(e)}")

# ============================================================================
# 🗑️ DELETE ROUTES
# ============================================================================

@router.delete("/{candidate_id}/offer-letter/{document_id}")
async def delete_offer_letter(candidate_id: str, document_id: int, db: Session = Depends(get_db)):
    """🗑️ Delete offer letter document"""
    try:
        offer_letter = db.query(OfferLetter).filter(
            OfferLetter.id == document_id,
            OfferLetter.candidate_id == candidate_id
        ).first()

        if not offer_letter:
            raise HTTPException(status_code=404, detail="Offer letter not found")

        # Delete file from filesystem
        if os.path.exists(offer_letter.file_path):
            os.remove(offer_letter.file_path)

        # Delete database record
        db.delete(offer_letter)
        db.commit()

        logger.info(f"🗑️ Successfully deleted offer letter {document_id} for {candidate_id}")
        return {"message": "Offer letter deleted successfully", "document_id": document_id}

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"❌ Error deleting offer letter {document_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete offer letter: {str(e)}")

@router.delete("/{candidate_id}/contract-agreement/{document_id}")
async def delete_contract_agreement(candidate_id: str, document_id: int, db: Session = Depends(get_db)):
    """🗑️ Delete contract agreement document"""
    try:
        contract = db.query(ContractAgreement).filter(
            ContractAgreement.id == document_id,
            ContractAgreement.candidate_id == candidate_id
        ).first()

        if not contract:
            raise HTTPException(status_code=404, detail="Contract agreement not found")

        # Delete file from filesystem
        if os.path.exists(contract.file_path):
            os.remove(contract.file_path)

        # Delete database record
        db.delete(contract)
        db.commit()

        logger.info(f"🗑️ Successfully deleted contract agreement {document_id} for {candidate_id}")
        return {"message": "Contract agreement deleted successfully", "document_id": document_id}

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"❌ Error deleting contract agreement {document_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete contract agreement: {str(e)}")

@router.delete("/{candidate_id}/salary-negotiation-terms/{document_id}")
async def delete_salary_negotiation_terms(candidate_id: str, document_id: int, db: Session = Depends(get_db)):
    """🗑️ Delete salary negotiation terms document"""
    try:
        salary_terms = db.query(SalaryNegotiationTerms).filter(
            SalaryNegotiationTerms.id == document_id,
            SalaryNegotiationTerms.candidate_id == candidate_id
        ).first()

        if not salary_terms:
            raise HTTPException(status_code=404, detail="Salary negotiation terms not found")

        # Delete file from filesystem
        if os.path.exists(salary_terms.file_path):
            os.remove(salary_terms.file_path)

        # Delete database record
        db.delete(salary_terms)
        db.commit()

        logger.info(f"🗑️ Successfully deleted salary negotiation terms {document_id} for {candidate_id}")
        return {"message": "Salary negotiation terms deleted successfully", "document_id": document_id}

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"❌ Error deleting salary negotiation terms {document_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete salary negotiation terms: {str(e)}")
