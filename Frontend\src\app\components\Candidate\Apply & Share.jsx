import { useState, useEffect } from 'react';
import {
  Calendar,
  Clock,
  Briefcase,
  ChevronDown,
  ChevronUp,
  AlertCircle,
  X,
  PlusCircle,
  FileText,
  Shield,
  DollarSign,
  Bell,
  Upload,
  Star,
  Phone,
  MapPin,
  Download
} from 'lucide-react';
import AuthModal from '../Auth/AuthModal';
import { authHelpers, realtimeHelpers } from '../../../lib/supabase';

// API Configuration
const API_BASE_URL = 'http://localhost:8000';
const CANDIDATE_ID = 'CAND001'; // This should be passed as prop or from context

// Type definitions for better maintainability (using JSDoc for clarity)
/**
 * @typedef {Object} Application
 * @property {number} id
 * @property {string} company
 * @property {string} position
 * @property {string} appliedDate
 * @property {string} deadline
 * @property {string} status
 * @property {string} description
 * @property {string} companyLogo
 * @property {string} companyInfo
 * @property {number} matchScore
 * @property {string[]} requiredSkills
 * @property {boolean} rejected
 * @property {Array} interviews
 * @property {Array<{skill: string, recommendation: string, handsOn: string}>} learningGaps
 */

/**
 * @typedef {Object} AvailabilitySlot
 * @property {string} day
 * @property {string[]} slots
 */

/**
 * @typedef {Object} CandidateProfile
 * @property {Object} resume
 * @property {Object} salary
 * @property {Object} backgroundVerification
 * @property {Object} interviewFeedback
 * @property {string} noticePeriod
 * @property {number} simulationInterviewScore
 * @property {Array} payslips
 * @property {Array} mockInterview
 */

const JobApplicationDashboard = () => {
  // State for all dashboard data - NO HARDCODED DATA
  const [applications, setApplications] = useState([]);
  const [loading, setLoading] = useState(true); // Start with loading true
  const [error, setError] = useState(null);
  const [availabilitySlots, setAvailabilitySlots] = useState([]);

  const [shortlistedAvailability, setShortlistedAvailability] = useState({});
  const [candidateProfile, setCandidateProfile] = useState({
    resume: { uploaded: false, visible: false, name: "", email: "", phone: "", summary: "", skills: [], experience: [], education: [], certifications: [] },
    salary: { currentCTC: "Not specified", expectedCTC: "Not specified" },
    backgroundVerification: { status: "Not Started", details: "No verification started", addressProof: null, providentFund: null, moonlightingDetails: null },
    interviewFeedback: {
      score: 0,
      comments: "No feedback available",
      mockInterview: [
        {
          question: "Explain the Virtual DOM in React.",
          answer: "The Virtual DOM is a lightweight copy of the actual DOM. React uses it to improve performance by minimizing direct DOM manipulations, comparing changes in the Virtual DOM, and updating only the necessary parts of the real DOM.",
          score: 90,
          feedback: "Clear explanation with good understanding of the concept."
        },
        {
          question: "How would you optimize a React application for performance?",
          answer: "I would use techniques like memoization with React.memo, code-splitting with dynamic imports, and optimizing re-renders by avoiding unnecessary state updates.",
          score: 80,
          feedback: "Good points, but could elaborate on lazy loading and bundle optimization."
        },
        {
          question: "Design a scalable system for a social media platform.",
          answer: "I’d use a microservices architecture with a load balancer, database sharding, and caching with Redis for scalability.",
          score: 75,
          feedback: "Solid high-level overview, but lacked details on specific database choices and API design."
        }
      ]
    },
    noticePeriod: "Not specified",
    payslips: [],
    simulationInterviewScore: 0
  });

  const [activeTab, setActiveTab] = useState('resume');
  const [expandedJobId, setExpandedJobId] = useState(null);
  const [newSlot, setNewSlot] = useState({ day: "Monday", time: "9:00 AM - 11:00 AM" });
  const [newJobSlot, setNewJobSlot] = useState({ day: "Monday", time: "9:00 AM - 11:00 AM" });
  const [isResumeOpen, setIsResumeOpen] = useState(false);
  const [payslipFiles, setPayslipFiles] = useState([]);
  const [addressProof, setAddressProof] = useState(null);
  const [providentFund, setProvidentFund] = useState("");
  const [moonlightingDetails, setMoonlightingDetails] = useState("");

  // Authentication states
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Document type selection state
  const [selectedDocumentType, setSelectedDocumentType] = useState("");

  // API Functions
  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log(`🔍 Attempting to fetch from: ${API_BASE_URL}/candidates/${CANDIDATE_ID}/dashboard`);

      const response = await fetch(`${API_BASE_URL}/candidates/${CANDIDATE_ID}/dashboard`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      console.log('✅ Dashboard data received:', data);

      // Update state with real data from API
      setApplications(data.applications || []);
      setAvailabilitySlots(data.availabilitySlots || []);

      // Update candidate profile with API data
      setCandidateProfile({
        resume: data.resume || { uploaded: false, visible: false, name: "", email: "", phone: "", summary: "", skills: [], experience: [], education: [], certifications: [] },
        salary: data.salary || { currentCTC: "Not specified", expectedCTC: "Not specified" },
        backgroundVerification: data.backgroundVerification || { status: "Not Started", details: "No verification started", addressProof: null, providentFund: null, moonlightingDetails: null },
        interviewFeedback: data.interviewFeedback || { score: 0, comments: "No feedback available", mockInterview: [] },
        noticePeriod: data.noticePeriod || "Not specified",
        payslips: data.payslips || [],
        simulationInterviewScore: data.interviewFeedback?.score || 0
      });

      // Also fetch detailed payslip information and availability slots
      await fetchPayslips();
      await fetchAvailabilitySlots();

      console.log('✅ Dashboard data loaded successfully');
    } catch (err) {
      console.error('❌ API call failed:', err);
      setError(`Backend connection failed: ${err.message}. Please ensure backend is running on port 8000.`);
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchDashboardData();
    checkDigiLockerStatus();
    checkAuthStatus();

    // Listen for auth changes
    const { data: { subscription } } = realtimeHelpers.onAuthStateChange((event, session) => {
      if (session?.user) {
        setCurrentUser(session.user);
        setIsAuthenticated(true);
      } else {
        setCurrentUser(null);
        setIsAuthenticated(false);
      }
    });

    return () => subscription?.unsubscribe();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const { user } = await authHelpers.getCurrentUser();
      if (user) {
        setCurrentUser(user);
        setIsAuthenticated(true);
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
    }
  };

  const checkDigiLockerStatus = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/candidates/${CANDIDATE_ID}/digilocker/auth-status`);
      if (response.ok) {
        const data = await response.json();
        setCandidateProfile(prev => ({
          ...prev,
          digilockerAuth: { status: data.auth_status }
        }));
      }
    } catch (error) {
      console.error('Error checking DigiLocker status:', error);
    }
  };

  const toggleExpandJob = (id) => {
    setExpandedJobId(prevId => prevId === id ? null : id);
  };

  const getDaysRemaining = (deadline) => {
    if (!deadline || typeof deadline !== 'string') return 0;
    try {
      const today = new Date();
      const deadlineDate = new Date(deadline);
      if (isNaN(deadlineDate.getTime())) return 0;
      const diffTime = deadlineDate - today;
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return diffDays > 0 ? diffDays : 0;
    } catch (error) {
      console.error('Error calculating days remaining:', error);
      return 0;
    }
  };

  const addAvailabilitySlot = async (jobId = null) => {
    const currentSlot = jobId ? newJobSlot : newSlot;

    if (!currentSlot.day || !currentSlot.time) return;

    // Check for duplicates in local state first
    if (jobId) {
      const existingSlots = shortlistedAvailability[jobId] || [];
      const isDuplicate = existingSlots.some(slot =>
        slot.day === currentSlot.day && slot.time === currentSlot.time
      );
      if (isDuplicate) {
        alert(`⚠️ This time slot already exists for this job. Please select a different time.`);
        return;
      }
    } else {
      const existingSlots = availabilitySlots.find(slot => slot.day === currentSlot.day);
      if (existingSlots && existingSlots.slots.includes(currentSlot.time)) {
        alert(`⚠️ This time slot already exists for ${currentSlot.day}. Please select a different time.`);
        return;
      }
    }

    try {
      // Convert day and time to the format expected by backend
      const [startTime, endTime] = currentSlot.time.split(' - ');

      // Calculate the correct date for the selected day
      const today = new Date();
      const dayNames = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
      const selectedDayIndex = dayNames.indexOf(currentSlot.day); // Monday=0, Tuesday=1, etc.

      // Get current day index (Monday=0, Tuesday=1, etc.)
      const currentDayIndex = (today.getDay() + 6) % 7; // Convert Sunday=0 to Monday=0 system

      // Calculate days to add to get to the selected day
      let daysToAdd = selectedDayIndex - currentDayIndex;
      if (daysToAdd <= 0) {
        daysToAdd += 7; // Move to next week if day has passed or is today
      }

      const targetDate = new Date(today.getTime() + daysToAdd * 24 * 60 * 60 * 1000);
      const interviewDate = targetDate.toLocaleDateString('en-GB'); // DD/MM/YYYY format

      console.log(`🔍 Date calculation: ${currentSlot.day} -> ${interviewDate} (${targetDate.toLocaleDateString('en-US', { weekday: 'long' })})`);

      let slotData = {
        slot_type: jobId ? "job_specific" : "general",
        slots: [{
          interview_date: interviewDate,
          start_time: startTime,
          end_time: endTime
        }]
      };

      // For job-specific slots, add company and position information
      if (jobId) {
        const application = applications.find(app => app.id === jobId);
        if (application) {
          slotData.company_name = application.company;
          slotData.job_position = application.position;
        } else {
          throw new Error('Job application not found');
        }
      }

      const endpoint = jobId
        ? `/candidates/${CANDIDATE_ID}/availability/job-specific`
        : `/candidates/${CANDIDATE_ID}/availability/general`;

      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(slotData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('❌ API Error:', response.status, errorData);
        throw new Error(errorData.detail || `HTTP ${response.status}: Failed to add availability slot`);
      }

      const result = await response.json();
      console.log('✅ Availability slot saved to database:', result);

      // Refresh availability data from database to ensure consistency
      await fetchAvailabilitySlots();

      if (jobId) {
        const application = applications.find(app => app.id === jobId);
        alert(`✅ Job-specific availability added for ${application?.position} at ${application?.company}!`);

        // Reset job-specific form
        setNewJobSlot({ day: "Monday", time: "9:00 AM - 11:00 AM" });
      } else {
        alert('✅ General availability slot added successfully!');

        // Reset general form
        setNewSlot({ day: "Monday", time: "9:00 AM - 11:00 AM" });
      }

    } catch (err) {
      console.error('Error adding availability slot:', err);

      if (jobId) {
        const application = applications.find(app => app.id === jobId);
        alert(`❌ Failed to add job-specific availability for ${application?.position} at ${application?.company}. ${err.message}`);
      } else {
        alert(`❌ Failed to add general availability slot. ${err.message}`);
      }
    }
  };

  const [deletingSlots, setDeletingSlots] = useState(new Set());
  const [addingSlot, setAddingSlot] = useState(false);

  const removeAvailabilitySlot = async (day, slotIndex, jobId = null, slotId = null) => {
    // Prevent duplicate delete requests
    if (slotId && deletingSlots.has(slotId)) {
      console.log(`⚠️ Already deleting slot ${slotId}, ignoring duplicate request`);
      return;
    }

    try {
      // Mark slot as being deleted
      if (slotId) {
        setDeletingSlots(prev => new Set([...prev, slotId]));
      }

      // If we have a slot ID, delete from database first
      if (slotId) {
        const response = await fetch(`${API_BASE_URL}/candidates/${CANDIDATE_ID}/availability/${slotId}`, {
          method: 'DELETE'
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.detail || 'Failed to delete availability slot from database');
        }

        console.log(`✅ Deleted slot ${slotId} from database`);

        // Refresh availability data from database to ensure consistency
        await fetchAvailabilitySlots();

        if (jobId) {
          alert('✅ Job-specific availability slot deleted successfully!');
        } else {
          alert('✅ General availability slot deleted successfully!');
        }
      } else {
        // If no slot ID, just update local state (fallback)
        if (jobId) {
          setShortlistedAvailability(prev => {
            if (!prev[jobId]) return prev;
            return {
              ...prev,
              [jobId]: prev[jobId].filter((_, idx) => idx !== slotIndex)
            };
          });
          alert('✅ Job-specific availability slot removed from display!');
        } else {
          setAvailabilitySlots(prevSlots => {
            const updatedSlots = prevSlots.map(slot => {
              if (slot.day === day) {
                const updatedTimeSlots = slot.slots.filter((_, idx) => idx !== slotIndex);
                return { ...slot, slots: updatedTimeSlots };
              }
              return slot;
            });
            const filteredSlots = updatedSlots.filter(slot => slot.slots.length > 0);
            return filteredSlots;
          });
          alert('✅ General availability slot removed from display!');
        }
      }

    } catch (err) {
      console.error('Error removing availability slot:', err);
      alert(`❌ Failed to remove availability slot: ${err.message}`);
    } finally {
      // Remove from deleting set
      if (slotId) {
        setDeletingSlots(prev => {
          const newSet = new Set(prev);
          newSet.delete(slotId);
          return newSet;
        });
      }
    }
  };

  const getStatusBadgeColor = (status) => {
    switch (status) {
      case "Applied": return "bg-blue-100 text-blue-700 border border-blue-200";
      case "Shortlisted": return "bg-amber-100 text-amber-700 border border-amber-200";
      case "Interview Scheduled": return "bg-purple-100 text-purple-700 border border-purple-200";
      case "Rejected": return "bg-red-100 text-red-700 border border-red-200";
      default: return "bg-gray-100 text-gray-700 border border-gray-200";
    }
  };

  const formatDate = (dateString) => {
    if (!dateString || typeof dateString !== 'string') return "";
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return "";
      return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
    } catch (error) {
      console.error('Error formatting date:', error);
      return "";
    }
  };

  const scheduleInterview = async (appId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/candidates/${CANDIDATE_ID}/applications/${appId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: 'Interview Scheduled' }),
      });

      if (!response.ok) throw new Error('Failed to schedule interview');

      // Update local state
      setApplications(prev => prev.map(a =>
        a.id === appId ? {
          ...a,
          status: "Interview Scheduled",
          interviews: [...(a.interviews || []), { date: new Date().toISOString(), status: "Scheduled" }]
        } : a
      ));

      alert('Interview scheduled successfully!');
    } catch (err) {
      console.error('Error scheduling interview:', err);
      alert('Failed to schedule interview. Please try again.');
    }
  };

  const applyWithAI = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${API_BASE_URL}/candidates/${CANDIDATE_ID}/apply-ai`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) throw new Error('Failed to apply with AI');

      const result = await response.json();
      alert(`${result.message}`);

      // Refresh dashboard data to show new applications
      await fetchDashboardData();
    } catch (err) {
      console.error('Error applying with AI:', err);
      alert('Failed to apply with AI. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handlePayslipUpload = async (event) => {
    const files = Array.from(event.target.files);
    if (files.length + candidateProfile.payslips.length > 6) {
      alert("You can upload a maximum of 6 payslips.");
      return;
    }

    try {
      let uploadedCount = 0;
      for (const file of files) {
        const formData = new FormData();
        formData.append('file', file);

        // Generate readable month_year with sequence number for uniqueness
        const currentDate = new Date();
        const month = String(currentDate.getMonth() + 1).padStart(2, '0');
        const year = currentDate.getFullYear();
        const sequence = Math.floor(Math.random() * 1000); // Random 3-digit number for uniqueness
        const monthYear = `${month}/${year}-${sequence}`; // Format: 06/2025-123

        const response = await fetch(`${API_BASE_URL}/candidates/${CANDIDATE_ID}/payslips?month_year=${monthYear}`, {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`Failed to upload ${file.name}: ${errorText}`);
        }
        uploadedCount++;
      }

      alert(`${uploadedCount} payslip(s) uploaded successfully!`);
      // Refresh dashboard data to show new payslips
      await fetchDashboardData();
    } catch (err) {
      console.error('Error uploading payslips:', err);
      alert(`Failed to upload payslips: ${err.message}`);
    }
  };

  const handlePayslipDelete = async (payslipId, filename) => {
    if (!confirm(`Are you sure you want to delete ${filename}?`)) {
      return;
    }

    try {
      const response = await fetch(`${API_BASE_URL}/candidates/${CANDIDATE_ID}/payslips/${payslipId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to delete payslip: ${errorText}`);
      }

      alert('Payslip deleted successfully!');
      // Refresh dashboard data to update payslip list
      await fetchDashboardData();
    } catch (err) {
      console.error('Error deleting payslip:', err);
      alert(`Failed to delete payslip: ${err.message}`);
    }
  };

  const handlePayslipView = (payslipId) => {
    window.open(`${API_BASE_URL}/candidates/${CANDIDATE_ID}/payslips/${payslipId}/download`, '_blank');
  };

  const formatPayslipPeriod = (monthYear) => {
    if (!monthYear) return 'Unknown Period';

    try {
      // Extract month/year from format like "06/2025-123"
      const [monthYearPart] = monthYear.split('-');
      const [month, year] = monthYearPart.split('/');

      const monthNames = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
      ];

      const monthName = monthNames[parseInt(month) - 1];
      return `${monthName} ${year}`;
    } catch (error) {
      return monthYear; // Fallback to original if parsing fails
    }
  };

  const fetchPayslips = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/candidates/${CANDIDATE_ID}/payslips/summary`);
      if (response.ok) {
        const payslipData = await response.json();
        setCandidateProfile(prev => ({
          ...prev,
          payslips: payslipData.payslips || []
        }));
        console.log('✅ Payslips loaded:', payslipData);
      }
    } catch (err) {
      console.error('Error fetching payslips:', err);
    }
  };

  const fetchAvailabilitySlots = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/candidates/${CANDIDATE_ID}/availability`);
      if (response.ok) {
        const availabilityData = await response.json();

        // Process availability data to match frontend format
        const generalSlots = {};
        const jobSpecificSlots = {};

        availabilityData.forEach(slot => {
          // Parse DD/MM/YYYY format correctly
          let dayName;
          try {
            if (slot.interview_date && slot.interview_date.includes('/')) {
              const [day, month, year] = slot.interview_date.split('/');
              const dateObj = new Date(parseInt(year), parseInt(month) - 1, parseInt(day)); // month is 0-indexed
              dayName = dateObj.toLocaleDateString('en-US', { weekday: 'long' });
            } else {
              console.warn(`⚠️ Invalid date format: ${slot.interview_date}`);
              dayName = 'Invalid Date';
            }
          } catch (error) {
            console.warn(`⚠️ Date parsing error for ${slot.interview_date}:`, error);
            dayName = 'Invalid Date';
          }

          const timeSlot = `${slot.start_time} - ${slot.end_time}`;

          if (slot.slot_type === 'general') {
            if (!generalSlots[dayName]) {
              generalSlots[dayName] = [];
            }
            generalSlots[dayName].push({ time: timeSlot, id: slot.id });
          } else if (slot.slot_type === 'job_specific' && slot.company_name) {
            // Find matching application
            const app = applications.find(a => a.company === slot.company_name && a.position === slot.job_position);
            if (app) {
              if (!jobSpecificSlots[app.id]) {
                jobSpecificSlots[app.id] = [];
              }
              jobSpecificSlots[app.id].push({ day: dayName, time: timeSlot, id: slot.id });
            } else {
              console.warn(`⚠️ No matching application found for ${slot.company_name} - ${slot.job_position}`);
            }
          }
        });

        // Convert general slots to array format with slot IDs
        const generalSlotsArray = Object.entries(generalSlots).map(([day, slots]) => ({
          day,
          slots: slots.map(s => s.time), // Keep time format for display
          slotIds: slots.map(s => s.id)   // Store IDs for deletion
        }));

        setAvailabilitySlots(generalSlotsArray);
        setShortlistedAvailability(jobSpecificSlots);

        console.log('✅ Availability slots loaded from database');
      } else {
        console.error('❌ Failed to fetch availability slots:', response.status);
      }
    } catch (err) {
      console.error('❌ Error fetching availability slots:', err);
    }
  };

  const handleAddressProofUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    if (!selectedDocumentType) {
      alert('Please select a document type first!');
      return;
    }

    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch(`${API_BASE_URL}/candidates/${CANDIDATE_ID}/background-verification/address-proof?document_type=${encodeURIComponent(selectedDocumentType)}`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) throw new Error('Failed to upload address proof');

      alert(`${selectedDocumentType} uploaded successfully!`);

      // Update the candidate profile with the uploaded document info
      setCandidateProfile(prev => ({
        ...prev,
        backgroundVerification: {
          ...prev.backgroundVerification,
          addressProof: {
            name: file.name,
            type: selectedDocumentType,
            verified: 'pending',
            uploadedAt: new Date().toISOString()
          }
        }
      }));

      // Refresh dashboard data
      await fetchDashboardData();
    } catch (err) {
      console.error('Error uploading address proof:', err);
      alert('Failed to upload address proof. Please try again.');
    }
  };

  const handleDigiLockerAuth = async () => {
    // Check if user is authenticated with Supabase first
    if (!isAuthenticated) {
      setShowAuthModal(true);
      return;
    }

    // Proceed with DigiLocker authentication
    try {
      const response = await fetch(`${API_BASE_URL}/candidates/${CANDIDATE_ID}/digilocker/auth`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${currentUser?.access_token || ''}`,
        },
        body: JSON.stringify({
          user_id: currentUser?.id,
          email: currentUser?.email,
          phone: currentUser?.phone
        })
      });

      if (response.ok) {
        const data = await response.json();
        // Open DigiLocker auth URL in a new window
        window.open(data.auth_url, '_blank', 'width=600,height=700');
        alert('Please complete authentication in the new window');

        // Update candidate profile to show pending status
        setCandidateProfile(prev => ({
          ...prev,
          digilockerAuth: {
            status: 'pending',
            user_email: currentUser?.email,
            user_phone: currentUser?.phone
          }
        }));
      } else {
        const error = await response.json();
        alert(error.detail || 'Failed to initiate DigiLocker authentication');
      }
    } catch (error) {
      console.error('Error initiating DigiLocker auth:', error);
      alert('Failed to initiate DigiLocker authentication');
    }
  };

  const handleAuthSuccess = (user) => {
    setCurrentUser(user);
    setIsAuthenticated(true);
    setShowAuthModal(false);

    // Auto-proceed with DigiLocker auth after successful Supabase auth
    setTimeout(() => {
      handleDigiLockerAuth();
    }, 1000);
  };

  const handleVerifyDocument = async (documentId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/candidates/${CANDIDATE_ID}/documents/${documentId}/verify`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        alert(`Document verification completed: ${data.verification_status}`);

        // Update the document status in the UI
        setCandidateProfile(prev => ({
          ...prev,
          backgroundVerification: {
            ...prev.backgroundVerification,
            addressProof: {
              ...prev.backgroundVerification.addressProof,
              verified: data.verification_status
            }
          }
        }));
      } else {
        const error = await response.json();
        alert(error.detail || 'Failed to verify document');
      }
    } catch (error) {
      console.error('Error verifying document:', error);
      alert('Failed to verify document');
    }
  };

  const handleResumeUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch(`${API_BASE_URL}/candidates/${CANDIDATE_ID}/resume`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) throw new Error('Failed to upload resume');

      alert('Resume uploaded successfully!');
      // Refresh dashboard data
      await fetchDashboardData();
    } catch (err) {
      console.error('Error uploading resume:', err);
      alert('Failed to upload resume. Please try again.');
    }
  };

  const handleResumeDownload = () => {
    window.open(`${API_BASE_URL}/candidates/${CANDIDATE_ID}/resume/download`, '_blank');
  };

  const toggleResumeVisibility = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/candidates/${CANDIDATE_ID}/resume/toggle-visibility`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) throw new Error('Failed to toggle resume visibility');

      const result = await response.json();

      // Update local state with backend response
      setCandidateProfile(prev => ({
        ...prev,
        resume: {
          ...prev.resume,
          visible: result.is_visible
        }
      }));

      // Sync UI state with backend visibility state
      setIsResumeOpen(result.is_visible);

    } catch (err) {
      console.error('Error toggling resume visibility:', err);
      // Fallback: just toggle the UI state
      setIsResumeOpen(!isResumeOpen);
    }
  };

  // Load Interview Feedback from Database
  const loadInterviewFeedback = async () => {
    try {
      setLoading(true);

      const response = await fetch(`${API_BASE_URL}/candidates/${CANDIDATE_ID}/interview-feedback`);

      if (!response.ok) {
        if (response.status === 404) {
          alert('No interview feedback found for this candidate.');
          return;
        }
        throw new Error('Failed to load interview feedback');
      }

      const result = await response.json();

      // Update candidate profile with database feedback
      setCandidateProfile(prev => ({
        ...prev,
        interviewFeedback: {
          score: result.overall_score,
          comments: result.general_comments,
          mockInterview: result.questions.map(q => ({
            question: q.question,
            answer: q.answer,
            score: q.score,
            feedback: q.feedback
          }))
        },
        simulationInterviewScore: result.overall_score
      }));

      alert(`Interview feedback loaded! Overall Score: ${result.overall_score}/100`);
    } catch (err) {
      console.error('Error loading interview feedback:', err);
      alert('Failed to load interview feedback. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'resume':
        return (
          <div className="p-8 bg-white rounded-xl border border-gray-200 h-full overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <FileText className="w-5 h-5 mr-2 text-blue-600" />
                Resume View
              </h3>
              <button
                onClick={toggleResumeVisibility}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium shadow-sm flex items-center"
              >
                <FileText className="w-4 h-4 mr-2" />
                {isResumeOpen ? "Hide Resume" : "View Resume"}
              </button>
            </div>
            {isResumeOpen && candidateProfile.resume && (
              <div className="bg-white border border-gray-200 rounded-xl p-8">
                <h1 className="text-xl font-semibold text-gray-900 mb-4">{candidateProfile.resume?.name || 'N/A'}</h1>
                <div className="flex flex-wrap items-center mb-4 text-gray-600 text-sm">
                  <div className="flex items-center mr-6">
                    <Phone className="w-4 h-4 mr-2" />
                    {candidateProfile.resume?.phone || 'N/A'}
                  </div>
                  <div className="flex items-center">
                    <MapPin className="w-4 h-4 mr-2" />
                    {candidateProfile.resume?.address || 'N/A'}
                  </div>
                </div>
                <div className="mb-6">
                  <h2 className="text-base font-semibold text-gray-900 mb-2">Professional Summary</h2>
                  <p className="text-gray-700 text-sm">{candidateProfile.resume?.summary || 'No summary provided.'}</p>
                </div>
                <div className="mb-6">
                  <h2 className="text-base font-semibold text-gray-900 mb-2">Skills</h2>
                  <div className="flex flex-wrap gap-2">
                    {(candidateProfile.resume?.skills || []).map((skill, idx) => (
                      <span key={idx} className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm">
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>
                <div className="mb-6">
                  <h2 className="text-base font-semibold text-gray-900 mb-2">Work Experience</h2>
                  {(candidateProfile.resume?.experience || []).map((exp, idx) => (
                    <div key={idx} className="mb-4">
                      <div className="flex justify-between">
                        <h3 className="text-sm font-semibold text-gray-900">{exp.role || 'N/A'}</h3>
                        <span className="text-gray-600 text-sm">{exp.duration || 'N/A'}</span>
                      </div>
                      <p className="text-gray-700 text-sm mb-2">{exp.company || 'N/A'}</p>
                      <ul className="list-disc pl-5 text-gray-700 text-sm">
                        {(exp.responsibilities || []).map((responsibility, respIdx) => (
                          <li key={respIdx}>{responsibility}</li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
                <div className="mb-6">
                  <h2 className="text-base font-semibold text-gray-900 mb-2">Education</h2>
                  {(candidateProfile.resume?.education || []).map((edu, idx) => (
                    <div key={idx} className="mb-2">
                      <div className="flex justify-between">
                        <h3 className="text-sm font-semibold text-gray-900">{edu.degree || 'N/A'}</h3>
                        <span className="text-gray-600 text-sm">{edu.year || 'N/A'}</span>
                      </div>
                      <p className="text-gray-700 text-sm">{edu.institution || 'N/A'}</p>
                    </div>
                  ))}
                </div>
                <div>
                  <h2 className="text-base font-semibold text-gray-900 mb-2">Certifications</h2>
                  {(candidateProfile.resume?.certifications || []).map((cert, idx) => (
                    <div key={idx} className="mb-2">
                      <div className="flex justify-between">
                        <h3 className="text-sm font-semibold text-gray-900">{cert.name || 'N/A'}</h3>
                        <span className="text-gray-600 text-sm">{cert.year || 'N/A'}</span>
                      </div>
                      <p className="text-gray-700 text-sm">{cert.issuer || 'N/A'}</p>
                    </div>
                  ))}
                </div>
                <div className="mt-6 flex space-x-4">
                  <input
                    type="file"
                    accept=".pdf,.doc,.docx"
                    onChange={handleResumeUpload}
                    className="hidden"
                    id="resume-upload"
                  />
                  <label
                    htmlFor="resume-upload"
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium shadow-sm cursor-pointer flex items-center"
                  >
                    <Upload className="w-4 h-4 mr-2" />
                    Update Resume
                  </label>
                  <button
                    onClick={handleResumeDownload}
                    className="px-4 py-2 bg-green-600 text-white rounded-lg text-sm font-medium shadow-sm flex items-center"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Download PDF
                  </button>
                </div>
              </div>
            )}
          </div>
        );
      case 'feedback':
        return (
          <div className="p-8 bg-white rounded-xl border border-gray-200 h-full overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <Star className="w-5 h-5 mr-2 text-amber-500" />
                Interview Feedback
              </h3>
              <button
                onClick={loadInterviewFeedback}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium shadow-sm flex items-center"
                disabled={loading}
              >
                <Star className="w-4 h-4 mr-2" />
                Load Feedback
              </button>
            </div>
            <div className="bg-white border border-gray-200 rounded-xl p-6 mb-6 shadow-sm">
              <div className="space-y-4">
                <div>
                  <p className="text-xs text-gray-500 font-medium">Overall Score</p>
                  <p className="font-semibold text-gray-900 text-sm">
                    {candidateProfile.interviewFeedback?.score ?? 'N/A'}/100
                  </p>
                </div>
                <div>
                  <p className="text-xs text-gray-500 font-medium">Overall Comments</p>
                  <p className="text-gray-700 text-sm">
                    {candidateProfile.interviewFeedback?.comments || 'No comments provided.'}
                  </p>
                </div>
              </div>
            </div>
            <h4 className="text-base font-semibold text-gray-900 mb-4">Mock Interview Questions</h4>
            <div className="space-y-6">
              {(candidateProfile.interviewFeedback?.mockInterview || []).map((item, idx) => (
                <div key={idx} className="bg-gradient-to-r from-indigo-50 to-blue-50 border border-indigo-200 rounded-xl p-6 shadow-sm">
                  <div className="mb-4">
                    <p className="text-sm font-semibold text-indigo-800">Question {idx + 1}</p>
                    <p className="text-gray-700 text-sm">{item.question}</p>
                  </div>
                  <div className="mb-4">
                    <p className="text-sm font-semibold text-indigo-800">Answer</p>
                    <p className="text-gray-700 text-sm">{item.answer}</p>
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-semibold text-indigo-800">Score</p>
                      <p className="text-gray-700 text-sm">{item.score}/100</p>
                    </div>
                    <div>
                      <p className="text-sm font-semibold text-indigo-800">Feedback</p>
                      <p className="text-gray-700 text-sm">{item.feedback}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );
      case 'availability':
        return (
          <div className="p-8 bg-white rounded-xl border border-gray-200 h-full overflow-y-auto">
            <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
              <Calendar className="w-5 h-5 mr-2 text-blue-600" />
              Interview Availability
            </h3>
            <div className="flex justify-between items-center mb-6">
              <span className="text-xs text-gray-500 font-medium">
                Companies will schedule interviews during these times
              </span>
            </div>

            {/* General Availability Display */}
            <div className="mb-6">
              {availabilitySlots && availabilitySlots.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {availabilitySlots.map((daySlot, idx) => (
                    <div key={idx} className="border border-gray-200 rounded-xl p-6 bg-white shadow-sm">
                      <h4 className="font-semibold text-gray-900 text-sm mb-4">{daySlot.day}</h4>
                      <ul className="space-y-3">
                        {(daySlot.slots || []).map((timeSlot, slotIdx) => (
                          <li key={slotIdx} className="flex items-center text-gray-700 text-sm">
                            <Clock className="w-4 h-4 mr-2 text-blue-500" />
                            {timeSlot}
                            <button
                              className="ml-auto text-red-500"
                              onClick={() => removeAvailabilitySlot(daySlot.day, slotIdx, null, daySlot.slotIds?.[slotIdx])}
                            >
                              <X className="w-4 h-4" />
                            </button>
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500 border border-gray-200 rounded-xl bg-gray-50">
                  <Calendar className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                  <p className="text-sm">No general availability slots added yet</p>
                  <p className="text-xs">Add your availability using the form below</p>
                </div>
              )}
            </div>

            {/* Add General Availability Form */}
            <div className="bg-white border border-gray-200 rounded-xl p-6 mb-6 shadow-sm">
              <h4 className="font-semibold text-gray-900 text-sm mb-4">Add General Availability</h4>
              <div className="flex flex-wrap gap-4">
                <select
                  className="px-4 py-2 border border-gray-200 rounded-lg bg-white text-sm shadow-sm"
                  value={newSlot.day}
                  onChange={(e) => setNewSlot({...newSlot, day: e.target.value})}
                >
                  {["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"].map(day => (
                    <option key={day} value={day}>{day}</option>
                  ))}
                </select>
                <select
                  className="px-4 py-2 border border-gray-200 rounded-lg bg-white text-sm shadow-sm"
                  value={newSlot.time}
                  onChange={(e) => setNewSlot({...newSlot, time: e.target.value})}
                >
                  {[
                    "9:00 AM - 11:00 AM",
                    "11:00 AM - 1:00 PM",
                    "1:00 PM - 3:00 PM",
                    "3:00 PM - 5:00 PM"
                  ].map(time => (
                    <option key={time} value={time}>{time}</option>
                  ))}
                </select>
                <button
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium shadow-sm"
                  onClick={() => addAvailabilitySlot()}
                >
                  Add Time Slot
                </button>
              </div>
            </div>

            {/* Shortlisted Jobs Availability */}
            {applications.filter(app => app.status === "Shortlisted").length > 0 && (
              <div className="mb-8">
                <h4 className="text-base font-semibold text-gray-900 mb-4">Shortlisted Jobs Availability</h4>
                {applications
                  .filter(app => app.status === "Shortlisted")
                  .map(app => (
                    <div key={app.id} className="mb-6 border border-gray-200 rounded-xl p-6 bg-white shadow-sm">
                      <h5 className="font-semibold text-gray-900 text-sm mb-4">{app.position} at {app.company}</h5>
                      <ul className="space-y-3 mb-4">
                        {(shortlistedAvailability[app.id] || []).map((slot, slotIdx) => (
                          <li key={slotIdx} className="flex items-center text-gray-700 text-sm">
                            <Clock className="w-4 h-4 mr-2 text-blue-500" />
                            {slot.day}: {slot.time}
                            <button
                              className="ml-auto text-red-500"
                              onClick={() => removeAvailabilitySlot(slot.day, slotIdx, app.id, slot.id)}
                            >
                              <X className="w-4 h-4" />
                            </button>
                          </li>
                        ))}
                      </ul>
                      <div className="flex flex-wrap gap-4">
                        <select
                          className="px-4 py-2 border border-gray-200 rounded-lg bg-white text-sm shadow-sm"
                          value={newJobSlot.day}
                          onChange={(e) => setNewJobSlot({...newJobSlot, day: e.target.value})}
                        >
                          {["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"].map(day => (
                            <option key={day} value={day}>{day}</option>
                          ))}
                        </select>
                        <select
                          className="px-4 py-2 border border-gray-200 rounded-lg bg-white text-sm shadow-sm"
                          value={newJobSlot.time}
                          onChange={(e) => setNewJobSlot({...newJobSlot, time: e.target.value})}
                        >
                          {[
                            "9:00 AM - 11:00 AM",
                            "11:00 AM - 1:00 PM",
                            "1:00 PM - 3:00 PM",
                            "3:00 PM - 5:00 PM"
                          ].map(time => (
                            <option key={time} value={time}>{time}</option>
                          ))}
                        </select>
                        <button
                          className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium shadow-sm"
                          onClick={() => addAvailabilitySlot(app.id)}
                        >
                          Add Time Slot
                        </button>
                      </div>
                    </div>
                  ))}
              </div>
            )}
          </div>
        );
      case 'salary':
        return (
          <div className="p-8 bg-white rounded-xl border border-gray-200 h-full overflow-y-auto">
            <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
              <DollarSign className="w-5 h-5 mr-2 text-green-600" />
              Salary Information
            </h3>
            <div className="bg-white border border-gray-200 rounded-xl p-6">
              <div className="grid grid-cols-2 gap-6">
                <div className="flex items-center bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                  <DollarSign className="w-5 h-5 text-green-600 mr-3" />
                  <div>
                    <p className="text-sm font-semibold text-gray-900">Current Salary</p>
                    <p className="text-sm text-gray-700">{candidateProfile.salary?.currentCTC || 'N/A'}</p>
                  </div>
                </div>
                <div className="flex items-center bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                  <DollarSign className="w-5 h-5 text-green-600 mr-3" />
                  <div>
                    <p className="text-sm font-semibold text-gray-900">Expected Salary</p>
                    <p className="text-sm text-gray-700">{candidateProfile.salary?.expectedCTC || 'N/A'}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      case 'noticePeriod':
        return (
          <div className="p-8 bg-white rounded-xl border border-gray-200 h-full overflow-y-auto">
            <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
              <Bell className="w-5 h-5 mr-2 text-blue-600" />
              Notice Period
            </h3>
            <div className="bg-white border border-gray-200 rounded-xl p-6 mb-6 shadow-sm">
              <div className="flex items-center mb-4">
                <Bell className="w-5 h-5 text-blue-600 mr-3" />
                <div>
                  <p className="text-sm font-semibold text-gray-900">Notice Period</p>
                  <p className="text-sm text-gray-700">{candidateProfile.noticePeriod || 'N/A'}</p>
                </div>
              </div>
              <div className="mt-6">
                <h4 className="text-base font-semibold text-gray-900 mb-4">Upload Last 6 Months Payslips</h4>
                <div className="flex items-center justify-between mb-4">
                  <input
                    type="file"
                    accept=".pdf,.jpg,.png"
                    multiple
                    onChange={handlePayslipUpload}
                    className="hidden"
                    id="payslip-upload"
                  />
                  <label
                    htmlFor="payslip-upload"
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium shadow-sm cursor-pointer flex items-center"
                  >
                    <Upload className="w-4 h-4 mr-2" />
                    Upload Payslips
                  </label>
                  <p className="text-xs text-gray-500">Maximum 6 files (PDF, JPG, PNG)</p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {(candidateProfile.payslips || []).length === 0 ? (
                    <div className="col-span-2 text-center py-8 text-gray-500">
                      <FileText className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                      <p className="text-sm">No payslips uploaded yet</p>
                      <p className="text-xs">Upload your last 6 months payslips above</p>
                    </div>
                  ) : (
                    (candidateProfile.payslips || []).map((payslip, idx) => (
                      <div key={payslip.id || idx} className="flex items-center bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <FileText className="w-5 h-5 text-blue-600 mr-3" />
                        <div className="flex-1">
                          <p className="text-sm font-semibold text-gray-900">{payslip.filename || payslip.name}</p>
                          <p className="text-xs text-gray-600">
                            {payslip.month_year && `Period: ${formatPayslipPeriod(payslip.month_year)}`}
                            {payslip.file_size_mb && ` • ${payslip.file_size_mb} MB`}
                            {payslip.uploaded_at && ` • Uploaded: ${payslip.uploaded_at}`}
                          </p>
                          <button
                            onClick={() => handlePayslipView(payslip.id)}
                            className="text-xs text-blue-600 hover:underline mt-1"
                          >
                            View File
                          </button>
                        </div>
                        <button
                          className="ml-auto text-red-500 hover:text-red-700"
                          onClick={() => handlePayslipDelete(payslip.id, payslip.filename || payslip.name)}
                          title="Delete payslip"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                    ))
                  )}
                </div>
                <div className="mt-4 text-xs text-gray-500 text-center">
                  {candidateProfile.payslips?.length || 0}/6 payslips uploaded
                </div>
              </div>
            </div>
          </div>
        );
      case 'backgroundVerification':
        return (
          <div className="p-8 bg-white rounded-xl border border-gray-200 h-full overflow-y-auto">
            <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
              <Shield className="w-5 h-5 mr-2 text-purple-600" />
              Background Verification
            </h3>
            <div className="bg-white border border-gray-200 rounded-xl p-6 mb-6 shadow-sm">
              <div className="flex items-center mb-4">
                <Shield className="w-5 h-5 text-purple-600 mr-3" />
                <div>
                  <p className="text-sm font-semibold text-gray-900">Status</p>
                  <p className="text-sm text-gray-700">{candidateProfile.backgroundVerification?.status || 'N/A'}</p>
                  <p className="text-xs text-gray-600 mt-1">{candidateProfile.backgroundVerification?.details || 'No details provided.'}</p>
                </div>
              </div>
              {/* DigiLocker Authentication Section */}
              <div className="mt-6 mb-6">
                <h4 className="text-base font-semibold text-gray-900 mb-4 flex items-center">
                  <Shield className="w-4 h-4 mr-2 text-blue-600" />
                  DigiLocker Authentication
                </h4>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <Shield className="w-4 h-4 text-blue-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {isAuthenticated && candidateProfile.digilockerAuth?.status === 'authenticated'
                            ? 'DigiLocker Connected'
                            : isAuthenticated
                            ? 'Ready for DigiLocker'
                            : 'Authentication Required'}
                        </p>
                        <p className="text-xs text-gray-600">
                          {isAuthenticated && candidateProfile.digilockerAuth?.status === 'authenticated'
                            ? `Connected as ${currentUser?.email || currentUser?.phone}`
                            : isAuthenticated
                            ? 'Click to connect your DigiLocker account'
                            : 'Please sign in to continue with DigiLocker verification'}
                        </p>
                        {currentUser && (
                          <p className="text-xs text-blue-600 mt-1">
                            Signed in as: {currentUser.email || currentUser.phone}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {isAuthenticated && (
                        <button
                          onClick={() => authHelpers.signOut().then(() => {
                            setCurrentUser(null);
                            setIsAuthenticated(false);
                          })}
                          className="px-3 py-1 text-xs text-gray-600 border border-gray-300 rounded hover:bg-gray-50"
                        >
                          Sign Out
                        </button>
                      )}
                      {candidateProfile.digilockerAuth?.status !== 'authenticated' && (
                        <button
                          onClick={handleDigiLockerAuth}
                          className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 flex items-center space-x-2"
                        >
                          <Shield className="w-4 h-4" />
                          <span>{isAuthenticated ? 'Connect DigiLocker' : 'Sign In & Connect'}</span>
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-6">
                <h4 className="text-base font-semibold text-gray-900 mb-4">Address Proof</h4>
                <div className="flex items-center justify-between mb-4">
                  <input
                    type="file"
                    accept=".pdf,.jpg,.png"
                    onChange={handleAddressProofUpload}
                    className="hidden"
                    id="address-proof-upload"
                  />
                  <label
                    htmlFor="address-proof-upload"
                    className="px-4 py-2 bg-purple-600 text-white rounded-lg text-sm font-medium shadow-sm cursor-pointer flex items-center"
                  >
                    <Upload className="w-4 h-4 mr-2" />
                    Upload Address Proof
                  </label>
                  <select
                    className="px-4 py-2 border border-gray-200 rounded-lg bg-white text-sm shadow-sm"
                    value={selectedDocumentType}
                    onChange={(e) => setSelectedDocumentType(e.target.value)}
                  >
                    <option value="">Select Document Type</option>
                    {["Aadhar Card", "Passport", "Driver's License", "Utility Bill", "Voter ID"].map(type => (
                      <option key={type} value={type}>{type}</option>
                    ))}
                  </select>
                </div>
                {candidateProfile.backgroundVerification?.addressProof && (
                  <div className="bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <FileText className="w-5 h-5 text-purple-600 mr-3" />
                        <div>
                          <p className="text-sm font-semibold text-gray-900">{candidateProfile.backgroundVerification.addressProof.name}</p>
                          <p className="text-xs text-gray-600">Type: {candidateProfile.backgroundVerification.addressProof.type || selectedDocumentType || 'N/A'}</p>
                          <p className="text-xs text-gray-500">Uploaded: {candidateProfile.backgroundVerification.addressProof.uploadedAt ? new Date(candidateProfile.backgroundVerification.addressProof.uploadedAt).toLocaleString() : 'N/A'}</p>
                          <div className="flex items-center space-x-3 mt-1">
                            <a href={candidateProfile.backgroundVerification.addressProof.url} target="_blank" rel="noopener noreferrer" className="text-xs text-purple-600 hover:underline">
                              View File
                            </a>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              candidateProfile.backgroundVerification.addressProof.verified === 'genuine'
                                ? 'bg-green-100 text-green-800'
                                : candidateProfile.backgroundVerification.addressProof.verified === 'fake'
                                ? 'bg-red-100 text-red-800'
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {candidateProfile.backgroundVerification.addressProof.verified || 'pending'}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {candidateProfile.digilockerAuth?.status === 'authenticated' &&
                         candidateProfile.backgroundVerification.addressProof.verified === 'pending' && (
                          <button
                            onClick={() => handleVerifyDocument(candidateProfile.backgroundVerification.addressProof.id)}
                            className="px-3 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700"
                          >
                            Verify with DigiLocker
                          </button>
                        )}
                        <button
                          className="text-red-500"
                          onClick={() => setCandidateProfile(prev => ({
                            ...prev,
                            backgroundVerification: {
                              ...prev.backgroundVerification,
                              addressProof: null
                            }
                          }))}
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
              <div className="mt-6">
                <h4 className="text-base font-semibold text-gray-900 mb-4">Provident Fund Details</h4>
                <textarea
                  className="w-full p-4 border border-gray-200 rounded-lg bg-white text-sm shadow-sm"
                  placeholder="Enter Provident Fund account number and details"
                  value={providentFund}
                  onChange={(e) => setProvidentFund(e.target.value)}
                />
                <button
                  className="mt-4 px-4 py-2 bg-purple-600 text-white rounded-lg text-sm font-medium shadow-sm"
                  onClick={() => setCandidateProfile(prev => ({
                    ...prev,
                    backgroundVerification: {
                      ...prev.backgroundVerification,
                      providentFund
                    }
                  }))}
                >
                  Save Provident Fund Details
                </button>
              </div>
              <div className="mt-6">
                <h4 className="text-base font-semibold text-gray-900 mb-4">Moonlighting Details</h4>
                <textarea
                  className="w-full p-4 border border-gray-200 rounded-lg bg-white text-sm shadow-sm"
                  placeholder="Enter details of any additional work or moonlighting activities"
                  value={moonlightingDetails}
                  onChange={(e) => setMoonlightingDetails(e.target.value)}
                />
                <button
                  className="mt-4 px-4 py-2 bg-purple-600 text-white rounded-lg text-sm font-medium shadow-sm"
                  onClick={() => setCandidateProfile(prev => ({
                    ...prev,
                    backgroundVerification: {
                      ...prev.backgroundVerification,
                      moonlightingDetails
                    }
                  }))}
                >
                  Save Moonlighting Details
                </button>
              </div>
            </div>
          </div>
        );
      case 'employers':
        return (
          <div className="p-8 bg-white rounded-xl border border-gray-200 h-full overflow-y-auto">
            <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
              <Briefcase className="w-5 h-5 mr-2 text-blue-600" />
              Employers ({applications.length})
            </h3>
            <div className="flex justify-between items-center mb-6">
              <span className="text-xs text-gray-500 font-medium flex items-center">
                <AlertCircle className="w-4 h-4 mr-2 text-red-500" />
                {applications.filter(app => getDaysRemaining(app.deadline) < 5).length} application(s) with upcoming deadlines
              </span>
              <button
                className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium shadow-sm flex items-center"
                onClick={applyWithAI}
              >
                <PlusCircle className="w-4 h-4 mr-2" />
                Apply with AI
              </button>
            </div>
            <div className="grid grid-cols-4 gap-6 mb-6">
              {[
                { status: "Applied", color: "blue", count: applications.filter(app => app.status === "Applied").length },
                { status: "Shortlisted", color: "amber", count: applications.filter(app => app.status === "Shortlisted").length },
                { status: "Interview", color: "purple", count: applications.filter(app => app.status === "Interview Scheduled").length },
                { status: "Rejected", color: "red", count: applications.filter(app => app.status === "Rejected").length }
              ].map(({ status, color, count }) => (
                <div key={status} className={`bg-${color}-100 rounded-xl p-6 border border-${color}-200 shadow-sm`}>
                  <p className={`text-${color}-700 text-sm font-semibold`}>{status}</p>
                  <p className={`text-lg font-semibold text-${color}-800`}>{count}</p>
                </div>
              ))}
            </div>
            <div className="space-y-6">
              {(applications || []).map(app => (
                <div key={app.id} className="border border-gray-200 rounded-xl overflow-hidden shadow-sm">
                  <div 
                    className="p-6 bg-white flex justify-between items-center cursor-pointer"
                    onClick={() => toggleExpandJob(app.id)}
                  >
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 flex-shrink-0 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold text-sm shadow-md">
                        {app.company?.charAt(0) || '?'}
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900 text-sm">{app.position || 'N/A'}</h4>
                        <p className="text-sm text-gray-600">{app.company || 'N/A'}</p>
                        <p className="text-xs text-gray-500">{app.companyInfo || 'No company info provided.'}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <span className={`text-xs px-3 py-1 rounded-full ${getStatusBadgeColor(app.status)} font-medium`}>
                        {app.status || 'Unknown'}
                      </span>
                      <div className="text-right">
                        <p className="text-xs text-gray-500">Applied: {formatDate(app.appliedDate)}</p>
                        <p className="text-xs text-gray-500">Deadline: {formatDate(app.deadline)}</p>
                        <p className="text-xs text-gray-500">Days left: {getDaysRemaining(app.deadline)}</p>
                      </div>
                      {expandedJobId === app.id ? (
                        <ChevronUp className="w-5 h-5 text-gray-500" />
                      ) : (
                        <ChevronDown className="w-5 h-5 text-gray-500" />
                      )}
                    </div>
                  </div>
                  {expandedJobId === app.id && (
                    <div className="p-6 bg-gray-50 border-t border-gray-200">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <p className="text-sm font-semibold text-gray-900 mb-2">Job Description</p>
                          <p className="text-sm text-gray-700">{app.description || 'No description provided.'}</p>
                        </div>
                        <div>
                          <p className="text-sm font-semibold text-gray-900 mb-2">Required Skills</p>
                          <div className="flex flex-wrap gap-2">
                            {(app.requiredSkills || []).map((skill, idx) => (
                              <span key={idx} className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm">
                                {skill}
                              </span>
                            ))}
                          </div>
                        </div>
                        <div>
                          <p className="text-sm font-semibold text-gray-900 mb-2">Match Score</p>
                          <p className="text-sm text-gray-700">{app.matchScore}%</p>
                        </div>
                        {app.rejected && app.learningGaps.length > 0 && (
                          <div>
                            <p className="text-sm font-semibold text-gray-900 mb-2">Learning Gaps</p>
                            <div className="space-y-4">
                              {(app.learningGaps || []).map((gap, idx) => (
                                <div key={idx} className="bg-red-50 p-4 rounded-lg border border-red-200">
                                  <p className="text-sm font-semibold text-red-800">{gap.skill}</p>
                                  <p className="text-sm text-gray-700"><strong>Recommendation:</strong> {gap.recommendation}</p>
                                  <p className="text-sm text-gray-700"><strong>Hands-On:</strong> {gap.handsOn}</p>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                        {app.status === "Shortlisted" && (
                          <div>
                            <p className="text-sm font-semibold text-gray-900 mb-2">Schedule Interview</p>
                            <button
                              className="px-4 py-2 bg-purple-600 text-white rounded-lg text-sm font-medium shadow-sm"
                              onClick={() => scheduleInterview(app.id)}
                            >
                              Schedule Interview
                            </button>
                          </div>
                        )}
                        {app.interviews.length > 0 && (
                          <div>
                            <p className="text-sm font-semibold text-gray-900 mb-2">Interviews</p>
                            <ul className="space-y-2">
                              {(app.interviews || []).map((interview, idx) => (
                                <li key={idx} className="text-sm text-gray-700">
                                  {formatDate(interview.date)} - {interview.status}
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 p-8 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard data...</p>
          <p className="text-sm text-gray-500 mt-2">Connecting to backend at {API_BASE_URL}</p>
        </div>
      </div>
    );
  }

  // Show error state with retry option
  if (error) {
    return (
      <div className="min-h-screen bg-gray-100 p-8 flex items-center justify-center">
        <div className="text-center max-w-md">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <h3 className="font-semibold mb-2">Connection Error</h3>
            <p className="text-sm">{error}</p>
          </div>
          <div className="space-y-2">
            <button
              onClick={fetchDashboardData}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium shadow-sm mr-2"
            >
              Retry Connection
            </button>
            <button
              onClick={() => setError(null)}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg text-sm font-medium shadow-sm"
            >
              Continue Offline
            </button>
          </div>
          <p className="text-xs text-gray-500 mt-4">
            Make sure the backend is running: <code>cd Backend && python apply.py</code>
          </p>
        </div>
      </div>
    );
  }

  // Show error state (but continue with fallback data)
  const showErrorBanner = error && !loading;

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className=" mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-2xl font-bold text-gray-900">Job Application Dashboard</h1>
          <div className="flex space-x-4">
            <button
              className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium shadow-sm flex items-center"
              onClick={applyWithAI}
              disabled={loading}
            >
              <PlusCircle className="w-4 h-4 mr-2" />
              Apply with AI
            </button>
          </div>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl border border-gray-200 shadow-sm">
              <div className="p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Profile Management</h2>
                <ul className="space-y-2">
                  {[
                    { id: 'resume', label: 'Resume', icon: FileText },
                    { id: 'feedback', label: 'Interview Feedback', icon: Star },
                    { id: 'availability', label: 'Interview Availability', icon: Calendar },
                    { id: 'salary', label: 'Salary Information', icon: DollarSign },
                    { id: 'noticePeriod', label: 'Notice Period', icon: Bell },
                    { id: 'backgroundVerification', label: 'Background Verification', icon: Shield },
                    { id: 'employers', label: 'Employers', icon: Briefcase },
                  ].map(tab => (
                    <li key={tab.id}>
                      <button
                        className={`w-full flex items-center px-4 py-2 text-sm font-medium rounded-lg ${
                          activeTab === tab.id
                            ? 'bg-blue-100 text-blue-700'
                            : 'text-gray-600 hover:bg-gray-50'
                        }`}
                        onClick={() => setActiveTab(tab.id)}
                      >
                        <tab.icon className="w-5 h-5 mr-2" />
                        {tab.label}
                      </button>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
          <div className="lg:col-span-3">{renderTabContent()}</div>
        </div>
      </div>

      {/* Authentication Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        onSuccess={handleAuthSuccess}
        purpose="DigiLocker document verification"
      />
    </div>
  );
};

export default JobApplicationDashboard;