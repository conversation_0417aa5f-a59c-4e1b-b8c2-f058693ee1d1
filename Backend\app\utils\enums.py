"""
Enums and constants for the application
"""

import os
from enum import Enum

class DocumentType(Enum):
    AADHAAR_CARD = "Aadhaar Card"
    PAN_CARD = "PAN Card"
    PASSPORT = "Passport"
    DRIVERS_LICENSE = "Driver's License"
    VOTER_ID = "Voter ID"
    UTILITY_BILL = "Utility Bill"
    BANK_STATEMENT = "Bank Statement"
    RENTAL_AGREEMENT = "Rental Agreement"
    PROPERTY_TAX_RECEIPT = "Property Tax Receipt"
    ELECTRICITY_BILL = "Electricity Bill"
    GAS_BILL = "Gas Bill"
    WATER_BILL = "Water Bill"
    TELEPHONE_BILL = "Telephone Bill"
    MOBILE_BILL = "Mobile Bill"
    INTERNET_BILL = "Internet Bill"
    AADHAR_CARD = "Aadhar Card"  # Keep old spelling for backward compatibility

    @classmethod
    def get_allowed_types(cls):
        """Get list of allowed document types"""
        return [doc_type.value for doc_type in cls]

class VerificationStatus(Enum):
    PENDING = "pending"
    VERIFIED = "verified"
    FAILED = "failed"
    GENUINE = "genuine"
    FAKE = "fake"

class AuthStatus(Enum):
    PENDING = "pending"
    AUTHENTICATED = "authenticated"
    FAILED = "failed"
    EXPIRED = "expired"

class BookingStatus(Enum):
    CONFIRMED = "confirmed"
    CANCELLED = "cancelled"
    COMPLETED = "completed"

class BackgroundVerificationStatus(Enum):
    IN_PROGRESS = "In Progress"
    COMPLETED = "Completed"
    FAILED = "Failed"
    PENDING = "Pending"

# File type constants
ALLOWED_RESUME_TYPES = [
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
]

ALLOWED_DOCUMENT_TYPES = [
    "application/pdf",
    "image/jpeg",
    "image/png",
    "image/jpg"
]

# File size limits (in bytes) - configurable via environment variables
MAX_RESUME_SIZE = int(os.getenv("MAX_RESUME_SIZE_MB", "5")) * 1024 * 1024  # Default 5MB
MAX_DOCUMENT_SIZE = int(os.getenv("MAX_DOCUMENT_SIZE_MB", "10")) * 1024 * 1024  # Default 10MB

# Business hours - configurable via environment variables
BUSINESS_START_HOUR = int(os.getenv("BUSINESS_START_HOUR", "9"))  # Default 9:00 AM
BUSINESS_END_HOUR = int(os.getenv("BUSINESS_END_HOUR", "17"))     # Default 5:00 PM

# Interview slot duration (in seconds) - configurable via environment variables
INTERVIEW_SLOT_DURATION = int(os.getenv("INTERVIEW_SLOT_DURATION_HOURS", "2")) * 3600  # Default 2 hours

# Maximum payslips allowed per candidate - configurable via environment variables
MAX_PAYSLIPS_PER_CANDIDATE = int(os.getenv("MAX_PAYSLIPS_PER_CANDIDATE", "6"))  # Default 6

# Default notice period days - configurable via environment variables
DEFAULT_NOTICE_PERIOD_DAYS = int(os.getenv("DEFAULT_NOTICE_PERIOD_DAYS", "30"))  # Default 30 days

# Server configuration - configurable via environment variables
DEFAULT_SERVER_HOST = os.getenv("SERVER_HOST", "0.0.0.0")
DEFAULT_SERVER_PORT = int(os.getenv("SERVER_PORT", "8001"))

# API configuration
API_TITLE = os.getenv("API_TITLE", "Job Application Dashboard")
API_VERSION = os.getenv("API_VERSION", "1.0.0")
API_DESCRIPTION = os.getenv("API_DESCRIPTION", "Complete job application management system with candidate tracking, resume management, interview feedback, and more")
