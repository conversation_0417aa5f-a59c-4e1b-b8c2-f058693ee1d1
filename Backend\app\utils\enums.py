"""
Enums and constants for the application
"""

from enum import Enum

class DocumentType(Enum):
    AADHAAR_CARD = "Aadhaar Card"
    PAN_CARD = "PAN Card"
    PASSPORT = "Passport"
    DRIVERS_LICENSE = "Driver's License"
    VOTER_ID = "Voter ID"
    UTILITY_BILL = "Utility Bill"
    BANK_STATEMENT = "Bank Statement"
    RENTAL_AGREEMENT = "Rental Agreement"
    PROPERTY_TAX_RECEIPT = "Property Tax Receipt"
    ELECTRICITY_BILL = "Electricity Bill"
    GAS_BILL = "Gas Bill"
    WATER_BILL = "Water Bill"
    TELEPHONE_BILL = "Telephone Bill"
    MOBILE_BILL = "Mobile Bill"
    INTERNET_BILL = "Internet Bill"
    AADHAR_CARD = "Aadhar Card"  # Keep old spelling for backward compatibility

    @classmethod
    def get_allowed_types(cls):
        """Get list of allowed document types"""
        return [doc_type.value for doc_type in cls]

class VerificationStatus(Enum):
    PENDING = "pending"
    VERIFIED = "verified"
    FAILED = "failed"
    GENUINE = "genuine"
    FAKE = "fake"

class AuthStatus(Enum):
    PENDING = "pending"
    AUTHENTICATED = "authenticated"
    FAILED = "failed"
    EXPIRED = "expired"

class BookingStatus(Enum):
    CONFIRMED = "confirmed"
    CANCELLED = "cancelled"
    COMPLETED = "completed"

class BackgroundVerificationStatus(Enum):
    IN_PROGRESS = "In Progress"
    COMPLETED = "Completed"
    FAILED = "Failed"
    PENDING = "Pending"

# File type constants
ALLOWED_RESUME_TYPES = [
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
]

ALLOWED_DOCUMENT_TYPES = [
    "application/pdf",
    "image/jpeg",
    "image/png",
    "image/jpg"
]

# File size limits (in bytes)
MAX_RESUME_SIZE = 5 * 1024 * 1024  # 5MB
MAX_DOCUMENT_SIZE = 10 * 1024 * 1024  # 10MB

# Business hours
BUSINESS_START_HOUR = 9  # 9:00 AM
BUSINESS_END_HOUR = 17   # 5:00 PM

# Interview slot duration (in seconds)
INTERVIEW_SLOT_DURATION = 7200  # 2 hours

# Maximum payslips allowed per candidate
MAX_PAYSLIPS_PER_CANDIDATE = 6
