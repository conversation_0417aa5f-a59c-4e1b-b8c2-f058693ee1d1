"""
Comprehensive Pydantic schemas for the job application system
"""

from datetime import datetime, date
from typing import Optional, List, Dict, Any
from pydantic import BaseModel

class CandidateCreate(BaseModel):
    candidate_id: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    position: Optional[str] = None

class CandidateResponse(BaseModel):
    candidate_id: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    position: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    class Config:
        from_attributes = True

class ResumeResponse(BaseModel):
    id: int
    candidate_id: str
    filename: str
    original_filename: str
    file_size: int
    content_type: str
    is_visible: bool
    uploaded_at: datetime
    updated_at: datetime
    class Config:
        from_attributes = True

# 📋 NOTICE PERIOD & PAYSLIP MODELS
class NoticePeriodCreate(BaseModel):
    notice_period_days: int = 30
    current_company: Optional[str] = None
    current_position: Optional[str] = None
    current_salary: Optional[int] = None
    expected_salary: Optional[int] = None

class NoticePeriodResponse(BaseModel):
    id: int
    candidate_id: str
    notice_period_days: int
    current_company: Optional[str] = None
    current_position: Optional[str] = None
    current_salary: Optional[int] = None
    expected_salary: Optional[int] = None
    created_at: datetime
    updated_at: datetime
    class Config:
        from_attributes = True

class PayslipResponse(BaseModel):
    id: int
    candidate_id: str
    filename: str
    original_filename: str
    file_size: int
    content_type: str
    month_year: str
    salary_amount: Optional[int] = None
    created_at: datetime
    class Config:
        from_attributes = True

# 🛡️ BACKGROUND VERIFICATION MODELS
class BackgroundVerificationCreate(BaseModel):
    status: str = "In Progress"
    verification_notes: Optional[str] = None
    employment_verified: bool = False
    education_verified: bool = False
    address_verified: bool = False

class BackgroundVerificationResponse(BaseModel):
    id: int
    candidate_id: str
    status: str
    verification_notes: Optional[str] = None
    employment_verified: bool
    education_verified: bool
    address_verified: bool
    created_at: datetime
    updated_at: datetime
    class Config:
        from_attributes = True

class AddressProofResponse(BaseModel):
    id: int
    candidate_id: str  # Added for easier analysis and identification
    document_type: str
    filename: str
    original_filename: str
    file_size: int
    content_type: str
    is_verified: bool
    digilocker_verified: bool
    digilocker_document_id: Optional[str] = None
    verification_status: str
    verification_details: Optional[dict] = None
    verified_at: Optional[datetime] = None
    created_at: datetime
    class Config:
        from_attributes = True

class ProvidentFundCreate(BaseModel):
    account_number: Optional[str] = None
    uan_number: Optional[str] = None
    previous_employer: Optional[str] = None
    current_employer: Optional[str] = None
    additional_details: Optional[str] = None

class ProvidentFundResponse(BaseModel):
    id: int
    account_number: Optional[str] = None
    uan_number: Optional[str] = None
    previous_employer: Optional[str] = None
    current_employer: Optional[str] = None
    additional_details: Optional[str] = None
    is_verified: bool
    created_at: datetime
    updated_at: datetime
    class Config:
        from_attributes = True

# 📅 CALENDAR-BASED AVAILABILITY MODELS
class TimeSlotCreate(BaseModel):
    interview_date: str  # DD/MM/YYYY format
    start_time: str      # HH:MM format (24-hour or 12-hour with AM/PM)
    end_time: str        # HH:MM format (24-hour or 12-hour with AM/PM)

class AvailabilitySlotCreate(BaseModel):
    slot_type: str = "general"  # 'general' or 'job_specific'
    slots: List[TimeSlotCreate]
    company_name: Optional[str] = None  # Required for job_specific
    job_position: Optional[str] = None  # Required for job_specific

class AvailabilitySlotResponse(BaseModel):
    id: int
    candidate_id: str
    slot_type: str
    interview_date: str  # DD/MM/YYYY format
    start_time: str      # HH:MM format
    end_time: str        # HH:MM format
    is_booked: bool
    company_name: Optional[str] = None
    job_position: Optional[str] = None
    created_at: datetime
    class Config:
        from_attributes = True

class InterviewBookingCreate(BaseModel):
    company_name: str
    job_position: str
    interview_date: str  # DD/MM/YYYY format

class InterviewBookingResponse(BaseModel):
    id: int
    slot_id: int
    candidate_id: str
    company_name: str
    job_position: str
    interview_date: str  # DD/MM/YYYY format
    start_time: str      # HH:MM format
    end_time: str        # HH:MM format
    booking_status: str
    created_at: datetime
    updated_at: datetime
    class Config:
        from_attributes = True

# 🔐 DIGILOCKER AUTHENTICATION MODELS
class DigiLockerAuthResponse(BaseModel):
    id: int
    candidate_id: str
    auth_status: str
    digilocker_user_id: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    class Config:
        from_attributes = True

class DigiLockerDocumentResponse(BaseModel):
    id: int
    candidate_id: str
    document_type: str
    document_id: str
    document_name: str
    issuer: Optional[str] = None
    issue_date: Optional[date] = None
    expiry_date: Optional[date] = None
    verification_status: str
    document_data: Optional[dict] = None
    created_at: datetime
    updated_at: datetime
    class Config:
        from_attributes = True

class DigiLockerAuthRequest(BaseModel):
    candidate_id: str

class DigiLockerCallbackRequest(BaseModel):
    code: str
    state: str

# ============================================================================
# 📄 EMPLOYMENT DOCUMENT SCHEMAS
# ============================================================================

class OfferLetterResponse(BaseModel):
    id: int
    candidate_id: str
    company_name: str
    job_position: str
    filename: str
    original_filename: str
    file_size: int
    content_type: str
    upload_status: str
    notes: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class ContractAgreementResponse(BaseModel):
    id: int
    candidate_id: str
    company_name: str
    job_position: str
    filename: str
    original_filename: str
    file_size: int
    content_type: str
    upload_status: str
    notes: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class SalaryNegotiationTermsResponse(BaseModel):
    id: int
    candidate_id: str
    company_name: str
    job_position: str
    filename: str
    original_filename: str
    file_size: int
    content_type: str
    upload_status: str
    notes: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# ============================================================================
# 🆕 NEW COMPREHENSIVE SCHEMAS
# ============================================================================

# --- 1. Role Schemas ---
class RoleCreate(BaseModel):
    name: str
    description: Optional[str] = None

class RoleResponse(BaseModel):
    role_id: int
    name: str
    description: Optional[str] = None
    created_at: datetime

    class Config:
        from_attributes = True

# --- 2. User Schemas ---
class UserCreate(BaseModel):
    name: str
    initials: Optional[str] = None
    email: str
    password_hash: str
    phone_number: Optional[str] = None
    role_id: int

class UserResponse(BaseModel):
    user_id: int
    name: str
    initials: Optional[str] = None
    email: str
    phone_number: Optional[str] = None
    role_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class UserUpdate(BaseModel):
    name: Optional[str] = None
    initials: Optional[str] = None
    email: Optional[str] = None
    phone_number: Optional[str] = None
    role_id: Optional[int] = None

# --- 3. User Profile Schemas ---
class UserProfileCreate(BaseModel):
    job_title: Optional[str] = None
    status: Optional[str] = None
    profile_strength: Optional[float] = None
    profile_completion: Optional[float] = None
    work_experience_status: Optional[str] = None
    portfolio_status: Optional[str] = None
    basic_info_status: Optional[str] = None
    resume_status: Optional[str] = None
    skills_status: Optional[str] = None
    location: Optional[str] = None
    profile_score: Optional[float] = None
    experience_level: Optional[float] = None
    skill_count: Optional[int] = None
    education_level: Optional[str] = None
    overall_competitiveness: Optional[str] = None
    expected_salary: Optional[str] = None
    availability: Optional[str] = None
    source: Optional[str] = None

class UserProfileResponse(BaseModel):
    profile_id: int
    user_id: int
    job_title: Optional[str] = None
    status: Optional[str] = None
    profile_strength: Optional[float] = None
    profile_completion: Optional[float] = None
    work_experience_status: Optional[str] = None
    portfolio_status: Optional[str] = None
    basic_info_status: Optional[str] = None
    resume_status: Optional[str] = None
    skills_status: Optional[str] = None
    location: Optional[str] = None
    profile_score: Optional[float] = None
    experience_level: Optional[float] = None
    skill_count: Optional[int] = None
    education_level: Optional[str] = None
    overall_competitiveness: Optional[str] = None
    expected_salary: Optional[str] = None
    availability: Optional[str] = None
    source: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# --- 4. Resume Schemas (New) ---
class ResumeNewCreate(BaseModel):
    user_id: int
    candidate_id: int
    job_id: Optional[int] = None
    file_url: str
    resume_url: Optional[str] = None
    parsed_data: Optional[str] = None
    is_active: bool = True
    version: int = 1
    activity_type: Optional[str] = None
    file_name: Optional[str] = None
    file_type: Optional[str] = None
    file_size_limit: Optional[str] = None
    ai_resume_generation_status: Optional[str] = None
    ai_resume_time_estimate: Optional[str] = None

class ResumeNewResponse(BaseModel):
    resume_id: int
    user_id: int
    candidate_id: int
    job_id: Optional[int] = None
    file_url: str
    resume_url: Optional[str] = None
    parsed_data: Optional[str] = None
    upload_date: datetime
    is_active: bool
    version: int
    activity_type: Optional[str] = None
    file_name: Optional[str] = None
    file_type: Optional[str] = None
    file_size_limit: Optional[str] = None
    ai_resume_generation_status: Optional[str] = None
    ai_resume_time_estimate: Optional[str] = None
    created_at: datetime

    class Config:
        from_attributes = True

# --- 5. Job Description Schemas ---
class JobDescriptionCreate(BaseModel):
    title: str
    company_name: Optional[str] = None
    location: Optional[str] = None
    description: str
    raw_text: Optional[str] = None
    keywords: Optional[str] = None
    status: str = "Active"
    department: Optional[str] = None
    required_skills: Optional[str] = None
    experience_level: Optional[str] = None
    education_requirements: Optional[str] = None
    threshold_score: Optional[float] = None
    source_file: Optional[str] = None
    salary_range: Optional[str] = None
    source: Optional[str] = None
    posted_date: Optional[datetime] = None
    applications_received: Optional[int] = None

class JobDescriptionResponse(BaseModel):
    job_id: int
    title: str
    company_name: Optional[str] = None
    location: Optional[str] = None
    description: str
    raw_text: Optional[str] = None
    keywords: Optional[str] = None
    status: str
    department: Optional[str] = None
    required_skills: Optional[str] = None
    experience_level: Optional[str] = None
    education_requirements: Optional[str] = None
    threshold_score: Optional[float] = None
    source_file: Optional[str] = None
    salary_range: Optional[str] = None
    source: Optional[str] = None
    posted_date: Optional[datetime] = None
    applications_received: Optional[int] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
