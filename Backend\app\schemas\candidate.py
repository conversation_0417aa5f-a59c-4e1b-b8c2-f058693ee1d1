"""
Candidate-related Pydantic schemas
"""

from datetime import datetime, date
from typing import Optional, List
from pydantic import BaseModel

class CandidateCreate(BaseModel):
    candidate_id: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    position: Optional[str] = None

class CandidateResponse(BaseModel):
    candidate_id: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    position: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    class Config:
        from_attributes = True

class ResumeResponse(BaseModel):
    id: int
    candidate_id: str
    filename: str
    original_filename: str
    file_size: int
    content_type: str
    is_visible: bool
    uploaded_at: datetime
    updated_at: datetime
    class Config:
        from_attributes = True

# 📋 NOTICE PERIOD & PAYSLIP MODELS
class NoticePeriodCreate(BaseModel):
    notice_period_days: int = 30
    current_company: Optional[str] = None
    current_position: Optional[str] = None
    current_salary: Optional[int] = None
    expected_salary: Optional[int] = None

class NoticePeriodResponse(BaseModel):
    id: int
    candidate_id: str
    notice_period_days: int
    current_company: Optional[str] = None
    current_position: Optional[str] = None
    current_salary: Optional[int] = None
    expected_salary: Optional[int] = None
    created_at: datetime
    updated_at: datetime
    class Config:
        from_attributes = True

class PayslipResponse(BaseModel):
    id: int
    candidate_id: str
    filename: str
    original_filename: str
    file_size: int
    content_type: str
    month_year: str
    salary_amount: Optional[int] = None
    created_at: datetime
    class Config:
        from_attributes = True

# 🛡️ BACKGROUND VERIFICATION MODELS
class BackgroundVerificationCreate(BaseModel):
    status: str = "In Progress"
    verification_notes: Optional[str] = None
    employment_verified: bool = False
    education_verified: bool = False
    address_verified: bool = False

class BackgroundVerificationResponse(BaseModel):
    id: int
    candidate_id: str
    status: str
    verification_notes: Optional[str] = None
    employment_verified: bool
    education_verified: bool
    address_verified: bool
    created_at: datetime
    updated_at: datetime
    class Config:
        from_attributes = True

class AddressProofResponse(BaseModel):
    id: int
    candidate_id: str  # Added for easier analysis and identification
    document_type: str
    filename: str
    original_filename: str
    file_size: int
    content_type: str
    is_verified: bool
    digilocker_verified: bool
    digilocker_document_id: Optional[str] = None
    verification_status: str
    verification_details: Optional[dict] = None
    verified_at: Optional[datetime] = None
    created_at: datetime
    class Config:
        from_attributes = True

class ProvidentFundCreate(BaseModel):
    account_number: Optional[str] = None
    uan_number: Optional[str] = None
    previous_employer: Optional[str] = None
    current_employer: Optional[str] = None
    additional_details: Optional[str] = None

class ProvidentFundResponse(BaseModel):
    id: int
    account_number: Optional[str] = None
    uan_number: Optional[str] = None
    previous_employer: Optional[str] = None
    current_employer: Optional[str] = None
    additional_details: Optional[str] = None
    is_verified: bool
    created_at: datetime
    updated_at: datetime
    class Config:
        from_attributes = True

# 📅 CALENDAR-BASED AVAILABILITY MODELS
class TimeSlotCreate(BaseModel):
    interview_date: str  # DD/MM/YYYY format
    start_time: str      # HH:MM format (24-hour or 12-hour with AM/PM)
    end_time: str        # HH:MM format (24-hour or 12-hour with AM/PM)

class AvailabilitySlotCreate(BaseModel):
    slot_type: str = "general"  # 'general' or 'job_specific'
    slots: List[TimeSlotCreate]
    company_name: Optional[str] = None  # Required for job_specific
    job_position: Optional[str] = None  # Required for job_specific

class AvailabilitySlotResponse(BaseModel):
    id: int
    candidate_id: str
    slot_type: str
    interview_date: str  # DD/MM/YYYY format
    start_time: str      # HH:MM format
    end_time: str        # HH:MM format
    is_booked: bool
    company_name: Optional[str] = None
    job_position: Optional[str] = None
    created_at: datetime
    class Config:
        from_attributes = True

class InterviewBookingCreate(BaseModel):
    company_name: str
    job_position: str
    interview_date: str  # DD/MM/YYYY format

class InterviewBookingResponse(BaseModel):
    id: int
    slot_id: int
    candidate_id: str
    company_name: str
    job_position: str
    interview_date: str  # DD/MM/YYYY format
    start_time: str      # HH:MM format
    end_time: str        # HH:MM format
    booking_status: str
    created_at: datetime
    updated_at: datetime
    class Config:
        from_attributes = True

# 🔐 DIGILOCKER AUTHENTICATION MODELS
class DigiLockerAuthResponse(BaseModel):
    id: int
    candidate_id: str
    auth_status: str
    digilocker_user_id: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    class Config:
        from_attributes = True

class DigiLockerDocumentResponse(BaseModel):
    id: int
    candidate_id: str
    document_type: str
    document_id: str
    document_name: str
    issuer: Optional[str] = None
    issue_date: Optional[date] = None
    expiry_date: Optional[date] = None
    verification_status: str
    document_data: Optional[dict] = None
    created_at: datetime
    updated_at: datetime
    class Config:
        from_attributes = True

class DigiLockerAuthRequest(BaseModel):
    candidate_id: str

class DigiLockerCallbackRequest(BaseModel):
    code: str
    state: str
