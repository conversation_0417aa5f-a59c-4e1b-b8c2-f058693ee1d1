"""
Availability and Interview Booking Routes
"""

import logging
from datetime import datetime, date, time
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.database.connection import get_db
from app.models.candidate import Candidate
from app.schemas.availability import (
    TimeSlotCreate, AvailabilitySlotCreate, AvailabilitySlotResponse,
    InterviewBookingCreate, InterviewBookingResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()

# ============================================================================
# 📅 AVAILABILITY MANAGEMENT ROUTES
# ============================================================================

def parse_date(date_str: str) -> date:
    """Parse DD/MM/YYYY format to date object"""
    try:
        return datetime.strptime(date_str, "%d/%m/%Y").date()
    except ValueError:
        raise HTTPException(status_code=400, detail=f"Invalid date format: {date_str}. Use DD/MM/YYYY")

def parse_time(time_str: str) -> time:
    """Parse HH:MM format to time object"""
    try:
        # Clean the time string
        clean_time = str(time_str).strip()

        # Debug logging
        logger.info(f"🕐 Parsing time: '{clean_time}' (type: {type(time_str)})")

        # Ensure we have HH:MM format
        if ':' not in clean_time:
            raise ValueError(f"Time must contain ':' separator")

        # Split and validate
        parts = clean_time.split(':')
        if len(parts) != 2:
            raise ValueError(f"Time must be in HH:MM format")

        hour, minute = parts
        hour = int(hour)
        minute = int(minute)

        # Validate ranges
        if not (0 <= hour <= 23):
            raise ValueError(f"Hour must be between 0-23")
        if not (0 <= minute <= 59):
            raise ValueError(f"Minute must be between 0-59")

        # Create time object
        return time(hour, minute)

    except (ValueError, TypeError) as e:
        logger.error(f"❌ Time parsing error for '{time_str}': {e}")
        raise HTTPException(status_code=400, detail=f"Invalid time format: '{time_str}'. Use HH:MM format (e.g., 09:00, 14:30)")

def format_date_for_response(date_obj: date) -> str:
    """Format date object to DD/MM/YYYY string"""
    return date_obj.strftime("%d/%m/%Y")

def format_time_for_response(time_obj: time) -> str:
    """Format time object to HH:MM string"""
    return time_obj.strftime("%H:%M")

@router.get("/candidates/{candidate_id}/availability")
async def get_all_availability_slots(candidate_id: str, db: Session = Depends(get_db)):
    """📅 Get all availability slots (general + job-specific) for a candidate"""
    try:
        # Import models here to avoid circular imports
        from app.models.candidate import AvailabilitySlot, ShortlistedJobAvailability
        
        # Check if candidate exists
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        # Get general availability slots
        general_slots = db.query(AvailabilitySlot).filter(
            AvailabilitySlot.candidate_id == candidate_id
        ).order_by(AvailabilitySlot.interview_date, AvailabilitySlot.start_time).all()

        # Get shortlisted job availability slots
        job_slots = db.query(ShortlistedJobAvailability).filter(
            ShortlistedJobAvailability.candidate_id == candidate_id
        ).order_by(ShortlistedJobAvailability.interview_date, ShortlistedJobAvailability.start_time).all()

        availability_data = []

        # Add general slots
        for slot in general_slots:
            availability_data.append({
                "id": slot.id,
                "candidate_id": slot.candidate_id,
                "slot_type": "general",
                "interview_date": format_date_for_response(slot.interview_date),
                "start_time": format_time_for_response(slot.start_time),
                "end_time": format_time_for_response(slot.end_time),
                "is_booked": slot.is_booked,
                "company_name": None,
                "job_position": None,
                "created_at": slot.created_at
            })

        # Add shortlisted job slots
        for slot in job_slots:
            availability_data.append({
                "id": slot.id,
                "candidate_id": slot.candidate_id,
                "slot_type": "job_specific",
                "interview_date": format_date_for_response(slot.interview_date),
                "start_time": format_time_for_response(slot.start_time),
                "end_time": format_time_for_response(slot.end_time),
                "is_booked": slot.is_booked,
                "company_name": slot.company_name,
                "job_position": slot.job_position,
                "created_at": slot.created_at
            })

        logger.info(f"📅 Retrieved {len(availability_data)} availability slots for {candidate_id}")
        return availability_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Error fetching availability slots for {candidate_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch availability slots: {str(e)}")

@router.post("/candidates/{candidate_id}/availability/general", status_code=status.HTTP_201_CREATED)
async def add_general_availability(
    candidate_id: str,
    availability_input: AvailabilitySlotCreate,
    db: Session = Depends(get_db)
):
    """📅 Add general availability slots with real calendar dates"""
    try:
        from app.models.candidate import AvailabilitySlot
        
        # Check if candidate exists
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        logger.info(f"📅 Adding {len(availability_input.slots)} general slots for {candidate_id}")

        created_slots = []
        skipped_slots = []

        for i, slot_data in enumerate(availability_input.slots):
            logger.info(f"🔄 Processing slot {i+1}/{len(availability_input.slots)}: {slot_data.interview_date} {slot_data.start_time}-{slot_data.end_time}")

            try:
                # Parse date and times with detailed logging
                logger.info(f"📅 Parsing date: '{slot_data.interview_date}'")
                interview_date = parse_date(slot_data.interview_date)
                logger.info(f"✅ Date parsed: {interview_date}")

                logger.info(f"🕐 Parsing start time: '{slot_data.start_time}'")
                start_time = parse_time(slot_data.start_time)
                logger.info(f"✅ Start time parsed: {start_time}")

                logger.info(f"🕐 Parsing end time: '{slot_data.end_time}'")
                end_time = parse_time(slot_data.end_time)
                logger.info(f"✅ End time parsed: {end_time}")

                # Validate time range
                if start_time >= end_time:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Start time {slot_data.start_time} must be before end time {slot_data.end_time}"
                    )

                # Check for existing slot conflicts
                logger.info(f"🔍 Checking for existing slots...")
                existing_slot = db.query(AvailabilitySlot).filter(
                    AvailabilitySlot.candidate_id == candidate_id,
                    AvailabilitySlot.interview_date == interview_date,
                    AvailabilitySlot.start_time == start_time,
                    AvailabilitySlot.end_time == end_time
                ).first()

                if existing_slot:
                    logger.warning(f"⚠️ Slot already exists for {candidate_id} on {slot_data.interview_date} {slot_data.start_time}-{slot_data.end_time}")
                    skipped_slots.append(f"{slot_data.interview_date} {slot_data.start_time}-{slot_data.end_time}")
                    continue

                # Create new availability slot
                logger.info(f"➕ Creating new slot...")
                new_slot = AvailabilitySlot(
                    candidate_id=candidate_id,
                    slot_type="general",
                    interview_date=interview_date,
                    start_time=start_time,
                    end_time=end_time,
                    is_booked=False
                )

                db.add(new_slot)
                created_slots.append(new_slot)
                logger.info(f"✅ Slot added to session: {slot_data.interview_date} {slot_data.start_time}-{slot_data.end_time}")

            except Exception as slot_error:
                logger.error(f"❌ Error processing slot {i+1}: {slot_error}")
                raise HTTPException(status_code=400, detail=f"Error processing slot {i+1}: {str(slot_error)}")

        # Commit the transaction
        logger.info(f"💾 Committing {len(created_slots)} slots to database...")
        db.commit()
        logger.info(f"✅ Database commit successful!")

        # Prepare response message
        message_parts = []
        if created_slots:
            message_parts.append(f"Added {len(created_slots)} general availability slots")
        if skipped_slots:
            message_parts.append(f"Skipped {len(skipped_slots)} existing slots")

        response_message = "; ".join(message_parts) if message_parts else "No slots processed"

        logger.info(f"✅ Final result for {candidate_id}: {len(created_slots)} created, {len(skipped_slots)} skipped")

        response = {
            "message": response_message,
            "candidate_id": candidate_id,
            "slots_created": len(created_slots),
            "slots_skipped": len(skipped_slots),
            "total_requested": len(availability_input.slots)
        }

        if skipped_slots:
            response["skipped_slots"] = skipped_slots

        return response

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"❌ Error adding general availability for {candidate_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to add general availability: {str(e)}")

@router.post("/candidates/{candidate_id}/availability/job-specific", status_code=status.HTTP_201_CREATED)
async def add_shortlisted_job_availability(
    candidate_id: str,
    availability_input: AvailabilitySlotCreate,
    db: Session = Depends(get_db)
):
    """📅 Add shortlisted job availability slots with real calendar dates"""
    try:
        from app.models.candidate import ShortlistedJobAvailability
        
        # Check if candidate exists
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        # Validate job-specific requirements
        if not availability_input.company_name or not availability_input.job_position:
            raise HTTPException(
                status_code=400,
                detail="Company name and job position are required for shortlisted job availability"
            )

        logger.info(f"📅 Adding {len(availability_input.slots)} shortlisted job slots for {candidate_id} - {availability_input.company_name}")

        created_slots = []
        for slot_data in availability_input.slots:
            # Parse date and times
            interview_date = parse_date(slot_data.interview_date)
            start_time = parse_time(slot_data.start_time)
            end_time = parse_time(slot_data.end_time)

            # Validate time range
            if start_time >= end_time:
                raise HTTPException(
                    status_code=400,
                    detail=f"Start time {slot_data.start_time} must be before end time {slot_data.end_time}"
                )

            # Check for existing slot conflicts
            existing_slot = db.query(ShortlistedJobAvailability).filter(
                ShortlistedJobAvailability.candidate_id == candidate_id,
                ShortlistedJobAvailability.company_name == availability_input.company_name,
                ShortlistedJobAvailability.job_position == availability_input.job_position,
                ShortlistedJobAvailability.interview_date == interview_date,
                ShortlistedJobAvailability.start_time == start_time,
                ShortlistedJobAvailability.end_time == end_time
            ).first()

            if existing_slot:
                logger.warning(f"⚠️ Job-specific slot already exists for {candidate_id} - {availability_input.company_name} on {slot_data.interview_date}")
                continue

            # Create new shortlisted job availability slot
            new_slot = ShortlistedJobAvailability(
                candidate_id=candidate_id,
                company_name=availability_input.company_name,
                job_position=availability_input.job_position,
                interview_date=interview_date,
                start_time=start_time,
                end_time=end_time,
                is_booked=False
            )

            db.add(new_slot)
            created_slots.append(new_slot)

        db.commit()

        logger.info(f"✅ Created {len(created_slots)} shortlisted job slots for {candidate_id}")
        return {
            "message": f"Added {len(created_slots)} shortlisted job availability slots",
            "candidate_id": candidate_id,
            "company_name": availability_input.company_name,
            "job_position": availability_input.job_position,
            "slots_created": len(created_slots)
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"❌ Error adding shortlisted job availability for {candidate_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to add shortlisted job availability: {str(e)}")

@router.get("/candidates/{candidate_id}/availability/general", response_model=List[AvailabilitySlotResponse])
async def get_general_availability(candidate_id: str, db: Session = Depends(get_db)):
    """📅 Get all general availability slots for a candidate"""
    try:
        from app.models.candidate import AvailabilitySlot
        
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        slots = db.query(AvailabilitySlot).filter(
            AvailabilitySlot.candidate_id == candidate_id,
            AvailabilitySlot.slot_type == "general"
        ).order_by(AvailabilitySlot.interview_date, AvailabilitySlot.start_time).all()

        return [
            AvailabilitySlotResponse(
                id=slot.id,
                candidate_id=slot.candidate_id,
                slot_type=slot.slot_type,
                interview_date=format_date_for_response(slot.interview_date),
                start_time=format_time_for_response(slot.start_time),
                end_time=format_time_for_response(slot.end_time),
                is_booked=slot.is_booked,
                company_name=None,
                job_position=None,
                created_at=slot.created_at
            )
            for slot in slots
        ]

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Error fetching general availability for {candidate_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch general availability: {str(e)}")

@router.delete("/candidates/{candidate_id}/availability/{slot_id}", status_code=status.HTTP_200_OK)
async def delete_availability_slot(candidate_id: str, slot_id: int, db: Session = Depends(get_db)):
    """🗑️ Delete an availability slot"""
    try:
        from app.models.candidate import AvailabilitySlot, ShortlistedJobAvailability

        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        # Check if slot exists in general availability
        general_slot = db.query(AvailabilitySlot).filter(
            AvailabilitySlot.id == slot_id,
            AvailabilitySlot.candidate_id == candidate_id
        ).first()

        # Check if slot exists in shortlisted job availability
        job_slot = db.query(ShortlistedJobAvailability).filter(
            ShortlistedJobAvailability.id == slot_id,
            ShortlistedJobAvailability.candidate_id == candidate_id
        ).first()

        if not general_slot and not job_slot:
            raise HTTPException(status_code=404, detail=f"Availability slot {slot_id} not found for candidate {candidate_id}")

        # Check if slot is booked
        slot_to_delete = general_slot or job_slot
        if slot_to_delete.is_booked:
            raise HTTPException(status_code=400, detail=f"Cannot delete booked slot {slot_id}")

        # Delete the slot
        if general_slot:
            db.delete(general_slot)
            slot_type = "general"
        else:
            db.delete(job_slot)
            slot_type = "job_specific"

        db.commit()

        logger.info(f"✅ Deleted {slot_type} availability slot {slot_id} for {candidate_id}")
        return {
            "message": f"Deleted {slot_type} availability slot",
            "candidate_id": candidate_id,
            "slot_id": slot_id
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"❌ Error deleting availability slot {slot_id} for {candidate_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete availability slot: {str(e)}")

# ============================================================================
# 📅 INTERVIEW BOOKING ROUTES
# ============================================================================

@router.post("/candidates/{candidate_id}/availability/book-slot", response_model=InterviewBookingResponse, status_code=status.HTTP_201_CREATED)
async def book_interview_slot(
    candidate_id: str,
    slot_id: int,
    booking_input: InterviewBookingCreate,
    db: Session = Depends(get_db)
):
    """📅 Book an interview slot with company conflict detection"""
    try:
        from app.models.candidate import AvailabilitySlot, InterviewBooking

        # Check if candidate exists
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        # Check if slot exists and is available
        slot = db.query(AvailabilitySlot).filter(
            AvailabilitySlot.id == slot_id,
            AvailabilitySlot.candidate_id == candidate_id
        ).first()

        if not slot:
            raise HTTPException(status_code=404, detail=f"Availability slot {slot_id} not found for candidate {candidate_id}")

        if slot.is_booked:
            raise HTTPException(status_code=400, detail=f"Slot {slot_id} is already booked")

        # Parse the booking date
        booking_date = parse_date(booking_input.interview_date)

        # Validate that booking date matches slot date
        if booking_date != slot.interview_date:
            raise HTTPException(
                status_code=400,
                detail=f"Booking date {booking_input.interview_date} does not match slot date {format_date_for_response(slot.interview_date)}"
            )

        # Check for company conflicts on the same date
        existing_booking = db.query(InterviewBooking).filter(
            InterviewBooking.candidate_id == candidate_id,
            InterviewBooking.company_name == booking_input.company_name,
            InterviewBooking.interview_date == booking_date,
            InterviewBooking.booking_status == "confirmed"
        ).first()

        if existing_booking:
            raise HTTPException(
                status_code=400,
                detail=f"Already have a confirmed interview with {booking_input.company_name} on {booking_input.interview_date}"
            )

        # Create the booking
        booking = InterviewBooking(
            slot_id=slot_id,
            candidate_id=candidate_id,
            company_name=booking_input.company_name,
            job_position=booking_input.job_position,
            interview_date=booking_date,
            start_time=slot.start_time,
            end_time=slot.end_time,
            booking_status="confirmed"
        )

        # Mark slot as booked
        slot.is_booked = True

        db.add(booking)
        db.commit()
        db.refresh(booking)

        logger.info(f"✅ Booked interview: {booking_input.company_name} - {candidate_id} on {booking_input.interview_date} {format_time_for_response(slot.start_time)}-{format_time_for_response(slot.end_time)}")

        return InterviewBookingResponse(
            id=booking.id,
            slot_id=booking.slot_id,
            candidate_id=booking.candidate_id,
            company_name=booking.company_name,
            job_position=booking.job_position,
            interview_date=format_date_for_response(booking.interview_date),
            start_time=format_time_for_response(booking.start_time),
            end_time=format_time_for_response(booking.end_time),
            booking_status=booking.booking_status,
            created_at=booking.created_at,
            updated_at=booking.updated_at
        )

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"❌ Error booking interview slot for {candidate_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to book interview slot: {str(e)}")

@router.get("/candidates/{candidate_id}/bookings", response_model=List[InterviewBookingResponse])
async def get_candidate_bookings(candidate_id: str, db: Session = Depends(get_db)):
    """📅 Get all interview bookings for a candidate"""
    try:
        from app.models.candidate import InterviewBooking

        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        bookings = db.query(InterviewBooking).filter(
            InterviewBooking.candidate_id == candidate_id
        ).order_by(InterviewBooking.interview_date, InterviewBooking.start_time).all()

        return [
            InterviewBookingResponse(
                id=booking.id,
                slot_id=booking.slot_id,
                candidate_id=booking.candidate_id,
                company_name=booking.company_name,
                job_position=booking.job_position,
                interview_date=format_date_for_response(booking.interview_date),
                start_time=format_time_for_response(booking.start_time),
                end_time=format_time_for_response(booking.end_time),
                booking_status=booking.booking_status,
                created_at=booking.created_at,
                updated_at=booking.updated_at
            )
            for booking in bookings
        ]

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Error fetching bookings for {candidate_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch bookings: {str(e)}")
