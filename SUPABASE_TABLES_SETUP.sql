-- 🗄️ Supabase Tables Setup for DigiLocker Integration
-- Run these SQL commands in your Supabase SQL Editor

-- 1. Create address_proofs table for document uploads
CREATE TABLE IF NOT EXISTS address_proofs (
    id SERIAL PRIMARY KEY,
    candidate_id VARCHAR(50) NOT NULL,
    document_type VARCHAR(100) NOT NULL,
    filename VA<PERSON>HAR(255) NOT NULL,
    original_filename VA<PERSON>HAR(255) NOT NULL,
    file_size INTEGER,
    content_type VARCHAR(100),
    is_verified BOOLEAN DEFAULT FALSE,
    digilocker_verified BOOLEAN DEFAULT FALSE,
    digilocker_document_id VARCHAR(100),
    verification_status VARCHAR(20) DEFAULT 'pending',
    verification_details JSONB,
    verified_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    synced_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. Create digilocker_auth table for authentication tracking
CREATE TABLE IF NOT EXISTS digilocker_auth (
    candidate_id VARCHAR(50) PRIMARY KEY,
    auth_status VARCHAR(20) NOT NULL DEFAULT 'pending',
    digilocker_user_id VARCHAR(50),
    access_token TEXT,
    token_expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    synced_at TIMESTAMPTZ DEFAULT NOW()
);

-- 3. Create document_verifications table for verification audit trail
CREATE TABLE IF NOT EXISTS document_verifications (
    id SERIAL PRIMARY KEY,
    candidate_id VARCHAR(50) NOT NULL,
    document_id INTEGER REFERENCES address_proofs(id),
    document_type VARCHAR(100) NOT NULL,
    verification_status VARCHAR(20) NOT NULL,
    digilocker_verified BOOLEAN DEFAULT FALSE,
    verification_details JSONB,
    verified_at TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 4. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_address_proofs_candidate_id ON address_proofs(candidate_id);
CREATE INDEX IF NOT EXISTS idx_address_proofs_document_type ON address_proofs(document_type);
CREATE INDEX IF NOT EXISTS idx_address_proofs_created_at ON address_proofs(created_at);
CREATE INDEX IF NOT EXISTS idx_digilocker_auth_candidate_id ON digilocker_auth(candidate_id);
CREATE INDEX IF NOT EXISTS idx_digilocker_auth_status ON digilocker_auth(auth_status);
CREATE INDEX IF NOT EXISTS idx_document_verifications_candidate_id ON document_verifications(candidate_id);

-- 5. Enable Row Level Security (RLS) for better security
ALTER TABLE address_proofs ENABLE ROW LEVEL SECURITY;
ALTER TABLE digilocker_auth ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_verifications ENABLE ROW LEVEL SECURITY;

-- 6. Create policies for authenticated users (adjust as needed)
CREATE POLICY "Allow authenticated users to view address_proofs" ON address_proofs
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated users to insert address_proofs" ON address_proofs
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated users to update address_proofs" ON address_proofs
    FOR UPDATE USING (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated users to view digilocker_auth" ON digilocker_auth
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated users to insert digilocker_auth" ON digilocker_auth
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated users to update digilocker_auth" ON digilocker_auth
    FOR UPDATE USING (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated users to view document_verifications" ON document_verifications
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated users to insert document_verifications" ON document_verifications
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- 7. Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 8. Create triggers for updated_at columns
CREATE TRIGGER update_address_proofs_updated_at BEFORE UPDATE ON address_proofs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_digilocker_auth_updated_at BEFORE UPDATE ON digilocker_auth
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 9. Grant necessary permissions (adjust as needed)
GRANT ALL ON address_proofs TO authenticated;
GRANT ALL ON digilocker_auth TO authenticated;
GRANT ALL ON document_verifications TO authenticated;
GRANT USAGE ON SEQUENCE address_proofs_id_seq TO authenticated;
GRANT USAGE ON SEQUENCE document_verifications_id_seq TO authenticated;
