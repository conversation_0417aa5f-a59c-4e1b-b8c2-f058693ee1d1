"""
Validation utilities
"""

from datetime import datetime, date, time
from fastapi import HTTPException
from sqlalchemy.orm import Session

from app.utils.enums import DocumentType, MAX_PAYSLIPS_PER_CANDIDATE
from app.models.candidate import Payslip, InterviewBooking

try:
    from dateutil.relativedelta import relativedelta
except ImportError:
    relativedelta = None

def validate_document_type(document_type: str) -> bool:
    """Validate document type for address proof - Dynamic validation"""
    allowed_types = DocumentType.get_allowed_types()

    if not document_type or document_type.strip() == "":
        raise HTTPException(
            status_code=400,
            detail="Document type is required"
        )

    if document_type not in allowed_types:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid document type '{document_type}'. Allowed types: {', '.join(sorted(set(allowed_types)))}"
        )
    return True

def validate_payslip_count(db: Session, candidate_id: str, month_year: str) -> bool:
    """Validate that candidate doesn't exceed 6 payslips limit"""
    existing_payslips = db.query(Payslip).filter(
        Payslip.candidate_id == candidate_id
    ).all()

    # Check if this month_year already exists (replacement is allowed)
    existing_month = db.query(Payslip).filter(
        Payslip.candidate_id == candidate_id,
        Payslip.month_year == month_year
    ).first()

    if existing_month:
        return True  # Replacement is allowed

    if len(existing_payslips) >= MAX_PAYSLIPS_PER_CANDIDATE:
        raise HTTPException(
            status_code=400,
            detail="Maximum 6 payslips allowed. Please delete an old payslip before uploading a new one."
        )

    return True

def validate_month_year(month_year: str) -> bool:
    """Validate MM/YYYY format and ensure it's within last 6 months"""
    try:
        payslip_date = datetime.strptime(month_year, "%m/%Y")
        current_date = datetime.now()

        # Calculate 6 months ago using timedelta for better accuracy
        if relativedelta:
            six_months_ago = current_date - relativedelta(months=6)
        else:
            # Fallback calculation
            current_month = current_date.month
            current_year = current_date.year

            if current_month > 6:
                six_months_ago_month = current_month - 6
                six_months_ago_year = current_year
            else:
                six_months_ago_month = current_month + 6
                six_months_ago_year = current_year - 1

            six_months_ago = datetime(six_months_ago_year, six_months_ago_month, 1)

        if payslip_date < six_months_ago:
            raise HTTPException(
                status_code=400,
                detail=f"Payslip date '{month_year}' is older than 6 months. Please upload payslips from the last 6 months only."
            )

        if payslip_date > current_date:
            raise HTTPException(
                status_code=400,
                detail=f"Payslip date '{month_year}' is in the future. Please use a valid past date."
            )

        return True
    except ValueError:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid month/year format '{month_year}'. Use MM/YYYY format (e.g., 06/2025)"
        )

# 📅 DATE/TIME UTILITY FUNCTIONS
def parse_date(date_str: str) -> date:
    """Parse DD/MM/YYYY format to date object"""
    try:
        return datetime.strptime(date_str, "%d/%m/%Y").date()
    except ValueError:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid date format '{date_str}'. Use DD/MM/YYYY format (e.g., 15/06/2025)"
        )

def parse_time(time_str: str) -> time:
    """Parse time string (24-hour or 12-hour with AM/PM) to time object"""
    time_str = time_str.strip()

    # Try 24-hour format first (HH:MM)
    try:
        return datetime.strptime(time_str, "%H:%M").time()
    except ValueError:
        pass

    # Try 12-hour format with AM/PM
    try:
        return datetime.strptime(time_str, "%I:%M %p").time()
    except ValueError:
        pass

    # Try 12-hour format without space
    try:
        return datetime.strptime(time_str, "%I:%M%p").time()
    except ValueError:
        pass

    raise HTTPException(
        status_code=400,
        detail=f"Invalid time format '{time_str}'. Use HH:MM (24-hour) or H:MM AM/PM (12-hour)"
    )

def format_time_for_response(time_obj: time) -> str:
    """Format time object to HH:MM string"""
    return time_obj.strftime("%H:%M")

def format_date_for_response(date_obj: date) -> str:
    """Format date object to DD/MM/YYYY string"""
    return date_obj.strftime("%d/%m/%Y")

def validate_business_hours(start_time: time, end_time: time) -> bool:
    """Validate that times are within business hours (9 AM - 5 PM)"""
    from app.utils.enums import BUSINESS_START_HOUR, BUSINESS_END_HOUR
    
    business_start = time(BUSINESS_START_HOUR, 0)  # 9:00 AM
    business_end = time(BUSINESS_END_HOUR, 0)     # 5:00 PM

    return (business_start <= start_time <= business_end and
            business_start <= end_time <= business_end)

def validate_slot_duration(start_time: time, end_time: time) -> bool:
    """Validate that slot is exactly 2 hours"""
    from app.utils.enums import INTERVIEW_SLOT_DURATION
    
    start_datetime = datetime.combine(date.today(), start_time)
    end_datetime = datetime.combine(date.today(), end_time)
    duration = end_datetime - start_datetime
    return duration.total_seconds() == INTERVIEW_SLOT_DURATION

def validate_weekday(interview_date: date) -> bool:
    """Validate that date is a weekday (Monday-Friday)"""
    return interview_date.weekday() < 5  # 0-4 are Monday-Friday

def check_company_time_conflict(db: Session, company_name: str, interview_date: date, start_time: time, end_time: time, exclude_booking_id: int = None) -> bool:
    """Check if company already has an interview booked at this time"""
    query = db.query(InterviewBooking).filter(
        InterviewBooking.company_name == company_name,
        InterviewBooking.interview_date == interview_date,
        InterviewBooking.booking_status == "confirmed"
    )

    if exclude_booking_id:
        query = query.filter(InterviewBooking.id != exclude_booking_id)

    existing_bookings = query.all()

    for booking in existing_bookings:
        # Check for time overlap
        if (start_time < booking.end_time and end_time > booking.start_time):
            return True  # Conflict found

    return False  # No conflict
