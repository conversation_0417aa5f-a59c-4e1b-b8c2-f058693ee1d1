-- 🗄️ Simple Supabase Tables Setup (Fixed)
-- Run these SQL commands in your Supabase SQL Editor

-- 1. Create address_proofs table (matching PostgreSQL structure)
CREATE TABLE IF NOT EXISTS address_proofs (
    id SERIAL PRIMARY KEY,
    candidate_id VARCHAR(50) NOT NULL,
    document_type VARCHAR(100) NOT NULL,
    filename VA<PERSON>HAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_size INTEGER,
    content_type VARCHAR(100),
    is_verified BOOLEAN DEFAULT FALSE,
    digilocker_verified BOOLEAN DEFAULT FALSE,
    digilocker_document_id VARCHAR(100),
    verification_status VARCHAR(20) DEFAULT 'pending',
    verification_details JSONB,
    verified_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    synced_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. Create digilocker_auth table
CREATE TABLE IF NOT EXISTS digilocker_auth (
    candidate_id VARCHAR(50) PRIMARY KEY,
    auth_status VARCHAR(20) NOT NULL DEFAULT 'pending',
    digilocker_user_id VARCHAR(50),
    access_token TEXT,
    token_expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    synced_at TIMESTAMPTZ DEFAULT NOW()
);

-- 3. Create basic indexes
CREATE INDEX IF NOT EXISTS idx_address_proofs_candidate_id ON address_proofs(candidate_id);
CREATE INDEX IF NOT EXISTS idx_address_proofs_document_type ON address_proofs(document_type);
CREATE INDEX IF NOT EXISTS idx_address_proofs_created_at ON address_proofs(created_at);
CREATE INDEX IF NOT EXISTS idx_digilocker_auth_candidate_id ON digilocker_auth(candidate_id);

-- 4. Insert test data to verify tables work
INSERT INTO address_proofs (
    candidate_id, 
    document_type, 
    filename, 
    original_filename, 
    file_size, 
    content_type,
    verification_status
) VALUES (
    'TEST001', 
    'Test Document', 
    'test_file.pdf', 
    'test_file.pdf', 
    1024, 
    'application/pdf',
    'pending'
) ON CONFLICT DO NOTHING;

INSERT INTO digilocker_auth (
    candidate_id, 
    auth_status, 
    digilocker_user_id
) VALUES (
    'TEST001', 
    'pending', 
    'test_user_123'
) ON CONFLICT (candidate_id) DO NOTHING;
