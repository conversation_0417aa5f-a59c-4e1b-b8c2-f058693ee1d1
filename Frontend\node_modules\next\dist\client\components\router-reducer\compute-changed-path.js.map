{"version": 3, "sources": ["../../../../src/client/components/router-reducer/compute-changed-path.ts"], "names": ["computeChangedPath", "extractPathFromFlightRouterState", "removeLeadingSlash", "segment", "slice", "segmentToPathname", "normalizeSegments", "segments", "reduce", "acc", "isGroupSegment", "flightRouterState", "Array", "isArray", "DEFAULT_SEGMENT_KEY", "INTERCEPTION_ROUTE_MARKERS", "some", "m", "startsWith", "undefined", "PAGE_SEGMENT_KEY", "parallelRoutes", "<PERSON><PERSON><PERSON>", "children", "push", "key", "value", "Object", "entries", "child<PERSON><PERSON>", "computeChangedPathImpl", "treeA", "treeB", "segmentA", "parallelRoutesA", "segmentB", "parallelRoutesB", "normalizedSegmentA", "normalizedSegmentB", "matchSegment", "parallel<PERSON><PERSON>er<PERSON>ey", "changedPath", "split"], "mappings": ";;;;;;;;;;;;;;;IAuHgBA,kBAAkB;eAAlBA;;IA9EAC,gCAAgC;eAAhCA;;;oCArC2B;yBAKpC;+BACsB;AAE7B,MAAMC,qBAAqB,CAACC;IAC1B,OAAOA,OAAO,CAAC,EAAE,KAAK,MAAMA,QAAQC,KAAK,CAAC,KAAKD;AACjD;AAEA,MAAME,oBAAoB,CAACF;IACzB,IAAI,OAAOA,YAAY,UAAU;QAC/B,uHAAuH;QACvH,gHAAgH;QAChH,IAAIA,YAAY,YAAY,OAAO;QAEnC,OAAOA;IACT;IAEA,OAAOA,OAAO,CAAC,EAAE;AACnB;AAEA,SAASG,kBAAkBC,QAAkB;IAC3C,OACEA,SAASC,MAAM,CAAC,CAACC,KAAKN;QACpBA,UAAUD,mBAAmBC;QAC7B,IAAIA,YAAY,MAAMO,IAAAA,uBAAc,EAACP,UAAU;YAC7C,OAAOM;QACT;QAEA,OAAO,AAAGA,MAAI,MAAGN;IACnB,GAAG,OAAO;AAEd;AAEO,SAASF,iCACdU,iBAAoC;IAEpC,MAAMR,UAAUS,MAAMC,OAAO,CAACF,iBAAiB,CAAC,EAAE,IAC9CA,iBAAiB,CAAC,EAAE,CAAC,EAAE,GACvBA,iBAAiB,CAAC,EAAE;IAExB,IACER,YAAYW,4BAAmB,IAC/BC,8CAA0B,CAACC,IAAI,CAAC,CAACC,IAAMd,QAAQe,UAAU,CAACD,KAE1D,OAAOE;IAET,IAAIhB,QAAQe,UAAU,CAACE,yBAAgB,GAAG,OAAO;IAEjD,MAAMb,WAAW;QAACF,kBAAkBF;KAAS;QACtBQ;IAAvB,MAAMU,iBAAiBV,CAAAA,sBAAAA,iBAAiB,CAAC,EAAE,YAApBA,sBAAwB,CAAC;IAEhD,MAAMW,eAAeD,eAAeE,QAAQ,GACxCtB,iCAAiCoB,eAAeE,QAAQ,IACxDJ;IAEJ,IAAIG,iBAAiBH,WAAW;QAC9BZ,SAASiB,IAAI,CAACF;IAChB,OAAO;QACL,KAAK,MAAM,CAACG,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACP,gBAAiB;YACzD,IAAII,QAAQ,YAAY;YAExB,MAAMI,YAAY5B,iCAAiCyB;YAEnD,IAAIG,cAAcV,WAAW;gBAC3BZ,SAASiB,IAAI,CAACK;YAChB;QACF;IACF;IAEA,OAAOvB,kBAAkBC;AAC3B;AAEA,SAASuB,uBACPC,KAAwB,EACxBC,KAAwB;IAExB,MAAM,CAACC,UAAUC,gBAAgB,GAAGH;IACpC,MAAM,CAACI,UAAUC,gBAAgB,GAAGJ;IAEpC,MAAMK,qBAAqBhC,kBAAkB4B;IAC7C,MAAMK,qBAAqBjC,kBAAkB8B;IAE7C,IACEpB,8CAA0B,CAACC,IAAI,CAC7B,CAACC,IACCoB,mBAAmBnB,UAAU,CAACD,MAAMqB,mBAAmBpB,UAAU,CAACD,KAEtE;QACA,OAAO;IACT;IAEA,IAAI,CAACsB,IAAAA,2BAAY,EAACN,UAAUE,WAAW;YAE9BlC;QADP,8FAA8F;QAC9F,OAAOA,CAAAA,oCAAAA,iCAAiC+B,kBAAjC/B,oCAA2C;IACpD;IAEA,IAAK,MAAMuC,qBAAqBN,gBAAiB;QAC/C,IAAIE,eAAe,CAACI,kBAAkB,EAAE;YACtC,MAAMC,cAAcX,uBAClBI,eAAe,CAACM,kBAAkB,EAClCJ,eAAe,CAACI,kBAAkB;YAEpC,IAAIC,gBAAgB,MAAM;gBACxB,OAAO,AAAGpC,kBAAkB8B,YAAU,MAAGM;YAC3C;QACF;IACF;IAEA,OAAO;AACT;AAEO,SAASzC,mBACd+B,KAAwB,EACxBC,KAAwB;IAExB,MAAMS,cAAcX,uBAAuBC,OAAOC;IAElD,IAAIS,eAAe,QAAQA,gBAAgB,KAAK;QAC9C,OAAOA;IACT;IAEA,mDAAmD;IACnD,OAAOnC,kBAAkBmC,YAAYC,KAAK,CAAC;AAC7C"}