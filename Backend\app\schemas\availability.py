"""
Availability and Interview Booking Schemas
"""

from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel

class TimeSlotCreate(BaseModel):
    interview_date: str  # DD/MM/YYYY format
    start_time: str      # HH:MM format
    end_time: str        # HH:MM format

class AvailabilitySlotCreate(BaseModel):
    slot_type: str = "general"  # 'general' or 'job_specific'
    slots: List[TimeSlotCreate]
    company_name: Optional[str] = None  # Required for job_specific
    job_position: Optional[str] = None  # Required for job_specific

class AvailabilitySlotResponse(BaseModel):
    id: int
    candidate_id: str
    slot_type: str
    interview_date: str  # DD/MM/YYYY format
    start_time: str      # HH:MM format
    end_time: str        # HH:MM format
    is_booked: bool
    company_name: Optional[str] = None
    job_position: Optional[str] = None
    created_at: datetime
    
    class Config:
        from_attributes = True

class InterviewBookingCreate(BaseModel):
    company_name: str
    job_position: str
    interview_date: str  # DD/MM/YYYY format

class InterviewBookingResponse(BaseModel):
    id: int
    slot_id: int
    candidate_id: str
    company_name: str
    job_position: str
    interview_date: str  # DD/MM/YYYY format
    start_time: str      # HH:MM format
    end_time: str        # HH:MM format
    booking_status: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True
