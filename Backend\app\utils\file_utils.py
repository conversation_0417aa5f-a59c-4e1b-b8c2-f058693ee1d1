"""
File handling utilities
"""

import os
import uuid
import shutil
from pathlib import Path
from fastapi import UploadFile, HTTPException

from app.utils.enums import (
    ALLOWED_RESUME_TYPES, ALLOWED_DOCUMENT_TYPES, 
    MAX_RESUME_SIZE, MAX_DOCUMENT_SIZE
)

UPLOAD_DIR = Path("uploads")
UPLOAD_DIR.mkdir(exist_ok=True)

def save_uploaded_file(file: UploadFile, candidate_id: str) -> tuple:
    """Save uploaded resume file"""
    file_extension = Path(file.filename).suffix
    unique_filename = f"{candidate_id}_resume_{uuid.uuid4()}{file_extension}"
    file_path = UPLOAD_DIR / unique_filename
    with open(file_path, "wb") as buffer:
        shutil.copyfileobj(file.file, buffer)
    return str(file_path), unique_filename

def save_payslip_file(file: UploadFile, candidate_id: str, month_year: str) -> tuple:
    """Save payslip file with month_year in filename"""
    file_extension = Path(file.filename).suffix
    # Format: CAND001_payslip_06_2025_uuid.pdf
    unique_filename = f"{candidate_id}_payslip_{month_year.replace('/', '_')}_{uuid.uuid4()}{file_extension}"
    file_path = UPLOAD_DIR / unique_filename
    with open(file_path, "wb") as buffer:
        shutil.copyfileobj(file.file, buffer)
    return str(file_path), unique_filename

def save_address_proof_file(file: UploadFile, candidate_id: str, document_type: str) -> tuple:
    """Save address proof file with document type in filename"""
    file_extension = Path(file.filename).suffix
    # Format: CAND001_address_aadhar_uuid.pdf
    doc_type_clean = document_type.lower().replace(" ", "_").replace("'", "")
    unique_filename = f"{candidate_id}_address_{doc_type_clean}_{uuid.uuid4()}{file_extension}"
    file_path = UPLOAD_DIR / unique_filename
    with open(file_path, "wb") as buffer:
        shutil.copyfileobj(file.file, buffer)
    return str(file_path), unique_filename

def validate_resume_file(file: UploadFile) -> bool:
    """Validate resume file type and size"""
    if file.content_type not in ALLOWED_RESUME_TYPES:
        raise HTTPException(
            status_code=400, 
            detail="Only PDF, DOC, and DOCX files are allowed"
        )

    if file.size > MAX_RESUME_SIZE:
        raise HTTPException(
            status_code=400, 
            detail="File size must be less than 5MB"
        )

    return True

def validate_address_proof_file(file: UploadFile) -> bool:
    """Validate address proof file type and size"""
    if file.content_type not in ALLOWED_DOCUMENT_TYPES:
        raise HTTPException(
            status_code=400,
            detail="Only PDF, JPG, and PNG files are allowed for address proof"
        )

    if file.size > MAX_DOCUMENT_SIZE:
        raise HTTPException(
            status_code=400,
            detail="Address proof file size must be less than 10MB"
        )

    return True

def validate_payslip_file(file: UploadFile) -> bool:
    """Validate payslip file type and size"""
    if file.content_type not in ALLOWED_DOCUMENT_TYPES:
        raise HTTPException(
            status_code=400,
            detail="Only PDF, JPG, and PNG files are allowed for payslips"
        )

    if file.size > MAX_DOCUMENT_SIZE:
        raise HTTPException(
            status_code=400,
            detail="Payslip file size must be less than 10MB"
        )

    return True
