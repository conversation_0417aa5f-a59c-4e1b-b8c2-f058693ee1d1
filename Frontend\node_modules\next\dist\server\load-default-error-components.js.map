{"version": 3, "sources": ["../../src/server/load-default-error-components.ts"], "names": ["loadDefaultErrorComponents", "loadDefaultErrorComponentsImpl", "distDir", "Document", "interopDefault", "require", "AppMod", "App", "ComponentMod", "Component", "routeModule", "userland", "default", "pageConfig", "buildManifest", "loadManifestWithRetries", "join", "BUILD_MANIFEST", "reactLoadableManifest", "page", "getTracer", "wrap", "LoadComponentsSpan"], "mappings": ";;;;+BA0EaA;;;eAAAA;;;2BA3DkB;sBACV;gCACU;wBACL;4BACS;gCACK;AA2BxC,eAAeC,+BACbC,OAAe;IAEf,MAAMC,WAAWC,IAAAA,8BAAc,EAACC,QAAQ;IACxC,MAAMC,SAASD,QAAQ;IACvB,MAAME,MAAMH,IAAAA,8BAAc,EAACE;IAE3B,yDAAyD;IACzD,qGAAqG;IACrG,MAAME,eACJH,QAAQ;IACV,MAAMI,YAAYD,aAAaE,WAAW,CAACC,QAAQ,CAACC,OAAO;IAE3D,OAAO;QACLL;QACAJ;QACAM;QACAI,YAAY,CAAC;QACbC,eAAgB,MAAMC,IAAAA,uCAAuB,EAC3CC,IAAAA,UAAI,EAACd,SAAS,CAAC,SAAS,EAAEe,yBAAc,CAAC,CAAC;QAE5CC,uBAAuB,CAAC;QACxBV;QACAW,MAAM;QACNT,aAAaF,aAAaE,WAAW;IACvC;AACF;AACO,MAAMV,6BAA6BoB,IAAAA,iBAAS,IAAGC,IAAI,CACxDC,8BAAkB,CAACtB,0BAA0B,EAC7CC"}