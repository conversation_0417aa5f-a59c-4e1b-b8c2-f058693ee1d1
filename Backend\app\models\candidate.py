"""
Comprehensive database models for the job application system
"""

import os
from datetime import datetime, timezone
from sqlalchemy import (
    Column, Integer, String, DateTime, Boolean, Text, ForeignKey,
    Date, Time, JSON, DECIMAL, Float, UniqueConstraint
)
from sqlalchemy.orm import relationship

from app.database.connection import Base

class Candidate(Base):
    __tablename__ = "candidates"
    candidate_id = Column(String, primary_key=True, index=True)
    first_name = Column(String(100), nullable=True)
    last_name = Column(String(100), nullable=True)
    email = Column(String(255), nullable=True)
    phone = Column(String(20), nullable=True)
    position = Column(String(200), nullable=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    resume = relationship("Resume", back_populates="candidate", uselist=False, cascade="all, delete-orphan")
    interview_feedback = relationship("InterviewFeedback", back_populates="candidate", uselist=False, cascade="all, delete-orphan")
    availability_slots = relationship("AvailabilitySlot", back_populates="candidate", cascade="all, delete-orphan")
    interview_bookings = relationship("InterviewBooking", back_populates="candidate", cascade="all, delete-orphan")
    notice_period = relationship("NoticePeriod", back_populates="candidate", uselist=False, cascade="all, delete-orphan")
    payslips = relationship("Payslip", back_populates="candidate", cascade="all, delete-orphan")
    background_verification = relationship("BackgroundVerification", back_populates="candidate", uselist=False, cascade="all, delete-orphan")

class Resume(Base):
    __tablename__ = "resumes"
    id = Column(Integer, primary_key=True, index=True)
    candidate_id = Column(String, ForeignKey("candidates.candidate_id"), nullable=False, unique=True)
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=False)
    content_type = Column(String(100), nullable=False)
    is_visible = Column(Boolean, default=True)
    uploaded_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    candidate = relationship("Candidate", back_populates="resume")

class AvailabilitySlot(Base):
    __tablename__ = "availability_slots"
    id = Column(Integer, primary_key=True, index=True)
    candidate_id = Column(String, ForeignKey("candidates.candidate_id"), nullable=False)
    slot_type = Column(String(20), nullable=False, default="general")  # Only 'general' now
    interview_date = Column(Date, nullable=False)  # Actual calendar date (DD/MM/YYYY)
    start_time = Column(Time, nullable=False)
    end_time = Column(Time, nullable=False)
    is_booked = Column(Boolean, default=False)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    candidate = relationship("Candidate", back_populates="availability_slots")

class ShortlistedJobAvailability(Base):
    __tablename__ = "shortlisted_job_availability"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)
    candidate_id = Column(String, ForeignKey("candidates.candidate_id"), nullable=False)
    company_name = Column(String(255), nullable=False)
    job_position = Column(String(255), nullable=False)
    interview_date = Column(Date, nullable=False)
    start_time = Column(Time, nullable=False)
    end_time = Column(Time, nullable=False)
    is_booked = Column(Boolean, default=False)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    candidate = relationship("Candidate")

class InterviewBooking(Base):
    __tablename__ = "interview_bookings"
    id = Column(Integer, primary_key=True, index=True)
    slot_id = Column(Integer, ForeignKey("availability_slots.id"), nullable=False)
    candidate_id = Column(String, ForeignKey("candidates.candidate_id"), nullable=False)
    company_name = Column(String(255), nullable=False)
    job_position = Column(String(255), nullable=False)
    interview_date = Column(Date, nullable=False)
    start_time = Column(Time, nullable=False)
    end_time = Column(Time, nullable=False)
    booking_status = Column(String(20), default="confirmed")  # confirmed, cancelled, completed
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    candidate = relationship("Candidate", back_populates="interview_bookings")
    slot = relationship("AvailabilitySlot")

class NoticePeriod(Base):
    __tablename__ = "notice_periods"
    id = Column(Integer, primary_key=True, index=True)
    candidate_id = Column(String, ForeignKey("candidates.candidate_id"), nullable=False, unique=True)
    notice_period_days = Column(Integer, nullable=False, default=lambda: int(os.getenv("DEFAULT_NOTICE_PERIOD_DAYS", "30")))
    current_company = Column(String(255), nullable=True)
    current_position = Column(String(255), nullable=True)
    current_salary = Column(Integer, nullable=True)  # Monthly salary
    expected_salary = Column(Integer, nullable=True)  # Expected monthly salary
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    candidate = relationship("Candidate", back_populates="notice_period")

class Payslip(Base):
    __tablename__ = "payslips"
    id = Column(Integer, primary_key=True, index=True)
    candidate_id = Column(String, ForeignKey("candidates.candidate_id"), nullable=False)
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=False)
    content_type = Column(String(100), nullable=False)
    month_year = Column(String(50), nullable=False)  # Format: MM/YYYY_timestamp for uniqueness
    salary_amount = Column(Integer, nullable=True)  # Extracted salary amount if available
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    candidate = relationship("Candidate", back_populates="payslips")

class BackgroundVerification(Base):
    __tablename__ = "background_verification"
    id = Column(Integer, primary_key=True, index=True)
    candidate_id = Column(String, ForeignKey("candidates.candidate_id"), nullable=False, unique=True)
    status = Column(String(20), default="In Progress")  # "In Progress", "Completed", "Failed", "Pending"
    verification_notes = Column(Text, nullable=True)
    employment_verified = Column(Boolean, default=False)
    education_verified = Column(Boolean, default=False)
    address_verified = Column(Boolean, default=False)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    candidate = relationship("Candidate", back_populates="background_verification")
    address_proofs = relationship("AddressProof", back_populates="background_verification", cascade="all, delete-orphan")
    provident_fund = relationship("ProvidentFund", back_populates="background_verification", uselist=False, cascade="all, delete-orphan")

class AddressProof(Base):
    __tablename__ = "address_proofs"
    id = Column(Integer, primary_key=True, index=True)
    candidate_id = Column(String, ForeignKey("candidates.candidate_id"), nullable=False)  # Direct candidate reference for easier analysis
    background_verification_id = Column(Integer, ForeignKey("background_verification.id"), nullable=False)
    document_type = Column(String(50), nullable=False)  # "Aadhar Card", "Passport", "Driver's License", "Utility Bill", "Voter ID"
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=False)
    content_type = Column(String(100), nullable=False)
    is_verified = Column(Boolean, default=False)
    # New DigiLocker verification fields
    digilocker_verified = Column(Boolean, default=False)  # Whether verified against DigiLocker
    digilocker_document_id = Column(String(100), nullable=True)  # Link to DigiLocker document
    verification_status = Column(String(20), default="pending")  # pending, verified, failed, genuine, fake
    verification_details = Column(JSON, nullable=True)  # Store verification details from DigiLocker
    verified_at = Column(DateTime, nullable=True)  # When DigiLocker verification was completed
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    # Relationships
    candidate = relationship("Candidate")  # Direct relationship to candidate for easier queries
    background_verification = relationship("BackgroundVerification", back_populates="address_proofs")

class ProvidentFund(Base):
    __tablename__ = "provident_fund"
    id = Column(Integer, primary_key=True, index=True)
    background_verification_id = Column(Integer, ForeignKey("background_verification.id"), nullable=False, unique=True)
    account_number = Column(String(50), nullable=True)
    uan_number = Column(String(20), nullable=True)  # Universal Account Number
    previous_employer = Column(String(255), nullable=True)
    current_employer = Column(String(255), nullable=True)
    additional_details = Column(Text, nullable=True)
    is_verified = Column(Boolean, default=False)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    background_verification = relationship("BackgroundVerification", back_populates="provident_fund")

class DigiLockerAuth(Base):
    __tablename__ = "digilocker_auth"
    id = Column(Integer, primary_key=True, index=True)
    candidate_id = Column(String, ForeignKey("candidates.candidate_id"), nullable=False, unique=True)
    access_token = Column(Text, nullable=True)
    refresh_token = Column(Text, nullable=True)
    token_expires_at = Column(DateTime, nullable=True)
    digilocker_user_id = Column(String(100), nullable=True)
    auth_status = Column(String(20), default="pending")  # pending, authenticated, failed, expired
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    candidate = relationship("Candidate")

class DigiLockerDocument(Base):
    __tablename__ = "digilocker_documents"
    id = Column(Integer, primary_key=True, index=True)
    candidate_id = Column(String, ForeignKey("candidates.candidate_id"), nullable=False)
    document_type = Column(String(50), nullable=False)  # "aadhaar", "pan", "driving_license", "passport", etc.
    document_id = Column(String(100), nullable=False)  # DigiLocker document ID
    document_name = Column(String(255), nullable=False)
    document_uri = Column(Text, nullable=False)
    issuer = Column(String(100), nullable=True)
    issue_date = Column(Date, nullable=True)
    expiry_date = Column(Date, nullable=True)
    verification_status = Column(String(20), default="pending")  # pending, verified, failed
    document_data = Column(JSON, nullable=True)  # Store extracted document data
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    candidate = relationship("Candidate")

# ============================================================================
# 🆕 ADDITIONAL MODELS FROM PROVIDED SCHEMA
# ============================================================================

# --- 1. Roles ---
class Role(Base):
    __tablename__ = "roles"
    role_id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(20), unique=True, nullable=False)  # "candidate" or "employer"
    description = Column(String(255), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    users = relationship("User", back_populates="role_rel")

# --- 2. Users ---
class User(Base):
    __tablename__ = "users"
    user_id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(255), nullable=False)  # Maps to full_name
    initials = Column(String(10), nullable=True)  # New field for initials
    email = Column(String(255), unique=True, nullable=False)
    password_hash = Column(String(255), nullable=False)  # Maps to password
    phone_number = Column(String(15), nullable=True)  # Maps to phone
    role_id = Column(Integer, ForeignKey("roles.role_id"), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    role_rel = relationship("Role", back_populates="users")
    resumes = relationship("Resume", back_populates="user", cascade="all, delete")
    applications = relationship("JobApplication", back_populates="user", cascade="all, delete")
    interviews = relationship("SimulationInterview", back_populates="user", cascade="all, delete")
    predictions = relationship("SalaryPrediction", back_populates="user", cascade="all, delete")
    threshold_scores = relationship("ThresholdScore", back_populates="user", cascade="all, delete")
    candidate_profile = relationship("Candidate", back_populates="user", uselist=False, cascade="all, delete")
    user_profile = relationship("UserProfile", back_populates="user", uselist=False, cascade="all, delete")
    learning_progress = relationship("LearningProgress", back_populates="user", cascade="all, delete")
    analytics = relationship("CandidateAnalytics", back_populates="user", cascade="all, delete")

# --- 3. User Profile ---
class UserProfile(Base):
    __tablename__ = "user_profiles"
    profile_id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"), nullable=False)
    job_title = Column(String(255), nullable=True)  # Maps to job_title
    status = Column(String(50), nullable=True)  # Maps to status (e.g., "Premium Member")
    profile_strength = Column(Float, nullable=True)  # Maps to profile_strength
    profile_completion = Column(Float, nullable=True)  # Maps to profile_completion (percentage)
    work_experience_status = Column(String(50), nullable=True)  # Maps to work_experience_status
    portfolio_status = Column(String(50), nullable=True)  # Maps to portfolio_status
    basic_info_status = Column(String(50), nullable=True)  # Maps to basic_info_status
    resume_status = Column(String(50), nullable=True)  # Maps to resume_status
    skills_status = Column(String(50), nullable=True)  # Maps to skills_status
    location = Column(String(255), nullable=True)  # Maps to location
    profile_score = Column(Float, nullable=True)  # Maps to profile_score (percentage)
    experience_level = Column(Float, nullable=True)  # Maps to experience_level (years)
    skill_count = Column(Integer, nullable=True)  # Maps to skill_count
    education_level = Column(String(100), nullable=True)  # Maps to education_level
    overall_competitiveness = Column(String(50), nullable=True)  # Maps to overall_competitiveness
    expected_salary = Column(String(50), nullable=True)  # Maps to expected_salary
    availability = Column(String(50), nullable=True)  # Maps to availability
    source = Column(String(255), nullable=True)  # Maps to source
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="user_profile")

# --- 4. Resumes (Updated) ---
class ResumeUpdated(Base):
    __tablename__ = "resumes_updated"
    resume_id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"), nullable=False)
    candidate_id = Column(Integer, ForeignKey("candidates.candidate_id", ondelete="CASCADE"), nullable=False)
    job_id = Column(Integer, ForeignKey("job_descriptions.job_id", ondelete="SET NULL"), nullable=True)
    file_url = Column(Text, nullable=False)
    resume_url = Column(String(255), nullable=True)
    parsed_data = Column(Text, nullable=True)
    upload_date = Column(DateTime, default=datetime.utcnow)
    is_active = Column(Boolean, default=True)
    version = Column(Integer, default=1)
    activity_type = Column(String(50), nullable=True)
    file_name = Column(String(255), nullable=True)  # Maps to resume_upload.file_name
    file_type = Column(String(50), nullable=True)  # Maps to resume_upload.file_type
    file_size_limit = Column(String(50), nullable=True)  # Maps to resume_upload.file_size_limit
    ai_resume_generation_status = Column(String(50), nullable=True)  # Maps to ai_resume_generation.status
    ai_resume_time_estimate = Column(String(50), nullable=True)  # Maps to ai_resume_generation.time_estimate
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="resumes")
    candidate = relationship("Candidate", back_populates="resumes")
    applications = relationship("JobApplication", back_populates="resume", cascade="all, delete")
    evaluations = relationship("ResumeEvaluation", back_populates="resume", cascade="all, delete")
    analytics = relationship("ResumeAnalytics", back_populates="resume", cascade="all, delete")
    job_associations = relationship("ResumeJobAssociation", back_populates="resume")

# --- 5. Job Descriptions ---
class JobDescription(Base):
    __tablename__ = "job_descriptions"
    job_id = Column(Integer, primary_key=True, autoincrement=True)
    title = Column(String(255), nullable=False)  # Maps to job_title
    company_name = Column(String(255), nullable=True)  # Maps to company
    location = Column(String(255), nullable=True)  # Maps to job_location
    description = Column(Text, nullable=False)  # Maps to job_description
    raw_text = Column(Text, nullable=True)
    keywords = Column(Text, nullable=True)
    status = Column(String(50), default="Active")  # Maps to status
    department = Column(String(100), nullable=True)
    required_skills = Column(Text, nullable=True)  # Maps to job_skills
    experience_level = Column(String(100), nullable=True)
    education_requirements = Column(Text, nullable=True)
    threshold_score = Column(Float, nullable=True)
    source_file = Column(String(255), nullable=True)
    salary_range = Column(String(50), nullable=True)  # Maps to job_salary
    source = Column(String(255), nullable=True)
    posted_date = Column(DateTime, nullable=True)  # Maps to posted_date
    applications_received = Column(Integer, nullable=True)  # Maps to applications_received
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    applications = relationship("JobApplication", back_populates="job", cascade="all, delete")
    candidates = relationship("Candidate", back_populates="job")
    required_skills_rel = relationship("JobRequiredSkills", back_populates="job", cascade="all, delete")
    threshold_scores = relationship("ThresholdScore", back_populates="job", cascade="all, delete")
    resume_associations = relationship("ResumeJobAssociation", back_populates="job")
    company_details = relationship("CompanyDetails", back_populates="job", uselist=False)

# --- 6. Candidates (Updated) ---
class CandidateUpdated(Base):
    __tablename__ = "candidates_updated"
    candidate_id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"), nullable=False)
    name = Column(String(255), nullable=True)  # Maps to candidate_name
    email = Column(String(255), nullable=True)
    job_id = Column(Integer, ForeignKey("job_descriptions.job_id", ondelete="SET NULL"), nullable=True)
    status = Column(String(50), default="Pending")  # Maps to status
    resume_url = Column(String(255), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="candidate_profile")
    job = relationship("JobDescription", back_populates="candidates")
    resumes = relationship("ResumeUpdated", back_populates="candidate")
    threshold_scores = relationship("ThresholdScore", back_populates="candidate")

# --- 7. Resume Job Associations ---
class ResumeJobAssociation(Base):
    __tablename__ = "resume_job_associations"
    id = Column(Integer, primary_key=True, index=True)
    resume_id = Column(Integer, ForeignKey("resumes_updated.resume_id"), nullable=False)
    job_id = Column(Integer, ForeignKey("job_descriptions.job_id"), nullable=False)
    match_score = Column(Float, nullable=True)  # Maps to match_score, overall_match
    selection_percentage = Column(Float, nullable=True)  # Maps to selection_percentage
    rejection_percentage = Column(Float, nullable=True)  # Maps to rejection_percentage
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    resume = relationship("ResumeUpdated", back_populates="job_associations")
    job = relationship("JobDescription", back_populates="resume_associations")

# --- 8. Job Applications ---
class JobApplication(Base):
    __tablename__ = "job_applications"
    application_id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"))
    job_id = Column(Integer, ForeignKey("job_descriptions.job_id", ondelete="CASCADE"))
    resume_id = Column(Integer, ForeignKey("resumes_updated.resume_id", ondelete="CASCADE"))
    status = Column(String(20), default="applied")  # Maps to status
    applied_at = Column(DateTime, default=datetime.utcnow)  # Maps to date
    note = Column(Text, nullable=True)  # Maps to note
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="applications")
    job = relationship("JobDescription", back_populates="applications")
    resume = relationship("ResumeUpdated", back_populates="applications")
    timeline = relationship("ApplicationTimeline", back_populates="application", cascade="all, delete")

# --- 9. Simulation Interviews ---
class SimulationInterview(Base):
    __tablename__ = "simulation_interviews"
    interview_id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"))
    job_id = Column(Integer, ForeignKey("job_descriptions.job_id", ondelete="CASCADE"))
    performance_score = Column(DECIMAL(5, 2), nullable=True)
    feedback = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="interviews")
    job = relationship("JobDescription")

# --- 10. Salary Predictions ---
class SalaryPrediction(Base):
    __tablename__ = "salary_predictions"
    prediction_id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"))
    role = Column(String(255), nullable=True)
    skills = Column(JSON, nullable=True)
    years_of_experience = Column(Integer, nullable=True)
    predicted_salary = Column(String(50), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="predictions")

# --- 11. Threshold Scores ---
class ThresholdScore(Base):
    __tablename__ = "threshold_scores"
    threshold_id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.user_id"), nullable=True)
    job_id = Column(Integer, ForeignKey("job_descriptions.job_id"), nullable=True)
    resume_id = Column(Integer, ForeignKey("resumes_updated.resume_id"), nullable=True)
    candidate_id = Column(Integer, ForeignKey("candidates_updated.candidate_id"), nullable=True)
    selection_score = Column(Float, nullable=True)
    rejection_score = Column(Float, nullable=True)
    threshold_value = Column(Float, nullable=True)
    threshold_result = Column(JSON, nullable=True)
    threshold_prompts = Column(Text, nullable=True)
    custom_prompts = Column(Text, nullable=True)
    sample_prompts_history = Column(Text, nullable=True)
    activity_type = Column(String(50), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    threshold_history = Column(Text, nullable=True)

    # Relationships
    job = relationship("JobDescription", back_populates="threshold_scores")
    user = relationship("User", back_populates="threshold_scores")
    resume = relationship("ResumeUpdated")
    candidate = relationship("CandidateUpdated", back_populates="threshold_scores")

# --- 12. Resume Evaluations ---
class ResumeEvaluation(Base):
    __tablename__ = "resume_evaluations"
    evaluation_id = Column(Integer, primary_key=True, autoincrement=True)
    resume_id = Column(Integer, ForeignKey("resumes_updated.resume_id"))
    evaluator_id = Column(Integer, ForeignKey("users.user_id"), nullable=True)
    score = Column(Float, nullable=True)
    strengths = Column(Text, nullable=True)
    weaknesses = Column(Text, nullable=True)
    feedback = Column(Text, nullable=True)
    evaluation_type = Column(String(50), nullable=False, default="automated")
    activity_type = Column(String(50), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    resume = relationship("ResumeUpdated", back_populates="evaluations")

# --- 13. Resume Analytics ---
class ResumeAnalytics(Base):
    __tablename__ = "resume_analytics"
    analytics_id = Column(Integer, primary_key=True, autoincrement=True)
    resume_id = Column(Integer, ForeignKey("resumes_updated.resume_id"), nullable=False)
    ai_generated_question = Column(String(255), nullable=False)
    answer_text = Column(Text, nullable=True)
    score = Column(Float, nullable=True)
    strengths = Column(Text, nullable=True)
    weaknesses = Column(Text, nullable=True)
    insights = Column(Text, nullable=True)
    activity_type = Column(String(50), nullable=True)
    generated_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    resume = relationship("ResumeUpdated", back_populates="analytics")

# --- 14. Skills ---
class Skill(Base):
    __tablename__ = "skills"
    skill_id = Column(Integer, primary_key=True, autoincrement=True)
    skill_name = Column(String(255), nullable=False, unique=True, index=True)
    skill_type = Column(String(50), nullable=True)
    category = Column(String(100), nullable=True)
    description = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    job_skills = relationship("JobRequiredSkills", back_populates="skill")
    user_skills = relationship("UserSkill", back_populates="skill")

# --- 15. Job Required Skills ---
class JobRequiredSkills(Base):
    __tablename__ = "job_required_skills"
    id = Column(Integer, primary_key=True, autoincrement=True)
    job_id = Column(Integer, ForeignKey("job_descriptions.job_id"))
    skill_id = Column(Integer, ForeignKey("skills.skill_id"))
    importance = Column(Float, nullable=True)
    selection_weight = Column(Float, nullable=True)
    rejection_weight = Column(Float, nullable=True)

    # Relationships
    job = relationship("JobDescription", back_populates="required_skills_rel")
    skill = relationship("Skill", back_populates="job_skills")

# --- 16. User Skills ---
class UserSkill(Base):
    __tablename__ = "user_skills"
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"))
    skill_id = Column(Integer, ForeignKey("skills.skill_id", ondelete="CASCADE"))
    proficiency_level = Column(Integer, default=1)
    years_of_experience = Column(Float, default=0.0)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    user = relationship("User")
    skill = relationship("Skill", back_populates="user_skills")

# --- 17. Learning Paths ---
class LearningPath(Base):
    __tablename__ = "learning_paths"
    path_id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"))
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    progress = Column(Float, default=0)
    step = Column(Integer, default=1)
    duration = Column(String(50), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = relationship("User")

# --- 18. Learning Progress ---
class LearningProgress(Base):
    __tablename__ = "learning_progress"
    progress_id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"))
    learning_path_id = Column(Integer, ForeignKey("learning_paths.path_id"), nullable=True)
    skill_recommendations = Column(JSON, nullable=True)  # Maps to skill_recommendations
    available_courses = Column(Integer, nullable=True)  # Maps to available_courses
    completion_rate = Column(Float, nullable=True)  # Maps to completion_rate
    courses_completed = Column(Integer, nullable=True)  # Maps to courses_completed
    study_hours = Column(Float, nullable=True)  # Maps to study_hours
    skills_improved = Column(Integer, nullable=True)  # Maps to skills_improved
    current_streak = Column(Integer, nullable=True)  # Maps to current_streak
    current_learning = Column(JSON, nullable=True)  # Maps to current_learning
    weekly_goal = Column(JSON, nullable=True)  # Maps to weekly_goal
    monthly_goal = Column(JSON, nullable=True)  # Maps to monthly_goal
    achievement_points = Column(JSON, nullable=True)  # Maps to achievement_points
    recommended_certifications = Column(JSON, nullable=True)  # Maps to recommended_certifications
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="learning_progress")
    learning_path = relationship("LearningPath")

# --- Additional Models Referenced ---
class ApplicationTimeline(Base):
    __tablename__ = "application_timeline"
    id = Column(Integer, primary_key=True, autoincrement=True)
    application_id = Column(Integer, ForeignKey("job_applications.application_id"))
    status = Column(String(50), nullable=False)
    timestamp = Column(DateTime, default=datetime.utcnow)
    notes = Column(Text, nullable=True)

    # Relationships
    application = relationship("JobApplication", back_populates="timeline")

class CompanyDetails(Base):
    __tablename__ = "company_details"
    id = Column(Integer, primary_key=True, autoincrement=True)
    job_id = Column(Integer, ForeignKey("job_descriptions.job_id"))
    company_size = Column(String(50), nullable=True)
    industry = Column(String(100), nullable=True)
    website = Column(String(255), nullable=True)
    description = Column(Text, nullable=True)

    # Relationships
    job = relationship("JobDescription", back_populates="company_details")

class CandidateAnalytics(Base):
    __tablename__ = "candidate_analytics"
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.user_id"))
    metric_name = Column(String(100), nullable=False)
    metric_value = Column(Float, nullable=True)
    recorded_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="analytics")

# ============================================================================
# 🆕 NEW COMPREHENSIVE MODELS
# ============================================================================

# --- 1. Roles ---
class Role(Base):
    __tablename__ = "roles"
    role_id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(20), unique=True, nullable=False)  # "candidate" or "employer"
    description = Column(String(255), nullable=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    # Relationships
    users = relationship("User", back_populates="role_rel")

# --- 2. Users ---
class User(Base):
    __tablename__ = "users"
    user_id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(255), nullable=False)  # Maps to full_name
    initials = Column(String(10), nullable=True)  # New field for initials
    email = Column(String(255), unique=True, nullable=False)
    password_hash = Column(String(255), nullable=False)  # Maps to password
    phone_number = Column(String(15), nullable=True)  # Maps to phone
    role_id = Column(Integer, ForeignKey("roles.role_id"), nullable=False)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # Relationships
    role_rel = relationship("Role", back_populates="users")
    resumes_new = relationship("ResumeNew", back_populates="user", cascade="all, delete")
    applications = relationship("JobApplication", back_populates="user", cascade="all, delete")
    interviews = relationship("SimulationInterview", back_populates="user", cascade="all, delete")
    predictions = relationship("SalaryPrediction", back_populates="user", cascade="all, delete")
    threshold_scores = relationship("ThresholdScore", back_populates="user", cascade="all, delete")
    candidate_profile = relationship("CandidateNew", back_populates="user", uselist=False, cascade="all, delete")
    user_profile = relationship("UserProfile", back_populates="user", uselist=False, cascade="all, delete")
    learning_progress = relationship("LearningProgress", back_populates="user", cascade="all, delete")
    analytics = relationship("CandidateAnalytics", back_populates="user", cascade="all, delete")

# --- 3. User Profile ---
class UserProfile(Base):
    __tablename__ = "user_profiles"
    profile_id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"), nullable=False)
    job_title = Column(String(255), nullable=True)  # Maps to job_title
    status = Column(String(50), nullable=True)  # Maps to status (e.g., "Premium Member")
    profile_strength = Column(Float, nullable=True)  # Maps to profile_strength
    profile_completion = Column(Float, nullable=True)  # Maps to profile_completion (percentage)
    work_experience_status = Column(String(50), nullable=True)  # Maps to work_experience_status
    portfolio_status = Column(String(50), nullable=True)  # Maps to portfolio_status
    basic_info_status = Column(String(50), nullable=True)  # Maps to basic_info_status
    resume_status = Column(String(50), nullable=True)  # Maps to resume_status
    skills_status = Column(String(50), nullable=True)  # Maps to skills_status
    location = Column(String(255), nullable=True)  # Maps to location
    profile_score = Column(Float, nullable=True)  # Maps to profile_score (percentage)
    experience_level = Column(Float, nullable=True)  # Maps to experience_level (years)
    skill_count = Column(Integer, nullable=True)  # Maps to skill_count
    education_level = Column(String(100), nullable=True)  # Maps to education_level
    overall_competitiveness = Column(String(50), nullable=True)  # Maps to overall_competitiveness
    expected_salary = Column(String(50), nullable=True)  # Maps to expected_salary
    availability = Column(String(50), nullable=True)  # Maps to availability
    source = Column(String(255), nullable=True)  # Maps to source
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # Relationships
    user = relationship("User", back_populates="user_profile")

# --- 4. New Resume Model ---
class ResumeNew(Base):
    __tablename__ = "resumes_new"
    resume_id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"), nullable=False)
    candidate_id = Column(Integer, ForeignKey("candidates_new.candidate_id", ondelete="CASCADE"), nullable=False)
    job_id = Column(Integer, ForeignKey("job_descriptions.job_id", ondelete="SET NULL"), nullable=True)
    file_url = Column(Text, nullable=False)
    resume_url = Column(String(255), nullable=True)
    parsed_data = Column(Text, nullable=True)
    upload_date = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    is_active = Column(Boolean, default=True)
    version = Column(Integer, default=1)
    activity_type = Column(String(50), nullable=True)
    file_name = Column(String(255), nullable=True)  # Maps to resume_upload.file_name
    file_type = Column(String(50), nullable=True)  # Maps to resume_upload.file_type
    file_size_limit = Column(String(50), nullable=True)  # Maps to resume_upload.file_size_limit
    ai_resume_generation_status = Column(String(50), nullable=True)  # Maps to ai_resume_generation.status
    ai_resume_time_estimate = Column(String(50), nullable=True)  # Maps to ai_resume_generation.time_estimate
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    # Relationships
    user = relationship("User", back_populates="resumes_new")
    candidate = relationship("CandidateNew", back_populates="resumes")
    applications = relationship("JobApplication", back_populates="resume", cascade="all, delete")
    evaluations = relationship("ResumeEvaluation", back_populates="resume", cascade="all, delete")
    analytics = relationship("ResumeAnalytics", back_populates="resume", cascade="all, delete")
    job_associations = relationship("ResumeJobAssociation", back_populates="resume")

# --- 5. Job Descriptions ---
class JobDescription(Base):
    __tablename__ = "job_descriptions"
    job_id = Column(Integer, primary_key=True, autoincrement=True)
    title = Column(String(255), nullable=False)  # Maps to job_title
    company_name = Column(String(255), nullable=True)  # Maps to company
    location = Column(String(255), nullable=True)  # Maps to job_location
    description = Column(Text, nullable=False)  # Maps to job_description
    raw_text = Column(Text, nullable=True)
    keywords = Column(Text, nullable=True)
    status = Column(String(50), default="Active")  # Maps to status
    department = Column(String(100), nullable=True)
    required_skills = Column(Text, nullable=True)  # Maps to job_skills
    experience_level = Column(String(100), nullable=True)
    education_requirements = Column(Text, nullable=True)
    threshold_score = Column(Float, nullable=True)
    source_file = Column(String(255), nullable=True)
    salary_range = Column(String(50), nullable=True)  # Maps to job_salary
    source = Column(String(255), nullable=True)
    posted_date = Column(DateTime, nullable=True)  # Maps to posted_date
    applications_received = Column(Integer, nullable=True)  # Maps to applications_received
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # Relationships
    applications = relationship("JobApplication", back_populates="job", cascade="all, delete")
    candidates = relationship("CandidateNew", back_populates="job")
    required_skills_rel = relationship("JobRequiredSkills", back_populates="job", cascade="all, delete")
    threshold_scores = relationship("ThresholdScore", back_populates="job", cascade="all, delete")
    resume_associations = relationship("ResumeJobAssociation", back_populates="job")
    company_details = relationship("CompanyDetails", back_populates="job", uselist=False)

# --- 6. New Candidates ---
class CandidateNew(Base):
    __tablename__ = "candidates_new"
    candidate_id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"), nullable=False)
    name = Column(String(255), nullable=True)  # Maps to candidate_name
    email = Column(String(255), nullable=True)
    job_id = Column(Integer, ForeignKey("job_descriptions.job_id", ondelete="SET NULL"), nullable=True)
    status = Column(String(50), default="Pending")  # Maps to status
    resume_url = Column(String(255), nullable=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # Relationships
    user = relationship("User", back_populates="candidate_profile")
    job = relationship("JobDescription", back_populates="candidates")
    resumes = relationship("ResumeNew", back_populates="candidate")
    threshold_scores = relationship("ThresholdScore", back_populates="candidate")

# --- 7. Resume Job Associations ---
class ResumeJobAssociation(Base):
    __tablename__ = "resume_job_associations"
    id = Column(Integer, primary_key=True, index=True)
    resume_id = Column(Integer, ForeignKey("resumes_new.resume_id"), nullable=False)
    job_id = Column(Integer, ForeignKey("job_descriptions.job_id"), nullable=False)
    match_score = Column(Float, nullable=True)  # Maps to match_score, overall_match
    selection_percentage = Column(Float, nullable=True)  # Maps to selection_percentage
    rejection_percentage = Column(Float, nullable=True)  # Maps to rejection_percentage
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    # Relationships
    resume = relationship("ResumeNew", back_populates="job_associations")
    job = relationship("JobDescription", back_populates="resume_associations")

# --- 8. Job Applications ---
class JobApplication(Base):
    __tablename__ = "job_applications"
    application_id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"))
    job_id = Column(Integer, ForeignKey("job_descriptions.job_id", ondelete="CASCADE"))
    resume_id = Column(Integer, ForeignKey("resumes_new.resume_id", ondelete="CASCADE"))
    status = Column(String(20), default="applied")  # Maps to status
    applied_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))  # Maps to date
    note = Column(Text, nullable=True)  # Maps to note
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    # Relationships
    user = relationship("User", back_populates="applications")
    job = relationship("JobDescription", back_populates="applications")
    resume = relationship("ResumeNew", back_populates="applications")
    timeline = relationship("ApplicationTimeline", back_populates="application", cascade="all, delete")
