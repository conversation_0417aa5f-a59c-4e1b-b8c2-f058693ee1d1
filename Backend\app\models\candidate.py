"""
Candidate and related database models
"""

from datetime import datetime, timezone, date, time
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, ForeignKey, Date, Time, JSON
from sqlalchemy.orm import relationship

from app.database.connection import Base

class Candidate(Base):
    __tablename__ = "candidates"
    candidate_id = Column(String, primary_key=True, index=True)
    first_name = Column(String(100), nullable=True)
    last_name = Column(String(100), nullable=True)
    email = Column(String(255), nullable=True)
    phone = Column(String(20), nullable=True)
    position = Column(String(200), nullable=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    resume = relationship("Resume", back_populates="candidate", uselist=False, cascade="all, delete-orphan")
    interview_feedback = relationship("InterviewFeedback", back_populates="candidate", uselist=False, cascade="all, delete-orphan")
    availability_slots = relationship("AvailabilitySlot", back_populates="candidate", cascade="all, delete-orphan")
    interview_bookings = relationship("InterviewBooking", back_populates="candidate", cascade="all, delete-orphan")
    notice_period = relationship("NoticePeriod", back_populates="candidate", uselist=False, cascade="all, delete-orphan")
    payslips = relationship("Payslip", back_populates="candidate", cascade="all, delete-orphan")
    background_verification = relationship("BackgroundVerification", back_populates="candidate", uselist=False, cascade="all, delete-orphan")

class Resume(Base):
    __tablename__ = "resumes"
    id = Column(Integer, primary_key=True, index=True)
    candidate_id = Column(String, ForeignKey("candidates.candidate_id"), nullable=False, unique=True)
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=False)
    content_type = Column(String(100), nullable=False)
    is_visible = Column(Boolean, default=True)
    uploaded_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    candidate = relationship("Candidate", back_populates="resume")

class AvailabilitySlot(Base):
    __tablename__ = "availability_slots"
    id = Column(Integer, primary_key=True, index=True)
    candidate_id = Column(String, ForeignKey("candidates.candidate_id"), nullable=False)
    slot_type = Column(String(20), nullable=False, default="general")  # Only 'general' now
    interview_date = Column(Date, nullable=False)  # Actual calendar date (DD/MM/YYYY)
    start_time = Column(Time, nullable=False)
    end_time = Column(Time, nullable=False)
    is_booked = Column(Boolean, default=False)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    candidate = relationship("Candidate", back_populates="availability_slots")

class ShortlistedJobAvailability(Base):
    __tablename__ = "shortlisted_job_availability"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)
    candidate_id = Column(String, ForeignKey("candidates.candidate_id"), nullable=False)
    company_name = Column(String(255), nullable=False)
    job_position = Column(String(255), nullable=False)
    interview_date = Column(Date, nullable=False)
    start_time = Column(Time, nullable=False)
    end_time = Column(Time, nullable=False)
    is_booked = Column(Boolean, default=False)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    candidate = relationship("Candidate")

class InterviewBooking(Base):
    __tablename__ = "interview_bookings"
    id = Column(Integer, primary_key=True, index=True)
    slot_id = Column(Integer, ForeignKey("availability_slots.id"), nullable=False)
    candidate_id = Column(String, ForeignKey("candidates.candidate_id"), nullable=False)
    company_name = Column(String(255), nullable=False)
    job_position = Column(String(255), nullable=False)
    interview_date = Column(Date, nullable=False)
    start_time = Column(Time, nullable=False)
    end_time = Column(Time, nullable=False)
    booking_status = Column(String(20), default="confirmed")  # confirmed, cancelled, completed
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    candidate = relationship("Candidate", back_populates="interview_bookings")
    slot = relationship("AvailabilitySlot")

class NoticePeriod(Base):
    __tablename__ = "notice_periods"
    id = Column(Integer, primary_key=True, index=True)
    candidate_id = Column(String, ForeignKey("candidates.candidate_id"), nullable=False, unique=True)
    notice_period_days = Column(Integer, nullable=False, default=30)  # Default 30 days
    current_company = Column(String(255), nullable=True)
    current_position = Column(String(255), nullable=True)
    current_salary = Column(Integer, nullable=True)  # Monthly salary
    expected_salary = Column(Integer, nullable=True)  # Expected monthly salary
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    candidate = relationship("Candidate", back_populates="notice_period")

class Payslip(Base):
    __tablename__ = "payslips"
    id = Column(Integer, primary_key=True, index=True)
    candidate_id = Column(String, ForeignKey("candidates.candidate_id"), nullable=False)
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=False)
    content_type = Column(String(100), nullable=False)
    month_year = Column(String(50), nullable=False)  # Format: MM/YYYY_timestamp for uniqueness
    salary_amount = Column(Integer, nullable=True)  # Extracted salary amount if available
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    candidate = relationship("Candidate", back_populates="payslips")

class BackgroundVerification(Base):
    __tablename__ = "background_verification"
    id = Column(Integer, primary_key=True, index=True)
    candidate_id = Column(String, ForeignKey("candidates.candidate_id"), nullable=False, unique=True)
    status = Column(String(20), default="In Progress")  # "In Progress", "Completed", "Failed", "Pending"
    verification_notes = Column(Text, nullable=True)
    employment_verified = Column(Boolean, default=False)
    education_verified = Column(Boolean, default=False)
    address_verified = Column(Boolean, default=False)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    candidate = relationship("Candidate", back_populates="background_verification")
    address_proofs = relationship("AddressProof", back_populates="background_verification", cascade="all, delete-orphan")
    provident_fund = relationship("ProvidentFund", back_populates="background_verification", uselist=False, cascade="all, delete-orphan")

class AddressProof(Base):
    __tablename__ = "address_proofs"
    id = Column(Integer, primary_key=True, index=True)
    candidate_id = Column(String, ForeignKey("candidates.candidate_id"), nullable=False)  # Direct candidate reference for easier analysis
    background_verification_id = Column(Integer, ForeignKey("background_verification.id"), nullable=False)
    document_type = Column(String(50), nullable=False)  # "Aadhar Card", "Passport", "Driver's License", "Utility Bill", "Voter ID"
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=False)
    content_type = Column(String(100), nullable=False)
    is_verified = Column(Boolean, default=False)
    # New DigiLocker verification fields
    digilocker_verified = Column(Boolean, default=False)  # Whether verified against DigiLocker
    digilocker_document_id = Column(String(100), nullable=True)  # Link to DigiLocker document
    verification_status = Column(String(20), default="pending")  # pending, verified, failed, genuine, fake
    verification_details = Column(JSON, nullable=True)  # Store verification details from DigiLocker
    verified_at = Column(DateTime, nullable=True)  # When DigiLocker verification was completed
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    # Relationships
    candidate = relationship("Candidate")  # Direct relationship to candidate for easier queries
    background_verification = relationship("BackgroundVerification", back_populates="address_proofs")

class ProvidentFund(Base):
    __tablename__ = "provident_fund"
    id = Column(Integer, primary_key=True, index=True)
    background_verification_id = Column(Integer, ForeignKey("background_verification.id"), nullable=False, unique=True)
    account_number = Column(String(50), nullable=True)
    uan_number = Column(String(20), nullable=True)  # Universal Account Number
    previous_employer = Column(String(255), nullable=True)
    current_employer = Column(String(255), nullable=True)
    additional_details = Column(Text, nullable=True)
    is_verified = Column(Boolean, default=False)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    background_verification = relationship("BackgroundVerification", back_populates="provident_fund")

class DigiLockerAuth(Base):
    __tablename__ = "digilocker_auth"
    id = Column(Integer, primary_key=True, index=True)
    candidate_id = Column(String, ForeignKey("candidates.candidate_id"), nullable=False, unique=True)
    access_token = Column(Text, nullable=True)
    refresh_token = Column(Text, nullable=True)
    token_expires_at = Column(DateTime, nullable=True)
    digilocker_user_id = Column(String(100), nullable=True)
    auth_status = Column(String(20), default="pending")  # pending, authenticated, failed, expired
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    candidate = relationship("Candidate")

class DigiLockerDocument(Base):
    __tablename__ = "digilocker_documents"
    id = Column(Integer, primary_key=True, index=True)
    candidate_id = Column(String, ForeignKey("candidates.candidate_id"), nullable=False)
    document_type = Column(String(50), nullable=False)  # "aadhaar", "pan", "driving_license", "passport", etc.
    document_id = Column(String(100), nullable=False)  # DigiLocker document ID
    document_name = Column(String(255), nullable=False)
    document_uri = Column(Text, nullable=False)
    issuer = Column(String(100), nullable=True)
    issue_date = Column(Date, nullable=True)
    expiry_date = Column(Date, nullable=True)
    verification_status = Column(String(20), default="pending")  # pending, verified, failed
    document_data = Column(JSON, nullable=True)  # Store extracted document data
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    candidate = relationship("Candidate")
