"""
Interview feedback Pydantic schemas
"""

from datetime import datetime
from typing import List
from pydantic import BaseModel

# Interview Feedback Models (Simple Database Models)
class InterviewFeedbackResponse(BaseModel):
    candidate_id: str
    overall_score: int
    max_score: int = 100
    general_comments: str
    questions: List[dict]
    created_at: datetime
    updated_at: datetime
    class Config:
        from_attributes = True
