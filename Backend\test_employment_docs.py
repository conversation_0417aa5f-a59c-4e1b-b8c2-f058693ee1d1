#!/usr/bin/env python3
"""
Test script for Employment Documents API
"""

import requests
import io

def test_offer_letter_upload():
    print('🧪 TESTING EMPLOYMENT DOCUMENTS API...')
    
    # Test data
    candidate_id = '2'
    url = f'http://localhost:8000/candidates/{candidate_id}/offer-letter'
    
    # Create a mock PDF file
    mock_file_content = b'%PDF-1.4 Mock PDF content for testing'
    files = {
        'file': ('test_offer_letter.pdf', io.BytesIO(mock_file_content), 'application/pdf')
    }
    
    data = {
        'company_name': 'DataSphere Analytics',
        'job_position': 'Full Stack Engineer',
        'notes': 'Initial offer received'
    }
    
    try:
        response = requests.post(url, files=files, data=data, timeout=10)
        print(f'📄 Offer Letter Upload Status: {response.status_code}')
        
        if response.status_code == 201:
            result = response.json()
            print(f'✅ Success! Document ID: {result.get("id")}')
            print(f'📁 Filename: {result.get("filename")}')
            print(f'🏢 Company: {result.get("company_name")}')
            print(f'💼 Position: {result.get("job_position")}')
            return result.get("id")
        else:
            print(f'❌ Error: {response.text}')
            return None
            
    except Exception as e:
        print(f'❌ Request failed: {e}')
        return None

def test_get_offer_letters():
    print('\n📋 TESTING GET OFFER LETTERS...')
    
    candidate_id = '2'
    url = f'http://localhost:8000/candidates/{candidate_id}/offer-letter'
    
    try:
        response = requests.get(url, timeout=10)
        print(f'📄 Get Offer Letters Status: {response.status_code}')
        
        if response.status_code == 200:
            results = response.json()
            print(f'✅ Found {len(results)} offer letters')
            for doc in results:
                print(f'  📄 {doc.get("filename")} - {doc.get("company_name")}')
        else:
            print(f'❌ Error: {response.text}')
            
    except Exception as e:
        print(f'❌ Request failed: {e}')

if __name__ == "__main__":
    # Test upload
    doc_id = test_offer_letter_upload()
    
    # Test get all
    test_get_offer_letters()
    
    print('\n🎉 Employment Documents API testing completed!')
