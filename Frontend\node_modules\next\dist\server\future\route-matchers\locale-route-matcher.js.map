{"version": 3, "sources": ["../../../../src/server/future/route-matchers/locale-route-matcher.ts"], "names": ["LocaleRouteMatcher", "RouteMatcher", "identity", "definition", "pathname", "i18n", "locale", "match", "options", "result", "test", "params", "detectedLocale"], "mappings": ";;;;+BAcaA;;;eAAAA;;;8BAXgB;AAWtB,MAAMA,2BAEHC,0BAAY;IACpB;;;;GAIC,GACD,IAAWC,WAAmB;YACuB;QAAnD,OAAO,CAAC,EAAE,IAAI,CAACC,UAAU,CAACC,QAAQ,CAAC,cAAc,GAAE,wBAAA,IAAI,CAACD,UAAU,CAACE,IAAI,qBAApB,sBAAsBC,MAAM,CAAC,CAAC;IACnF;IAEA;;;;;;;GAOC,GACD,AAAOC,MACLH,QAAgB,EAChBI,OAAmC,EACP;YAUxB,sEAAsE;QACtE,sBAAsB;QACtBA,eAAiC;QAXrC,sEAAsE;QACtE,gCAAgC;QAChC,MAAMC,SAAS,IAAI,CAACC,IAAI,CAACN,UAAUI;QACnC,IAAI,CAACC,QAAQ,OAAO;QAEpB,OAAO;YACLN,YAAY,IAAI,CAACA,UAAU;YAC3BQ,QAAQF,OAAOE,MAAM;YACrBC,gBAGEJ,CAAAA,4BAAAA,gBAAAA,QAASH,IAAI,qBAAbG,cAAeI,cAAc,OAAI,wBAAA,IAAI,CAACT,UAAU,CAACE,IAAI,qBAApB,sBAAsBC,MAAM;QACjE;IACF;IAEA;;;;;;;GAOC,GACD,AAAOI,KAAKN,QAAgB,EAAEI,OAAmC,EAAE;QACjE,2EAA2E;QAC3E,gEAAgE;QAChE,IAAI,IAAI,CAACL,UAAU,CAACE,IAAI,KAAIG,2BAAAA,QAASH,IAAI,GAAE;YACzC,0EAA0E;YAC1E,2BAA2B;YAC3B,IACE,IAAI,CAACF,UAAU,CAACE,IAAI,CAACC,MAAM,IAC3BE,QAAQH,IAAI,CAACO,cAAc,IAC3B,IAAI,CAACT,UAAU,CAACE,IAAI,CAACC,MAAM,KAAKE,QAAQH,IAAI,CAACO,cAAc,EAC3D;gBACA,OAAO;YACT;YAEA,yEAAyE;YACzE,8BAA8B;YAC9B,OAAO,KAAK,CAACF,KAAKF,QAAQH,IAAI,CAACD,QAAQ;QACzC;QAEA,wEAAwE;QACxE,YAAY;QACZ,OAAO,KAAK,CAACM,KAAKN;IACpB;AACF"}