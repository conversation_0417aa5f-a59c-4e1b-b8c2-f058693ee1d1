# ============================================================================
# 🔧 FASTJOB BACKEND CONFIGURATION
# ============================================================================
# Copy this file to .env and update the values according to your setup

# ============================================================================
# 🗄️ DATABASE CONFIGURATION
# ============================================================================
DB_HOST=localhost
DB_PORT=5433
DB_NAME=postgres
DB_USER=postgres
DB_PASSWORD=Yashwanth@567
DB_POOL_SIZE=5
DB_MAX_OVERFLOW=10

# ============================================================================
# 📁 FILE STORAGE CONFIGURATION
# ============================================================================
UPLOAD_DIR=uploads

# File size limits (in MB)
MAX_RESUME_SIZE_MB=5
MAX_DOCUMENT_SIZE_MB=10

# ============================================================================
# 🏢 BUSINESS CONFIGURATION
# ============================================================================
# Business hours (24-hour format)
BUSINESS_START_HOUR=9
BUSINESS_END_HOUR=17

# Interview slot duration (in hours)
INTERVIEW_SLOT_DURATION_HOURS=2

# Maximum payslips allowed per candidate
MAX_PAYSLIPS_PER_CANDIDATE=6

# Default notice period (in days)
DEFAULT_NOTICE_PERIOD_DAYS=30

# ============================================================================
# 🌐 SERVER CONFIGURATION
# ============================================================================
SERVER_HOST=0.0.0.0
SERVER_PORT=8001

# ============================================================================
# 📚 API CONFIGURATION
# ============================================================================
API_TITLE=Job Application Dashboard
API_VERSION=1.0.0
API_DESCRIPTION=Complete job application management system with candidate tracking, resume management, interview feedback, and more

# ============================================================================
# 🔐 SUPABASE CONFIGURATION
# ============================================================================
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key

# ============================================================================
# 🔒 DIGILOCKER CONFIGURATION
# ============================================================================
DIGILOCKER_CLIENT_ID=your_digilocker_client_id
DIGILOCKER_CLIENT_SECRET=your_digilocker_client_secret
DIGILOCKER_REDIRECT_URI=http://localhost:8001/auth/digilocker/callback
DIGILOCKER_AUTH_URL=https://api.digitallocker.gov.in/public/oauth2/1/authorize
DIGILOCKER_TOKEN_URL=https://api.digitallocker.gov.in/public/oauth2/1/token
DIGILOCKER_BASE_URL=https://api.digitallocker.gov.in

# ============================================================================
# 📧 EMAIL CONFIGURATION (Optional)
# ============================================================================
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASSWORD=your_app_password
# SMTP_FROM_EMAIL=<EMAIL>

# ============================================================================
# 📱 SMS CONFIGURATION (Optional)
# ============================================================================
# SMS_PROVIDER=twilio
# TWILIO_ACCOUNT_SID=your_twilio_account_sid
# TWILIO_AUTH_TOKEN=your_twilio_auth_token
# TWILIO_PHONE_NUMBER=your_twilio_phone_number

# ============================================================================
# 🔍 LOGGING CONFIGURATION
# ============================================================================
# LOG_LEVEL=INFO
# LOG_FILE=logs/fastjob.log

# ============================================================================
# 🚀 DEPLOYMENT CONFIGURATION
# ============================================================================
# ENVIRONMENT=development
# DEBUG=true
# CORS_ORIGINS=http://localhost:3000,http://localhost:3001
