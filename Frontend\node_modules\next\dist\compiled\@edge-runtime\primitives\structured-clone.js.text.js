module.exports = "\"use strict\";\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __name = (target, value) => __defProp(target, \"name\", { value, configurable: true });\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/primitives/structured-clone.js\nvar structured_clone_exports = {};\n__export(structured_clone_exports, {\n  structuredClone: () => structuredClone2\n});\nmodule.exports = __toCommonJS(structured_clone_exports);\n\n// ../../node_modules/.pnpm/@ungap+structured-clone@1.2.0/node_modules/@ungap/structured-clone/esm/types.js\nvar VOID = -1;\nvar PRIMITIVE = 0;\nvar ARRAY = 1;\nvar OBJECT = 2;\nvar DATE = 3;\nvar REGEXP = 4;\nvar MAP = 5;\nvar SET = 6;\nvar ERROR = 7;\nvar BIGINT = 8;\n\n// ../../node_modules/.pnpm/@ungap+structured-clone@1.2.0/node_modules/@ungap/structured-clone/esm/deserialize.js\nvar env = typeof self === \"object\" ? self : globalThis;\nvar deserializer = /* @__PURE__ */ __name(($, _) => {\n  const as = /* @__PURE__ */ __name((out, index) => {\n    $.set(index, out);\n    return out;\n  }, \"as\");\n  const unpair = /* @__PURE__ */ __name((index) => {\n    if ($.has(index))\n      return $.get(index);\n    const [type, value] = _[index];\n    switch (type) {\n      case PRIMITIVE:\n      case VOID:\n        return as(value, index);\n      case ARRAY: {\n        const arr = as([], index);\n        for (const index2 of value)\n          arr.push(unpair(index2));\n        return arr;\n      }\n      case OBJECT: {\n        const object = as({}, index);\n        for (const [key, index2] of value)\n          object[unpair(key)] = unpair(index2);\n        return object;\n      }\n      case DATE:\n        return as(new Date(value), index);\n      case REGEXP: {\n        const { source, flags } = value;\n        return as(new RegExp(source, flags), index);\n      }\n      case MAP: {\n        const map = as(/* @__PURE__ */ new Map(), index);\n        for (const [key, index2] of value)\n          map.set(unpair(key), unpair(index2));\n        return map;\n      }\n      case SET: {\n        const set = as(/* @__PURE__ */ new Set(), index);\n        for (const index2 of value)\n          set.add(unpair(index2));\n        return set;\n      }\n      case ERROR: {\n        const { name, message } = value;\n        return as(new env[name](message), index);\n      }\n      case BIGINT:\n        return as(BigInt(value), index);\n      case \"BigInt\":\n        return as(Object(BigInt(value)), index);\n    }\n    return as(new env[type](value), index);\n  }, \"unpair\");\n  return unpair;\n}, \"deserializer\");\nvar deserialize = /* @__PURE__ */ __name((serialized) => deserializer(/* @__PURE__ */ new Map(), serialized)(0), \"deserialize\");\n\n// ../../node_modules/.pnpm/@ungap+structured-clone@1.2.0/node_modules/@ungap/structured-clone/esm/serialize.js\nvar EMPTY = \"\";\nvar { toString } = {};\nvar { keys } = Object;\nvar typeOf = /* @__PURE__ */ __name((value) => {\n  const type = typeof value;\n  if (type !== \"object\" || !value)\n    return [PRIMITIVE, type];\n  const asString = toString.call(value).slice(8, -1);\n  switch (asString) {\n    case \"Array\":\n      return [ARRAY, EMPTY];\n    case \"Object\":\n      return [OBJECT, EMPTY];\n    case \"Date\":\n      return [DATE, EMPTY];\n    case \"RegExp\":\n      return [REGEXP, EMPTY];\n    case \"Map\":\n      return [MAP, EMPTY];\n    case \"Set\":\n      return [SET, EMPTY];\n  }\n  if (asString.includes(\"Array\"))\n    return [ARRAY, asString];\n  if (asString.includes(\"Error\"))\n    return [ERROR, asString];\n  return [OBJECT, asString];\n}, \"typeOf\");\nvar shouldSkip = /* @__PURE__ */ __name(([TYPE, type]) => TYPE === PRIMITIVE && (type === \"function\" || type === \"symbol\"), \"shouldSkip\");\nvar serializer = /* @__PURE__ */ __name((strict, json, $, _) => {\n  const as = /* @__PURE__ */ __name((out, value) => {\n    const index = _.push(out) - 1;\n    $.set(value, index);\n    return index;\n  }, \"as\");\n  const pair = /* @__PURE__ */ __name((value) => {\n    if ($.has(value))\n      return $.get(value);\n    let [TYPE, type] = typeOf(value);\n    switch (TYPE) {\n      case PRIMITIVE: {\n        let entry = value;\n        switch (type) {\n          case \"bigint\":\n            TYPE = BIGINT;\n            entry = value.toString();\n            break;\n          case \"function\":\n          case \"symbol\":\n            if (strict)\n              throw new TypeError(\"unable to serialize \" + type);\n            entry = null;\n            break;\n          case \"undefined\":\n            return as([VOID], value);\n        }\n        return as([TYPE, entry], value);\n      }\n      case ARRAY: {\n        if (type)\n          return as([type, [...value]], value);\n        const arr = [];\n        const index = as([TYPE, arr], value);\n        for (const entry of value)\n          arr.push(pair(entry));\n        return index;\n      }\n      case OBJECT: {\n        if (type) {\n          switch (type) {\n            case \"BigInt\":\n              return as([type, value.toString()], value);\n            case \"Boolean\":\n            case \"Number\":\n            case \"String\":\n              return as([type, value.valueOf()], value);\n          }\n        }\n        if (json && \"toJSON\" in value)\n          return pair(value.toJSON());\n        const entries = [];\n        const index = as([TYPE, entries], value);\n        for (const key of keys(value)) {\n          if (strict || !shouldSkip(typeOf(value[key])))\n            entries.push([pair(key), pair(value[key])]);\n        }\n        return index;\n      }\n      case DATE:\n        return as([TYPE, value.toISOString()], value);\n      case REGEXP: {\n        const { source, flags } = value;\n        return as([TYPE, { source, flags }], value);\n      }\n      case MAP: {\n        const entries = [];\n        const index = as([TYPE, entries], value);\n        for (const [key, entry] of value) {\n          if (strict || !(shouldSkip(typeOf(key)) || shouldSkip(typeOf(entry))))\n            entries.push([pair(key), pair(entry)]);\n        }\n        return index;\n      }\n      case SET: {\n        const entries = [];\n        const index = as([TYPE, entries], value);\n        for (const entry of value) {\n          if (strict || !shouldSkip(typeOf(entry)))\n            entries.push(pair(entry));\n        }\n        return index;\n      }\n    }\n    const { message } = value;\n    return as([TYPE, { name: type, message }], value);\n  }, \"pair\");\n  return pair;\n}, \"serializer\");\nvar serialize = /* @__PURE__ */ __name((value, { json, lossy } = {}) => {\n  const _ = [];\n  return serializer(!(json || lossy), !!json, /* @__PURE__ */ new Map(), _)(value), _;\n}, \"serialize\");\n\n// ../../node_modules/.pnpm/@ungap+structured-clone@1.2.0/node_modules/@ungap/structured-clone/esm/index.js\nvar esm_default = typeof structuredClone === \"function\" ? (\n  /* c8 ignore start */\n  (any, options) => options && (\"json\" in options || \"lossy\" in options) ? deserialize(serialize(any, options)) : structuredClone(any)\n) : (any, options) => deserialize(serialize(any, options));\n\n// src/primitives/structured-clone.js\nfunction structuredClone2(value, options) {\n  if (value instanceof ReadableStream) {\n    const transform = new TransformStream({});\n    value.pipeTo(transform.writable);\n    return transform.readable;\n  }\n  return esm_default(value, options);\n}\n__name(structuredClone2, \"structuredClone\");\n// Annotate the CommonJS export names for ESM import in node:\n0 && (module.exports = {\n  structuredClone\n});\n"