{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.2", "@mui/material": "^7.1.2", "@supabase/auth-ui-react": "^0.4.6", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.39.0", "chart.js": "^4.5.0", "lucide-react": "^0.518.0", "next": "^14.0.0", "react": "^18.0.0", "react-big-calendar": "^1.19.4", "react-chartjs-2": "^5.3.0", "react-circular-progressbar": "^2.2.0", "react-dom": "^18.0.0", "react-icons": "^5.5.0", "react-toastify": "^11.0.5", "recharts": "^2.15.3"}, "devDependencies": {"autoprefixer": "^10.4.16", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.0"}}