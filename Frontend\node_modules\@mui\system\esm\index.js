/**
 * @mui/system v7.1.1
 *
 * @license MIT
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import _formatErrorMessage from "@mui/utils/formatMuiErrorMessage";
export { css, keyframes, StyledEngineProvider } from '@mui/styled-engine';
export { default as GlobalStyles } from "./GlobalStyles/index.js";
export { default as borders } from "./borders/index.js";
export * from "./borders/index.js";
export { default as breakpoints } from "./breakpoints/index.js";
export { default as cssContainerQueries } from "./cssContainerQueries/index.js";
export { handleBreakpoints, mergeBreakpointsInOrder, resolveBreakpointValues as unstable_resolveBreakpointValues } from "./breakpoints/index.js";
export { default as compose } from "./compose/index.js";
export { default as display } from "./display/index.js";
export { default as flexbox } from "./flexbox/index.js";
export * from "./flexbox/index.js";
export { default as grid } from "./cssGrid/index.js";
export * from "./cssGrid/index.js";
export { default as palette } from "./palette/index.js";
export * from "./palette/index.js";
export { default as positions } from "./positions/index.js";
export * from "./positions/index.js";
export { default as shadows } from "./shadows/index.js";
export { default as sizing } from "./sizing/index.js";
export * from "./sizing/index.js";
export { default as spacing } from "./spacing/index.js";
export * from "./spacing/index.js";
export { default as style, getPath, getStyleValue } from "./style/index.js";
export { default as typography } from "./typography/index.js";
export * from "./typography/index.js";
export { default as unstable_styleFunctionSx, unstable_createStyleFunctionSx, extendSxProp as unstable_extendSxProp, unstable_defaultSxConfig } from "./styleFunctionSx/index.js";
// TODO: Remove this function in v6
// eslint-disable-next-line @typescript-eslint/naming-convention
export function experimental_sx() {
  throw new Error(process.env.NODE_ENV !== "production" ? 'MUI: The `experimental_sx` has been moved to `theme.unstable_sx`.' + 'For more details, see https://github.com/mui/material-ui/pull/35150.' : _formatErrorMessage(19));
}
export { default as unstable_getThemeValue } from "./getThemeValue/index.js";
export { default as Box } from "./Box/index.js";
export { default as createBox } from "./createBox/index.js";
export { default as createStyled } from "./createStyled/index.js";
export * from "./createStyled/index.js";
export { default as styled } from "./styled/index.js";
export { default as createTheme } from "./createTheme/index.js";
export { default as createBreakpoints } from "./createBreakpoints/createBreakpoints.js";
export { default as createSpacing } from "./createTheme/createSpacing.js";
export { default as shape } from "./createTheme/shape.js";
export { default as useThemeProps, getThemeProps } from "./useThemeProps/index.js";
export { default as useTheme } from "./useTheme/index.js";
export { default as useThemeWithoutDefault } from "./useThemeWithoutDefault/index.js";
export { default as useMediaQuery } from "./useMediaQuery/index.js";
export * from "./colorManipulator/index.js";
export { default as ThemeProvider } from "./ThemeProvider/index.js";
export { default as unstable_memoTheme } from "./memoTheme.js";
export { default as unstable_createCssVarsProvider } from "./cssVars/createCssVarsProvider.js";
export { default as unstable_createGetCssVar } from "./cssVars/createGetCssVar.js";
export { default as unstable_cssVarsParser } from "./cssVars/cssVarsParser.js";
export { default as unstable_prepareCssVars } from "./cssVars/prepareCssVars.js";
export { default as unstable_createCssVarsTheme } from "./cssVars/createCssVarsTheme.js";
export { default as responsivePropType } from "./responsivePropType/index.js";
export { default as RtlProvider } from "./RtlProvider/index.js";
export * from "./RtlProvider/index.js";
export * from "./version/index.js";

/** ----------------- */
/** Layout components */
export { default as createContainer } from "./Container/createContainer.js";
export { default as Container } from "./Container/index.js";
export * from "./Container/index.js";
export { default as Grid } from "./Grid/Grid.js";
export * from "./Grid/index.js";
export { default as Stack } from "./Stack/Stack.js";
export * from "./Stack/index.js";