#!/usr/bin/env python3
"""
🚀 FASTJOB - Clean Job Application Dashboard API

📋 ESSENTIAL FLOWS:
1. 👤 Candidate Management
2. 📄 Resume Management (Upload, view, update, download, show/hide)
3. 🤖 LLM Interview Feedback (Questions + Answers → AI generates scores & feedback)

🗄️ Database: PostgreSQL (Port 5433, Password: <PERSON><PERSON><PERSON><PERSON>@567)
"""

import os
import logging
import uvicorn
import uuid
import shutil
import httpx
import json
from pathlib import Path
from typing import Optional, List
from datetime import datetime, timezone, date, time, timedelta
from urllib.parse import quote_plus, urlencode
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

try:
    from dateutil.relativedelta import relativedelta
except ImportError:
    relativedelta = None

# FastAPI imports
from fastapi import FastAPI, Depends, HTTPException, status, UploadFile, File, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse, RedirectResponse

# Database imports
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Boolean, Text, ForeignKey, Date, Time, JSON, text
from sqlalchemy.orm import Session, sessionmaker, declarative_base, relationship

# Pydantic imports
from pydantic import BaseModel

# Supabase imports
from supabase import create_client, Client

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ============================================================================
# 🗄️ DATABASE CONFIGURATION
# ============================================================================

password = quote_plus("Yashwanth@567")
DATABASE_URL = f"postgresql://postgres:{password}@localhost:5433/postgres"

engine = create_engine(DATABASE_URL, pool_size=5, max_overflow=10)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

UPLOAD_DIR = Path("uploads")
UPLOAD_DIR.mkdir(exist_ok=True)

# ============================================================================
# 🔐 SUPABASE & DIGILOCKER CONFIGURATION
# ============================================================================

# Supabase Configuration
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_ANON_KEY = os.getenv("SUPABASE_ANON_KEY")

# Initialize Supabase client
try:
    if SUPABASE_URL and SUPABASE_ANON_KEY:
        supabase: Client = create_client(SUPABASE_URL, SUPABASE_ANON_KEY)
        logger.info(f"✅ Supabase client initialized successfully: {SUPABASE_URL}")
    else:
        supabase = None
        logger.warning("⚠️ Supabase configuration missing - sync features disabled")
        logger.warning(f"SUPABASE_URL: {'✓' if SUPABASE_URL else '✗'}")
        logger.warning(f"SUPABASE_ANON_KEY: {'✓' if SUPABASE_ANON_KEY else '✗'}")
except Exception as e:
    supabase = None
    logger.error(f"❌ Failed to initialize Supabase client: {str(e)}")

# DigiLocker Configuration
DIGILOCKER_CLIENT_ID = os.getenv("DIGILOCKER_CLIENT_ID")
DIGILOCKER_CLIENT_SECRET = os.getenv("DIGILOCKER_CLIENT_SECRET")
DIGILOCKER_REDIRECT_URI = os.getenv("DIGILOCKER_REDIRECT_URI")
DIGILOCKER_AUTH_URL = os.getenv("DIGILOCKER_AUTH_URL")
DIGILOCKER_TOKEN_URL = os.getenv("DIGILOCKER_TOKEN_URL")
DIGILOCKER_BASE_URL = os.getenv("DIGILOCKER_BASE_URL")

# ============================================================================
# 🗃️ DATABASE MODELS
# ============================================================================

class Candidate(Base):
    __tablename__ = "candidates"
    candidate_id = Column(String, primary_key=True, index=True)
    first_name = Column(String(100), nullable=True)
    last_name = Column(String(100), nullable=True)
    email = Column(String(255), nullable=True)
    phone = Column(String(20), nullable=True)
    position = Column(String(200), nullable=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    resume = relationship("Resume", back_populates="candidate", uselist=False, cascade="all, delete-orphan")
    interview_feedback = relationship("InterviewFeedback", back_populates="candidate", uselist=False, cascade="all, delete-orphan")
    availability_slots = relationship("AvailabilitySlot", back_populates="candidate", cascade="all, delete-orphan")
    interview_bookings = relationship("InterviewBooking", back_populates="candidate", cascade="all, delete-orphan")
    notice_period = relationship("NoticePeriod", back_populates="candidate", uselist=False, cascade="all, delete-orphan")
    payslips = relationship("Payslip", back_populates="candidate", cascade="all, delete-orphan")
    background_verification = relationship("BackgroundVerification", back_populates="candidate", uselist=False, cascade="all, delete-orphan")

class Resume(Base):
    __tablename__ = "resumes"
    id = Column(Integer, primary_key=True, index=True)
    candidate_id = Column(String, ForeignKey("candidates.candidate_id"), nullable=False, unique=True)
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=False)
    content_type = Column(String(100), nullable=False)
    is_visible = Column(Boolean, default=True)
    uploaded_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    candidate = relationship("Candidate", back_populates="resume")

class InterviewFeedback(Base):
    __tablename__ = "interview_feedback"
    id = Column(Integer, primary_key=True, index=True)
    candidate_id = Column(String, ForeignKey("candidates.candidate_id"), nullable=False, unique=True)
    overall_score = Column(Integer, nullable=False)
    max_score = Column(Integer, default=100)
    general_comments = Column(Text, nullable=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    candidate = relationship("Candidate", back_populates="interview_feedback")
    questions = relationship("InterviewQuestion", back_populates="feedback", cascade="all, delete-orphan")

class InterviewQuestion(Base):
    __tablename__ = "interview_questions"
    id = Column(Integer, primary_key=True, index=True)
    feedback_id = Column(Integer, ForeignKey("interview_feedback.id"), nullable=False)
    question = Column(Text, nullable=False)
    answer = Column(Text, nullable=True)
    score = Column(Integer, nullable=False)
    max_score = Column(Integer, default=100)
    question_feedback = Column(Text, nullable=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    feedback = relationship("InterviewFeedback", back_populates="questions")

class AvailabilitySlot(Base):
    __tablename__ = "availability_slots"
    id = Column(Integer, primary_key=True, index=True)
    candidate_id = Column(String, ForeignKey("candidates.candidate_id"), nullable=False)
    slot_type = Column(String(20), nullable=False, default="general")  # Only 'general' now
    interview_date = Column(Date, nullable=False)  # Actual calendar date (DD/MM/YYYY)
    start_time = Column(Time, nullable=False)
    end_time = Column(Time, nullable=False)
    is_booked = Column(Boolean, default=False)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    candidate = relationship("Candidate", back_populates="availability_slots")

class ShortlistedJobAvailability(Base):
    __tablename__ = "shortlisted_job_availability"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)
    candidate_id = Column(String, ForeignKey("candidates.candidate_id"), nullable=False)
    company_name = Column(String(255), nullable=False)
    job_position = Column(String(255), nullable=False)
    interview_date = Column(Date, nullable=False)
    start_time = Column(Time, nullable=False)
    end_time = Column(Time, nullable=False)
    is_booked = Column(Boolean, default=False)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    candidate = relationship("Candidate")

class InterviewBooking(Base):
    __tablename__ = "interview_bookings"
    id = Column(Integer, primary_key=True, index=True)
    slot_id = Column(Integer, ForeignKey("availability_slots.id"), nullable=False)
    candidate_id = Column(String, ForeignKey("candidates.candidate_id"), nullable=False)
    company_name = Column(String(255), nullable=False)
    job_position = Column(String(255), nullable=False)
    interview_date = Column(Date, nullable=False)
    start_time = Column(Time, nullable=False)
    end_time = Column(Time, nullable=False)
    booking_status = Column(String(20), default="confirmed")  # confirmed, cancelled, completed
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    candidate = relationship("Candidate", back_populates="interview_bookings")
    slot = relationship("AvailabilitySlot")

class NoticePeriod(Base):
    __tablename__ = "notice_periods"
    id = Column(Integer, primary_key=True, index=True)
    candidate_id = Column(String, ForeignKey("candidates.candidate_id"), nullable=False, unique=True)
    notice_period_days = Column(Integer, nullable=False, default=30)  # Default 30 days
    current_company = Column(String(255), nullable=True)
    current_position = Column(String(255), nullable=True)
    current_salary = Column(Integer, nullable=True)  # Monthly salary
    expected_salary = Column(Integer, nullable=True)  # Expected monthly salary
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    candidate = relationship("Candidate", back_populates="notice_period")

class Payslip(Base):
    __tablename__ = "payslips"
    id = Column(Integer, primary_key=True, index=True)
    candidate_id = Column(String, ForeignKey("candidates.candidate_id"), nullable=False)
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=False)
    content_type = Column(String(100), nullable=False)
    month_year = Column(String(50), nullable=False)  # Format: MM/YYYY_timestamp for uniqueness
    salary_amount = Column(Integer, nullable=True)  # Extracted salary amount if available
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    candidate = relationship("Candidate", back_populates="payslips")

class BackgroundVerification(Base):
    __tablename__ = "background_verification"
    id = Column(Integer, primary_key=True, index=True)
    candidate_id = Column(String, ForeignKey("candidates.candidate_id"), nullable=False, unique=True)
    status = Column(String(20), default="In Progress")  # "In Progress", "Completed", "Failed", "Pending"
    verification_notes = Column(Text, nullable=True)
    employment_verified = Column(Boolean, default=False)
    education_verified = Column(Boolean, default=False)
    address_verified = Column(Boolean, default=False)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    candidate = relationship("Candidate", back_populates="background_verification")
    address_proofs = relationship("AddressProof", back_populates="background_verification", cascade="all, delete-orphan")
    provident_fund = relationship("ProvidentFund", back_populates="background_verification", uselist=False, cascade="all, delete-orphan")

class AddressProof(Base):
    __tablename__ = "address_proofs"
    id = Column(Integer, primary_key=True, index=True)
    candidate_id = Column(String, ForeignKey("candidates.candidate_id"), nullable=False)  # Direct candidate reference for easier analysis
    background_verification_id = Column(Integer, ForeignKey("background_verification.id"), nullable=False)
    document_type = Column(String(50), nullable=False)  # "Aadhar Card", "Passport", "Driver's License", "Utility Bill", "Voter ID"
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=False)
    content_type = Column(String(100), nullable=False)
    is_verified = Column(Boolean, default=False)
    # New DigiLocker verification fields
    digilocker_verified = Column(Boolean, default=False)  # Whether verified against DigiLocker
    digilocker_document_id = Column(String(100), nullable=True)  # Link to DigiLocker document
    verification_status = Column(String(20), default="pending")  # pending, verified, failed, genuine, fake
    verification_details = Column(JSON, nullable=True)  # Store verification details from DigiLocker
    verified_at = Column(DateTime, nullable=True)  # When DigiLocker verification was completed
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    # Relationships
    candidate = relationship("Candidate")  # Direct relationship to candidate for easier queries
    background_verification = relationship("BackgroundVerification", back_populates="address_proofs")

class ProvidentFund(Base):
    __tablename__ = "provident_fund"
    id = Column(Integer, primary_key=True, index=True)
    background_verification_id = Column(Integer, ForeignKey("background_verification.id"), nullable=False, unique=True)
    account_number = Column(String(50), nullable=True)
    uan_number = Column(String(20), nullable=True)  # Universal Account Number
    previous_employer = Column(String(255), nullable=True)
    current_employer = Column(String(255), nullable=True)
    additional_details = Column(Text, nullable=True)
    is_verified = Column(Boolean, default=False)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    background_verification = relationship("BackgroundVerification", back_populates="provident_fund")

class DigiLockerAuth(Base):
    __tablename__ = "digilocker_auth"
    id = Column(Integer, primary_key=True, index=True)
    candidate_id = Column(String, ForeignKey("candidates.candidate_id"), nullable=False, unique=True)
    access_token = Column(Text, nullable=True)
    refresh_token = Column(Text, nullable=True)
    token_expires_at = Column(DateTime, nullable=True)
    digilocker_user_id = Column(String(100), nullable=True)
    auth_status = Column(String(20), default="pending")  # pending, authenticated, failed, expired
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    candidate = relationship("Candidate")

class DigiLockerDocument(Base):
    __tablename__ = "digilocker_documents"
    id = Column(Integer, primary_key=True, index=True)
    candidate_id = Column(String, ForeignKey("candidates.candidate_id"), nullable=False)
    document_type = Column(String(50), nullable=False)  # "aadhaar", "pan", "driving_license", "passport", etc.
    document_id = Column(String(100), nullable=False)  # DigiLocker document ID
    document_name = Column(String(255), nullable=False)
    document_uri = Column(Text, nullable=False)
    issuer = Column(String(100), nullable=True)
    issue_date = Column(Date, nullable=True)
    expiry_date = Column(Date, nullable=True)
    verification_status = Column(String(20), default="pending")  # pending, verified, failed
    document_data = Column(JSON, nullable=True)  # Store extracted document data
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    candidate = relationship("Candidate")

# Create tables (optimized for faster startup)
# Note: JobApplication table will be created when the model is defined later
Base.metadata.create_all(bind=engine)

# Database setup functions
def setup_database():
    """Ensure database schema exists with proper structure"""
    try:
        logger.info("🔄 Setting up database schema...")

        # Create all tables with the updated schema
        Base.metadata.create_all(bind=engine)
        logger.info("✅ Database schema setup completed successfully")

        # Safely check and add candidate_id column to address_proofs if needed
        from sqlalchemy import text, inspect
        with engine.connect() as conn:
            inspector = inspect(engine)
            if "address_proofs" in inspector.get_table_names():
                columns = [col['name'] for col in inspector.get_columns("address_proofs")]
                if 'candidate_id' not in columns:
                    logger.info("🔧 Adding candidate_id column to address_proofs table...")
                    try:
                        conn.execute(text("ALTER TABLE address_proofs ADD COLUMN candidate_id VARCHAR"))
                        conn.commit()

                        # Use ORM to safely update existing records with candidate_id
                        with SessionLocal() as db:
                            # Get all address proofs without candidate_id
                            address_proofs_to_update = db.query(AddressProof).filter(
                                AddressProof.candidate_id.is_(None)
                            ).all()

                            for address_proof in address_proofs_to_update:
                                # Get the background verification record
                                bg_verification = db.query(BackgroundVerification).filter(
                                    BackgroundVerification.id == address_proof.background_verification_id
                                ).first()

                                if bg_verification:
                                    address_proof.candidate_id = bg_verification.candidate_id

                            db.commit()
                        logger.info("✅ Added candidate_id column to address_proofs table")
                    except Exception as e:
                        logger.warning(f"⚠️ Could not add candidate_id column: {str(e)}")
                        conn.rollback()
                else:
                    logger.info("✅ address_proofs table already has candidate_id column")

    except Exception as e:
        logger.error(f"Database setup error: {str(e)}")

def verify_file_database_sync():
    """Verify that uploaded files have corresponding database records"""
    try:
        logger.info("🔍 Verifying file-database synchronization...")

        if not UPLOAD_DIR.exists():
            logger.info("📁 No uploads directory found")
            return

        # Get all uploaded files
        uploaded_files = list(UPLOAD_DIR.glob("*"))
        logger.info(f"📁 Found {len(uploaded_files)} files in uploads directory")

        # Check database connection
        with SessionLocal() as db:
            # Count database records
            resume_count = db.query(Resume).count()
            payslip_count = db.query(Payslip).count()
            address_proof_count = db.query(AddressProof).count()
            bg_verification_count = db.query(BackgroundVerification).count()

            logger.info(f"📊 Database records - Resumes: {resume_count}, Payslips: {payslip_count}, Address Proofs: {address_proof_count}, Background Verifications: {bg_verification_count}")

        logger.info("✅ File-database verification completed")

    except Exception as e:
        logger.error(f"❌ Error during file-database verification: {str(e)}")

# Run database setup and verification (simplified to prevent hanging)
try:
    logger.info("🔄 Setting up database schema...")
    Base.metadata.create_all(bind=engine)
    logger.info("✅ Database tables ready")
except Exception as e:
    logger.error(f"❌ Database setup error: {str(e)}")
    # Continue anyway - tables might already exist

# ============================================================================
# 📝 PYDANTIC MODELS
# ============================================================================

class CandidateCreate(BaseModel):
    candidate_id: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    position: Optional[str] = None

class CandidateResponse(BaseModel):
    candidate_id: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    position: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    class Config:
        from_attributes = True



class ResumeResponse(BaseModel):
    id: int
    candidate_id: str
    filename: str
    original_filename: str
    file_size: int
    content_type: str
    is_visible: bool
    uploaded_at: datetime
    updated_at: datetime
    class Config:
        from_attributes = True

# 📋 NOTICE PERIOD & PAYSLIP MODELS
class NoticePeriodCreate(BaseModel):
    notice_period_days: int = 30
    current_company: Optional[str] = None
    current_position: Optional[str] = None
    current_salary: Optional[int] = None
    expected_salary: Optional[int] = None

class NoticePeriodResponse(BaseModel):
    id: int
    candidate_id: str
    notice_period_days: int
    current_company: Optional[str] = None
    current_position: Optional[str] = None
    current_salary: Optional[int] = None
    expected_salary: Optional[int] = None
    created_at: datetime
    updated_at: datetime
    class Config:
        from_attributes = True

class PayslipResponse(BaseModel):
    id: int
    candidate_id: str
    filename: str
    original_filename: str
    file_size: int
    content_type: str
    month_year: str
    salary_amount: Optional[int] = None
    created_at: datetime
    class Config:
        from_attributes = True

# Interview Feedback Models (Simple Database Models)
class InterviewFeedbackResponse(BaseModel):
    candidate_id: str
    overall_score: int
    max_score: int = 100
    general_comments: str
    questions: List[dict]
    created_at: datetime
    updated_at: datetime
    class Config:
        from_attributes = True

# 🛡️ BACKGROUND VERIFICATION MODELS
class BackgroundVerificationCreate(BaseModel):
    status: str = "In Progress"
    verification_notes: Optional[str] = None
    employment_verified: bool = False
    education_verified: bool = False
    address_verified: bool = False

class BackgroundVerificationResponse(BaseModel):
    id: int
    candidate_id: str
    status: str
    verification_notes: Optional[str] = None
    employment_verified: bool
    education_verified: bool
    address_verified: bool
    created_at: datetime
    updated_at: datetime
    class Config:
        from_attributes = True

class AddressProofResponse(BaseModel):
    id: int
    candidate_id: str  # Added for easier analysis and identification
    document_type: str
    filename: str
    original_filename: str
    file_size: int
    content_type: str
    is_verified: bool
    digilocker_verified: bool
    digilocker_document_id: Optional[str] = None
    verification_status: str
    verification_details: Optional[dict] = None
    verified_at: Optional[datetime] = None
    created_at: datetime
    class Config:
        from_attributes = True

class ProvidentFundCreate(BaseModel):
    account_number: Optional[str] = None
    uan_number: Optional[str] = None
    previous_employer: Optional[str] = None
    current_employer: Optional[str] = None
    additional_details: Optional[str] = None

class ProvidentFundResponse(BaseModel):
    id: int
    account_number: Optional[str] = None
    uan_number: Optional[str] = None
    previous_employer: Optional[str] = None
    current_employer: Optional[str] = None
    additional_details: Optional[str] = None
    is_verified: bool
    created_at: datetime
    updated_at: datetime
    class Config:
        from_attributes = True

# 📅 CALENDAR-BASED AVAILABILITY MODELS
class TimeSlotCreate(BaseModel):
    interview_date: str  # DD/MM/YYYY format
    start_time: str      # HH:MM format (24-hour or 12-hour with AM/PM)
    end_time: str        # HH:MM format (24-hour or 12-hour with AM/PM)

class AvailabilitySlotCreate(BaseModel):
    slot_type: str = "general"  # 'general' or 'job_specific'
    slots: List[TimeSlotCreate]
    company_name: Optional[str] = None  # Required for job_specific
    job_position: Optional[str] = None  # Required for job_specific

class AvailabilitySlotResponse(BaseModel):
    id: int
    candidate_id: str
    slot_type: str
    interview_date: str  # DD/MM/YYYY format
    start_time: str      # HH:MM format
    end_time: str        # HH:MM format
    is_booked: bool
    company_name: Optional[str] = None
    job_position: Optional[str] = None
    created_at: datetime
    class Config:
        from_attributes = True

class InterviewBookingCreate(BaseModel):
    company_name: str
    job_position: str
    interview_date: str  # DD/MM/YYYY format

class InterviewBookingResponse(BaseModel):
    id: int
    slot_id: int
    candidate_id: str
    company_name: str
    job_position: str
    interview_date: str  # DD/MM/YYYY format
    start_time: str      # HH:MM format
    end_time: str        # HH:MM format
    booking_status: str
    created_at: datetime
    updated_at: datetime
    class Config:
        from_attributes = True

# 🔐 DIGILOCKER AUTHENTICATION MODELS
class DigiLockerAuthResponse(BaseModel):
    id: int
    candidate_id: str
    auth_status: str
    digilocker_user_id: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    class Config:
        from_attributes = True

class DigiLockerDocumentResponse(BaseModel):
    id: int
    candidate_id: str
    document_type: str
    document_id: str
    document_name: str
    issuer: Optional[str] = None
    issue_date: Optional[date] = None
    expiry_date: Optional[date] = None
    verification_status: str
    document_data: Optional[dict] = None
    created_at: datetime
    updated_at: datetime
    class Config:
        from_attributes = True

class DigiLockerAuthRequest(BaseModel):
    candidate_id: str

class DigiLockerCallbackRequest(BaseModel):
    code: str
    state: str

# ============================================================================
# 🛠️ UTILITY FUNCTIONS
# ============================================================================

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# ============================================================================
# 🔐 DIGILOCKER SERVICE FUNCTIONS
# ============================================================================

async def generate_digilocker_auth_url(candidate_id: str) -> str:
    """Generate DigiLocker authorization URL"""
    # Always use demo mode for testing since DigiLocker requires official registration
    logger.info(f"🧪 Using DigiLocker demo mode for candidate {candidate_id}")
    return f"http://localhost:8000/auth/digilocker/demo?state={candidate_id}"

    # Real DigiLocker integration (commented out for demo)
    # if not all([DIGILOCKER_CLIENT_ID, DIGILOCKER_AUTH_URL, DIGILOCKER_REDIRECT_URI]):
    #     raise HTTPException(status_code=500, detail="DigiLocker not configured")
    #
    # params = {
    #     "response_type": "code",
    #     "client_id": DIGILOCKER_CLIENT_ID,
    #     "redirect_uri": DIGILOCKER_REDIRECT_URI,
    #     "state": candidate_id,
    #     "scope": "openid profile aadhaar"
    # }
    #
    # auth_url = f"{DIGILOCKER_AUTH_URL}?{urlencode(params)}"
    # return auth_url

async def exchange_code_for_token(code: str, candidate_id: str) -> dict:
    """Exchange authorization code for access token"""
    if not all([DIGILOCKER_CLIENT_ID, DIGILOCKER_CLIENT_SECRET, DIGILOCKER_TOKEN_URL]):
        raise HTTPException(status_code=500, detail="DigiLocker configuration incomplete")

    token_data = {
        "grant_type": "authorization_code",
        "code": code,
        "redirect_uri": DIGILOCKER_REDIRECT_URI,
        "client_id": DIGILOCKER_CLIENT_ID,
        "client_secret": DIGILOCKER_CLIENT_SECRET
    }

    async with httpx.AsyncClient() as client:
        response = await client.post(DIGILOCKER_TOKEN_URL, data=token_data)

        if response.status_code != 200:
            logger.error(f"DigiLocker token exchange failed: {response.text}")
            raise HTTPException(status_code=400, detail="Failed to exchange code for token")

        return response.json()

async def fetch_digilocker_documents(access_token: str) -> list:
    """Fetch documents from DigiLocker"""
    if not DIGILOCKER_BASE_URL:
        raise HTTPException(status_code=500, detail="DigiLocker base URL not configured")

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    documents_url = f"{DIGILOCKER_BASE_URL}/public/oauth2/2/files"

    async with httpx.AsyncClient() as client:
        response = await client.get(documents_url, headers=headers)

        if response.status_code != 200:
            logger.error(f"Failed to fetch DigiLocker documents: {response.text}")
            raise HTTPException(status_code=400, detail="Failed to fetch documents from DigiLocker")

        return response.json().get("items", [])

async def store_digilocker_auth(db: Session, candidate_id: str, token_data: dict, user_info: dict = None):
    """Store DigiLocker authentication data"""
    try:
        # Check if auth record exists
        auth_record = db.query(DigiLockerAuth).filter(
            DigiLockerAuth.candidate_id == candidate_id
        ).first()

        if auth_record:
            # Update existing record
            auth_record.access_token = token_data.get("access_token")
            auth_record.refresh_token = token_data.get("refresh_token")
            auth_record.token_expires_at = datetime.now(timezone.utc) + timedelta(seconds=token_data.get("expires_in", 3600))
            auth_record.auth_status = "authenticated"
            auth_record.digilocker_user_id = user_info.get("sub") if user_info else None
            auth_record.updated_at = datetime.now(timezone.utc)
        else:
            # Create new record
            auth_record = DigiLockerAuth(
                candidate_id=candidate_id,
                access_token=token_data.get("access_token"),
                refresh_token=token_data.get("refresh_token"),
                token_expires_at=datetime.now(timezone.utc) + timedelta(seconds=token_data.get("expires_in", 3600)),
                auth_status="authenticated",
                digilocker_user_id=user_info.get("sub") if user_info else None
            )
            db.add(auth_record)

        db.commit()

        # Sync authentication to Supabase
        await sync_digilocker_auth_to_supabase(candidate_id, auth_record)

        return auth_record

    except Exception as e:
        db.rollback()
        logger.error(f"Failed to store DigiLocker auth for {candidate_id}: {str(e)}")
        raise

async def store_digilocker_documents(db: Session, candidate_id: str, documents: list):
    """Store DigiLocker documents"""
    try:
        for doc in documents:
            # Check if document already exists
            existing_doc = db.query(DigiLockerDocument).filter(
                DigiLockerDocument.candidate_id == candidate_id,
                DigiLockerDocument.document_id == doc.get("id")
            ).first()

            if not existing_doc:
                document_record = DigiLockerDocument(
                    candidate_id=candidate_id,
                    document_type=doc.get("type", "unknown"),
                    document_id=doc.get("id"),
                    document_name=doc.get("name", ""),
                    document_uri=doc.get("uri", ""),
                    issuer=doc.get("issuer"),
                    issue_date=datetime.strptime(doc.get("date"), "%Y-%m-%d").date() if doc.get("date") else None,
                    verification_status="pending",
                    document_data=doc
                )
                db.add(document_record)

        db.commit()

    except Exception as e:
        db.rollback()
        logger.error(f"Failed to store DigiLocker documents for {candidate_id}: {str(e)}")
        raise

async def sync_with_supabase(candidate_id: str, auth_data: dict, documents: list):
    """Sync DigiLocker data with Supabase"""
    if not supabase:
        logger.warning("Supabase client not initialized")
        return

    try:
        # Store authentication data in Supabase
        supabase.table("digilocker_auth").upsert({
            "candidate_id": candidate_id,
            "auth_status": "authenticated",
            "digilocker_user_id": auth_data.get("user_id"),
            "synced_at": datetime.now(timezone.utc).isoformat()
        }).execute()

        # Store documents in Supabase
        for doc in documents:
            supabase.table("digilocker_documents").upsert({
                "candidate_id": candidate_id,
                "document_type": doc.get("type"),
                "document_id": doc.get("id"),
                "document_name": doc.get("name"),
                "verification_status": "pending",
                "synced_at": datetime.now(timezone.utc).isoformat()
            }).execute()

        logger.info(f"Successfully synced DigiLocker data to Supabase for candidate {candidate_id}")

    except Exception as e:
        logger.error(f"Failed to sync with Supabase for {candidate_id}: {str(e)}")
        # Don't raise exception as this is not critical

def save_uploaded_file(file: UploadFile, candidate_id: str) -> tuple:
    file_extension = Path(file.filename).suffix
    unique_filename = f"{candidate_id}_resume_{uuid.uuid4()}{file_extension}"
    file_path = UPLOAD_DIR / unique_filename
    with open(file_path, "wb") as buffer:
        shutil.copyfileobj(file.file, buffer)
    return str(file_path), unique_filename

def save_payslip_file(file: UploadFile, candidate_id: str, month_year: str) -> tuple:
    """Save payslip file with month_year in filename"""
    file_extension = Path(file.filename).suffix
    # Format: CAND001_payslip_06_2025_uuid.pdf
    unique_filename = f"{candidate_id}_payslip_{month_year.replace('/', '_')}_{uuid.uuid4()}{file_extension}"
    file_path = UPLOAD_DIR / unique_filename
    with open(file_path, "wb") as buffer:
        shutil.copyfileobj(file.file, buffer)
    return str(file_path), unique_filename

def save_address_proof_file(file: UploadFile, candidate_id: str, document_type: str) -> tuple:
    """Save address proof file with document type in filename"""
    file_extension = Path(file.filename).suffix
    # Format: CAND001_address_aadhar_uuid.pdf
    doc_type_clean = document_type.lower().replace(" ", "_").replace("'", "")
    unique_filename = f"{candidate_id}_address_{doc_type_clean}_{uuid.uuid4()}{file_extension}"
    file_path = UPLOAD_DIR / unique_filename
    with open(file_path, "wb") as buffer:
        shutil.copyfileobj(file.file, buffer)
    return str(file_path), unique_filename

def validate_address_proof_file(file: UploadFile) -> bool:
    """Validate address proof file type and size"""
    allowed_types = ["application/pdf", "image/jpeg", "image/png", "image/jpg"]
    if file.content_type not in allowed_types:
        raise HTTPException(
            status_code=400,
            detail="Only PDF, JPG, and PNG files are allowed for address proof"
        )

    if file.size > 10 * 1024 * 1024:  # 10MB limit
        raise HTTPException(
            status_code=400,
            detail="Address proof file size must be less than 10MB"
        )

    return True

def validate_document_type(document_type: str) -> bool:
    """Validate document type for address proof - Dynamic validation"""
    # Dynamic document types - easily expandable
    allowed_types = [
        "Aadhaar Card", "PAN Card", "Passport", "Driver's License", "Voter ID",
        "Utility Bill", "Bank Statement", "Rental Agreement", "Property Tax Receipt",
        "Electricity Bill", "Gas Bill", "Water Bill", "Telephone Bill",
        "Mobile Bill", "Internet Bill", "Aadhar Card"  # Keep old spelling for backward compatibility
    ]

    if not document_type or document_type.strip() == "":
        raise HTTPException(
            status_code=400,
            detail="Document type is required"
        )

    if document_type not in allowed_types:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid document type '{document_type}'. Allowed types: {', '.join(sorted(set(allowed_types)))}"
        )
    return True

def validate_payslip_file(file: UploadFile) -> bool:
    """Validate payslip file type and size"""
    allowed_types = ["application/pdf", "image/jpeg", "image/png", "image/jpg"]
    if file.content_type not in allowed_types:
        raise HTTPException(
            status_code=400,
            detail="Only PDF, JPG, and PNG files are allowed for payslips"
        )

    if file.size > 10 * 1024 * 1024:  # 10MB limit for payslips
        raise HTTPException(
            status_code=400,
            detail="Payslip file size must be less than 10MB"
        )

    return True

def validate_payslip_count(db: Session, candidate_id: str, month_year: str) -> bool:
    """Validate that candidate doesn't exceed 6 payslips limit"""
    existing_payslips = db.query(Payslip).filter(
        Payslip.candidate_id == candidate_id
    ).all()

    # Check if this month_year already exists (replacement is allowed)
    existing_month = db.query(Payslip).filter(
        Payslip.candidate_id == candidate_id,
        Payslip.month_year == month_year
    ).first()

    if existing_month:
        return True  # Replacement is allowed

    if len(existing_payslips) >= 6:
        raise HTTPException(
            status_code=400,
            detail="Maximum 6 payslips allowed. Please delete an old payslip before uploading a new one."
        )

    return True

def validate_month_year(month_year: str) -> bool:
    """Validate MM/YYYY format and ensure it's within last 6 months"""
    try:
        payslip_date = datetime.strptime(month_year, "%m/%Y")
        current_date = datetime.now()

        # Calculate 6 months ago using timedelta for better accuracy
        if relativedelta:
            six_months_ago = current_date - relativedelta(months=6)
        else:
            # Fallback calculation
            current_month = current_date.month
            current_year = current_date.year

            if current_month > 6:
                six_months_ago_month = current_month - 6
                six_months_ago_year = current_year
            else:
                six_months_ago_month = current_month + 6
                six_months_ago_year = current_year - 1

            six_months_ago = datetime(six_months_ago_year, six_months_ago_month, 1)

        if payslip_date < six_months_ago:
            raise HTTPException(
                status_code=400,
                detail=f"Payslip date '{month_year}' is older than 6 months. Please upload payslips from the last 6 months only."
            )

        if payslip_date > current_date:
            raise HTTPException(
                status_code=400,
                detail=f"Payslip date '{month_year}' is in the future. Please use a valid past date."
            )

        return True
    except ImportError:
        # Fallback if dateutil is not available
        payslip_date = datetime.strptime(month_year, "%m/%Y")
        current_date = datetime.now()

        # Simple 6-month calculation
        current_month = current_date.month
        current_year = current_date.year

        if current_month > 6:
            six_months_ago_month = current_month - 6
            six_months_ago_year = current_year
        else:
            six_months_ago_month = current_month + 6
            six_months_ago_year = current_year - 1

        six_months_ago = datetime(six_months_ago_year, six_months_ago_month, 1)

        if payslip_date < six_months_ago:
            raise HTTPException(
                status_code=400,
                detail=f"Payslip date '{month_year}' is older than 6 months. Please upload payslips from the last 6 months only."
            )

        if payslip_date > current_date:
            raise HTTPException(
                status_code=400,
                detail=f"Payslip date '{month_year}' is in the future. Please use a valid past date."
            )

        return True
    except ValueError:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid month/year format '{month_year}'. Use MM/YYYY format (e.g., 06/2025)"
        )

# 📅 DATE/TIME UTILITY FUNCTIONS
def parse_date(date_str: str) -> date:
    """Parse DD/MM/YYYY format to date object"""
    try:
        return datetime.strptime(date_str, "%d/%m/%Y").date()
    except ValueError:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid date format '{date_str}'. Use DD/MM/YYYY format (e.g., 15/06/2025)"
        )

def parse_time(time_str: str) -> time:
    """Parse time string (24-hour or 12-hour with AM/PM) to time object"""
    time_str = time_str.strip()

    # Try 24-hour format first (HH:MM)
    try:
        return datetime.strptime(time_str, "%H:%M").time()
    except ValueError:
        pass

    # Try 12-hour format with AM/PM
    try:
        return datetime.strptime(time_str, "%I:%M %p").time()
    except ValueError:
        pass

    # Try 12-hour format without space
    try:
        return datetime.strptime(time_str, "%I:%M%p").time()
    except ValueError:
        pass

    raise HTTPException(
        status_code=400,
        detail=f"Invalid time format '{time_str}'. Use HH:MM (24-hour) or H:MM AM/PM (12-hour)"
    )

def format_time_for_response(time_obj: time) -> str:
    """Format time object to HH:MM string"""
    return time_obj.strftime("%H:%M")

def format_date_for_response(date_obj: date) -> str:
    """Format date object to DD/MM/YYYY string"""
    return date_obj.strftime("%d/%m/%Y")

def validate_business_hours(start_time: time, end_time: time) -> bool:
    """Validate that times are within business hours (9 AM - 5 PM)"""
    business_start = time(9, 0)  # 9:00 AM
    business_end = time(17, 0)   # 5:00 PM

    return (business_start <= start_time <= business_end and
            business_start <= end_time <= business_end)

def validate_slot_duration(start_time: time, end_time: time) -> bool:
    """Validate that slot is exactly 2 hours"""
    start_datetime = datetime.combine(date.today(), start_time)
    end_datetime = datetime.combine(date.today(), end_time)
    duration = end_datetime - start_datetime
    return duration.total_seconds() == 7200  # 2 hours = 7200 seconds

def validate_weekday(interview_date: date) -> bool:
    """Validate that date is a weekday (Monday-Friday)"""
    return interview_date.weekday() < 5  # 0-4 are Monday-Friday

def check_company_time_conflict(db: Session, company_name: str, interview_date: date, start_time: time, end_time: time, exclude_booking_id: int = None) -> bool:
    """Check if company already has an interview booked at this time"""
    query = db.query(InterviewBooking).filter(
        InterviewBooking.company_name == company_name,
        InterviewBooking.interview_date == interview_date,
        InterviewBooking.booking_status == "confirmed"
    )

    if exclude_booking_id:
        query = query.filter(InterviewBooking.id != exclude_booking_id)

    existing_bookings = query.all()

    for booking in existing_bookings:
        # Check for time overlap
        if (start_time < booking.end_time and end_time > booking.start_time):
            return True  # Conflict found

    return False  # No conflict

# ============================================================================
# 🚀 FASTAPI APP
# ============================================================================

app = FastAPI(
    title="Job Application Dashboard",
    description="Complete job application management system with candidate tracking, resume management, interview feedback, and more",
    version="1.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {
        "message": "Job Application Dashboard API",
        "version": "1.0.0",
        "status": "✅ Active",
        "routes": {
            "candidates": "POST, GET /candidates",
            "resume": "POST, GET, PUT, DELETE /candidates/{id}/resume",
            "notice_period": "POST, GET /candidates/{id}/notice-period",
            "payslips": "POST, GET, DELETE /candidates/{id}/payslips",
            "background_verification": "POST, GET /candidates/{id}/background-verification",
            "address_proof": "POST, GET /candidates/{id}/background-verification/address-proof",
            "provident_fund": "POST, GET /candidates/{id}/background-verification/provident-fund",
            "availability": "POST, GET, DELETE /candidates/{id}/availability",
            "bookings": "POST, GET /candidates/{id}/bookings",
            "dashboard": "GET /candidates/{id}/dashboard",
            "apply_ai": "POST /candidates/{id}/apply-ai",
            "application_status": "PUT /candidates/{id}/applications/{app_id}/status"
        }
    }



# ============================================================================
# 👤 CANDIDATE ROUTES
# ============================================================================

@app.post("/candidates", response_model=CandidateResponse, status_code=status.HTTP_201_CREATED)
async def create_candidate(candidate: CandidateCreate, db: Session = Depends(get_db)):
    try:
        existing = db.query(Candidate).filter(Candidate.candidate_id == candidate.candidate_id).first()
        if existing:
            raise HTTPException(status_code=400, detail=f"Candidate {candidate.candidate_id} already exists")

        db_candidate = Candidate(**candidate.model_dump())
        db.add(db_candidate)
        db.commit()
        db.refresh(db_candidate)

        logger.info(f"✅ Created candidate: {candidate.candidate_id}")
        return db_candidate
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to create candidate: {str(e)}")

@app.get("/candidates", response_model=List[CandidateResponse])
async def get_all_candidates(db: Session = Depends(get_db)):
    """👥 Get all candidates"""
    try:
        candidates = db.query(Candidate).all()
        return candidates
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get candidates: {str(e)}")

@app.get("/candidates/{candidate_id}", response_model=CandidateResponse)
async def get_candidate(candidate_id: str, db: Session = Depends(get_db)):
    """👤 Get a specific candidate"""
    try:
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")
        return candidate
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get candidate: {str(e)}")



# ============================================================================
# 📄 RESUME ROUTES
# ============================================================================

@app.post("/candidates/{candidate_id}/resume", response_model=ResumeResponse, status_code=status.HTTP_201_CREATED)
async def upload_resume(candidate_id: str, file: UploadFile = File(...), db: Session = Depends(get_db)):
    """📄 Upload resume for a candidate (replaces existing if any)"""
    file_path = None
    try:
        # Check if candidate exists
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        # Validate file type and size
        allowed_types = ["application/pdf", "application/msword",
                        "application/vnd.openxmlformats-officedocument.wordprocessingml.document"]
        if file.content_type not in allowed_types:
            raise HTTPException(status_code=400, detail="Only PDF, DOC, and DOCX files are allowed")

        if file.size > 5 * 1024 * 1024:
            raise HTTPException(status_code=400, detail="File size must be less than 5MB")

        # Handle existing resume
        existing_resume = db.query(Resume).filter(Resume.candidate_id == candidate_id).first()
        if existing_resume:
            # Remove old file from filesystem
            if os.path.exists(existing_resume.file_path):
                os.remove(existing_resume.file_path)
            # Delete old resume record
            db.delete(existing_resume)
            db.flush()  # Ensure deletion is committed

        # Save new file to filesystem
        file_path, unique_filename = save_uploaded_file(file, candidate_id)

        # Create new resume record in database
        resume = Resume(
            candidate_id=candidate_id,
            filename=unique_filename,
            original_filename=file.filename,
            file_path=file_path,
            file_size=file.size,
            content_type=file.content_type,
            is_visible=True
        )

        db.add(resume)
        db.commit()
        db.refresh(resume)

        logger.info(f"📤 Successfully uploaded resume for {candidate_id}: {unique_filename}")
        return resume

    except HTTPException:
        # Clean up file if database operation failed
        if file_path and os.path.exists(file_path):
            os.remove(file_path)
        raise
    except Exception as e:
        # Clean up file if database operation failed
        if file_path and os.path.exists(file_path):
            os.remove(file_path)
        db.rollback()
        logger.error(f"❌ Failed to upload resume for {candidate_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to upload resume: {str(e)}")


@app.get("/candidates/{candidate_id}/resume/view")
async def view_resume(candidate_id: str, db: Session = Depends(get_db)):
    try:
        resume = db.query(Resume).filter(Resume.candidate_id == candidate_id).first()
        if not resume:
            raise HTTPException(status_code=404, detail=f"No resume found for candidate {candidate_id}")

        if not resume.is_visible:
            raise HTTPException(status_code=403, detail="Resume is currently hidden")

        if not os.path.exists(resume.file_path):
            raise HTTPException(status_code=404, detail="Resume file not found on server")

        return FileResponse(
            path=resume.file_path,
            filename=resume.original_filename,
            media_type=resume.content_type,
            headers={"Content-Disposition": f"inline; filename={resume.original_filename}"}
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to view resume: {str(e)}")


@app.get("/candidates/{candidate_id}/resume/download")
async def download_resume(candidate_id: str, db: Session = Depends(get_db)):
    try:
        resume = db.query(Resume).filter(Resume.candidate_id == candidate_id).first()
        if not resume:
            raise HTTPException(status_code=404, detail=f"No resume found for candidate {candidate_id}")

        if not os.path.exists(resume.file_path):
            raise HTTPException(status_code=404, detail="Resume file not found on server")

        return FileResponse(
            path=resume.file_path,
            filename=resume.original_filename,
            media_type=resume.content_type,
            headers={"Content-Disposition": f"attachment; filename={resume.original_filename}"}
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to download resume: {str(e)}")

@app.put("/candidates/{candidate_id}/resume/toggle-visibility")
async def toggle_resume_visibility(candidate_id: str, db: Session = Depends(get_db)):
    """👁️ Toggle resume visibility (show/hide)"""
    try:
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        resume = db.query(Resume).filter(Resume.candidate_id == candidate_id).first()
        if not resume:
            raise HTTPException(status_code=404, detail=f"Resume not found for candidate {candidate_id}")

        # Toggle visibility
        resume.is_visible = not resume.is_visible
        db.commit()

        logger.info(f"✅ Toggled resume visibility for {candidate_id}: {resume.is_visible}")
        return {
            "message": f"Resume visibility {'enabled' if resume.is_visible else 'disabled'}",
            "candidate_id": candidate_id,
            "is_visible": resume.is_visible
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"❌ Error toggling resume visibility for {candidate_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to toggle resume visibility: {str(e)}")

# ============================================================================
# 📋 NOTICE PERIOD ROUTES
# ============================================================================

@app.post("/candidates/{candidate_id}/notice-period", response_model=NoticePeriodResponse, status_code=status.HTTP_201_CREATED)
async def create_or_update_notice_period(
    candidate_id: str,
    notice_data: NoticePeriodCreate,
    db: Session = Depends(get_db)
):
    """📋 Create or update candidate notice period information"""
    try:
        # Check if candidate exists
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        # Check if notice period already exists
        existing_notice = db.query(NoticePeriod).filter(NoticePeriod.candidate_id == candidate_id).first()

        if existing_notice:
            # Update existing notice period
            for field, value in notice_data.model_dump(exclude_unset=True).items():
                setattr(existing_notice, field, value)
            existing_notice.updated_at = datetime.now(timezone.utc)

            db.commit()
            db.refresh(existing_notice)

            logger.info(f"📋 Updated notice period for {candidate_id}")
            return existing_notice
        else:
            # Create new notice period
            notice_period = NoticePeriod(
                candidate_id=candidate_id,
                **notice_data.model_dump()
            )

            db.add(notice_period)
            db.commit()
            db.refresh(notice_period)

            logger.info(f"📋 Created notice period for {candidate_id}")
            return notice_period

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to create/update notice period: {str(e)}")

@app.get("/candidates/{candidate_id}/notice-period", response_model=NoticePeriodResponse)
async def get_notice_period(candidate_id: str, db: Session = Depends(get_db)):
    """📋 Get candidate notice period information"""
    try:
        notice_period = db.query(NoticePeriod).filter(NoticePeriod.candidate_id == candidate_id).first()
        if not notice_period:
            raise HTTPException(status_code=404, detail=f"No notice period found for candidate {candidate_id}")

        return notice_period
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get notice period: {str(e)}")

# ============================================================================
# 💰 PAYSLIP ROUTES
# ============================================================================

@app.post("/candidates/{candidate_id}/payslips", response_model=PayslipResponse, status_code=status.HTTP_201_CREATED)
async def upload_payslip(
    candidate_id: str,
    month_year: str,  # MM/YYYY format
    file: UploadFile = File(...),
    salary_amount: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """💰 Upload payslip for a specific month/year"""
    file_path = None
    try:
        # Check if candidate exists
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        # Skip month_year validation to allow multiple payslips with timestamps

        # Validate file
        validate_payslip_file(file)

        # Validate payslip count (max 6 files)
        validate_payslip_count(db, candidate_id, month_year)

        # Allow multiple payslips - no need to check for existing month/year
        # Each payslip upload creates a new record

        # Save new file to filesystem
        file_path, unique_filename = save_payslip_file(file, candidate_id, month_year)

        # Create new payslip record in database
        payslip = Payslip(
            candidate_id=candidate_id,
            filename=unique_filename,
            original_filename=file.filename,
            file_path=file_path,
            file_size=file.size,
            content_type=file.content_type,
            month_year=month_year,
            salary_amount=salary_amount
        )

        db.add(payslip)
        db.commit()
        db.refresh(payslip)

        logger.info(f"💰 Successfully uploaded payslip for {candidate_id} - {month_year}: {unique_filename}")
        return payslip

    except HTTPException:
        # Clean up file if database operation failed
        if file_path and os.path.exists(file_path):
            os.remove(file_path)
        raise
    except Exception as e:
        # Clean up file if database operation failed
        if file_path and os.path.exists(file_path):
            os.remove(file_path)
        db.rollback()
        logger.error(f"❌ Failed to upload payslip for {candidate_id} - {month_year}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to upload payslip: {str(e)}")

@app.get("/candidates/{candidate_id}/payslips", response_model=List[PayslipResponse])
async def get_payslips(candidate_id: str, db: Session = Depends(get_db)):
    """💰 Get all payslips for a candidate"""
    try:
        payslips = db.query(Payslip).filter(
            Payslip.candidate_id == candidate_id
        ).order_by(Payslip.month_year.desc()).all()

        return payslips
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get payslips: {str(e)}")

@app.get("/candidates/{candidate_id}/payslips/summary")
async def get_payslips_summary(candidate_id: str, db: Session = Depends(get_db)):
    """💰 Get payslips summary for UI display"""
    try:
        payslips = db.query(Payslip).filter(
            Payslip.candidate_id == candidate_id
        ).order_by(Payslip.month_year.desc()).all()

        return {
            "total_payslips": len(payslips),
            "max_allowed": 6,
            "remaining_slots": max(0, 6 - len(payslips)),
            "payslips": [
                {
                    "id": p.id,
                    "month_year": p.month_year,
                    "filename": p.original_filename,
                    "file_size_mb": round(p.file_size / (1024 * 1024), 2),
                    "uploaded_at": p.created_at.strftime("%d/%m/%Y")
                } for p in payslips
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get payslips summary: {str(e)}")

@app.get("/candidates/{candidate_id}/payslips/{payslip_id}/download")
async def download_payslip(candidate_id: str, payslip_id: int, db: Session = Depends(get_db)):
    """💰 Download a specific payslip"""
    try:
        payslip = db.query(Payslip).filter(
            Payslip.id == payslip_id,
            Payslip.candidate_id == candidate_id
        ).first()

        if not payslip:
            raise HTTPException(status_code=404, detail="Payslip not found")

        if not os.path.exists(payslip.file_path):
            raise HTTPException(status_code=404, detail="Payslip file not found on server")

        return FileResponse(
            path=payslip.file_path,
            filename=payslip.original_filename,
            media_type=payslip.content_type,
            headers={"Content-Disposition": f"attachment; filename={payslip.original_filename}"}
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to download payslip: {str(e)}")

@app.delete("/candidates/{candidate_id}/payslips/{payslip_id}")
async def delete_payslip(candidate_id: str, payslip_id: int, db: Session = Depends(get_db)):
    """💰 Delete a specific payslip"""
    try:
        payslip = db.query(Payslip).filter(
            Payslip.id == payslip_id,
            Payslip.candidate_id == candidate_id
        ).first()

        if not payslip:
            raise HTTPException(status_code=404, detail="Payslip not found")

        # Remove file from filesystem
        if os.path.exists(payslip.file_path):
            os.remove(payslip.file_path)

        # Remove from database
        db.delete(payslip)
        db.commit()

        logger.info(f"💰 Deleted payslip {payslip_id} for {candidate_id}")
        return {"message": f"Payslip for {payslip.month_year} deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to delete payslip: {str(e)}")

# ============================================================================
# 🛡️ BACKGROUND VERIFICATION ROUTES
# ============================================================================

@app.post("/candidates/{candidate_id}/background-verification", response_model=BackgroundVerificationResponse, status_code=status.HTTP_201_CREATED)
async def create_background_verification(candidate_id: str, verification_data: BackgroundVerificationCreate, db: Session = Depends(get_db)):
    """🛡️ Create or update background verification status"""
    try:
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        # Check if background verification already exists
        existing_verification = db.query(BackgroundVerification).filter(
            BackgroundVerification.candidate_id == candidate_id
        ).first()

        if existing_verification:
            # Update existing verification
            for key, value in verification_data.model_dump().items():
                setattr(existing_verification, key, value)
            existing_verification.updated_at = datetime.now(timezone.utc)
            db.commit()
            db.refresh(existing_verification)
            logger.info(f"🛡️ Updated background verification for {candidate_id}")
            return existing_verification
        else:
            # Create new verification
            verification = BackgroundVerification(
                candidate_id=candidate_id,
                **verification_data.model_dump()
            )
            db.add(verification)
            db.commit()
            db.refresh(verification)
            logger.info(f"🛡️ Created background verification for {candidate_id}")
            return verification

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to create background verification: {str(e)}")

@app.get("/candidates/{candidate_id}/background-verification")
async def get_background_verification(candidate_id: str, db: Session = Depends(get_db)):
    """🛡️ Get background verification status with detailed address proof information"""
    try:
        # Get candidate information
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        verification = db.query(BackgroundVerification).filter(
            BackgroundVerification.candidate_id == candidate_id
        ).first()

        if not verification:
            raise HTTPException(status_code=404, detail=f"No background verification found for candidate {candidate_id}")

        # Get address proofs with enhanced information
        address_proofs = db.query(AddressProof).filter(
            AddressProof.candidate_id == candidate_id
        ).all()

        # Enhanced response with candidate details and address proof analysis
        result = {
            "id": verification.id,
            "candidate_id": verification.candidate_id,
            "candidate_name": f"{candidate.first_name} {candidate.last_name}",
            "candidate_email": candidate.email,
            "status": verification.status,
            "verification_notes": verification.verification_notes,
            "employment_verified": verification.employment_verified,
            "education_verified": verification.education_verified,
            "address_verified": verification.address_verified,
            "created_at": verification.created_at,
            "updated_at": verification.updated_at,
            "address_proofs": [
                {
                    "id": proof.id,
                    "candidate_id": proof.candidate_id,  # Now available for easier analysis
                    "document_type": proof.document_type,
                    "filename": proof.original_filename,
                    "file_size_mb": round(proof.file_size / (1024 * 1024), 2),
                    "is_verified": proof.is_verified,
                    "uploaded_date": proof.created_at.strftime("%d/%m/%Y %H:%M"),
                    "file_exists": os.path.exists(proof.file_path)
                } for proof in address_proofs
            ],
            "address_proof_summary": {
                "total_documents": len(address_proofs),
                "verified_documents": sum(1 for p in address_proofs if p.is_verified),
                "document_types": list(set(p.document_type for p in address_proofs))
            }
        }

        return result

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get background verification: {str(e)}")

@app.post("/candidates/{candidate_id}/background-verification/address-proof")
async def upload_address_proof(
    candidate_id: str,
    document_type: str,
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """🛡️ Upload address proof document"""
    file_path = None
    try:
        # Check if candidate exists
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        # Validate document type and file
        validate_document_type(document_type)
        validate_address_proof_file(file)

        # Get or create background verification
        verification = db.query(BackgroundVerification).filter(
            BackgroundVerification.candidate_id == candidate_id
        ).first()

        if not verification:
            verification = BackgroundVerification(candidate_id=candidate_id)
            db.add(verification)
            db.flush()  # Get the ID for foreign key

        # Handle existing document of same type
        existing_proof = db.query(AddressProof).filter(
            AddressProof.background_verification_id == verification.id,
            AddressProof.document_type == document_type
        ).first()

        if existing_proof:
            # Remove old file from filesystem
            if os.path.exists(existing_proof.file_path):
                os.remove(existing_proof.file_path)
            # Delete old address proof record
            db.delete(existing_proof)
            db.flush()  # Ensure deletion is committed

        # Save new file to filesystem
        file_path, unique_filename = save_address_proof_file(file, candidate_id, document_type)

        # Create new address proof record in database
        address_proof = AddressProof(
            candidate_id=candidate_id,  # Direct candidate reference for easier analysis
            background_verification_id=verification.id,
            document_type=document_type,
            filename=unique_filename,
            original_filename=file.filename,
            file_path=file_path,
            file_size=file.size,
            content_type=file.content_type,
            is_verified=False
        )

        db.add(address_proof)
        db.commit()
        db.refresh(address_proof)

        # Sync to Supabase immediately after upload
        try:
            await sync_uploaded_document_to_supabase_simple(candidate_id, address_proof)
        except Exception as sync_error:
            logger.error(f"⚠️ Supabase sync failed but upload succeeded: {sync_error}")

        logger.info(f"🛡️ Successfully uploaded {document_type} for {candidate_id}: {unique_filename}")
        return {
            "message": f"{document_type} uploaded successfully",
            "document_id": address_proof.id,
            "document_type": document_type,
            "filename": address_proof.original_filename,
            "uploaded_at": address_proof.created_at.isoformat(),
            "file_size": address_proof.file_size
        }

    except HTTPException:
        # Clean up file if database operation failed
        if file_path and os.path.exists(file_path):
            os.remove(file_path)
        raise
    except Exception as e:
        # Clean up file if database operation failed
        if file_path and os.path.exists(file_path):
            os.remove(file_path)
        db.rollback()
        logger.error(f"❌ Failed to upload {document_type} for {candidate_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to upload address proof: {str(e)}")

@app.get("/candidates/{candidate_id}/background-verification/address-proofs", response_model=List[AddressProofResponse])
async def get_address_proofs(candidate_id: str, db: Session = Depends(get_db)):
    """🛡️ Get all address proofs for a candidate"""
    try:
        verification = db.query(BackgroundVerification).filter(
            BackgroundVerification.candidate_id == candidate_id
        ).first()

        if not verification:
            return []

        address_proofs = db.query(AddressProof).filter(
            AddressProof.background_verification_id == verification.id
        ).all()

        return address_proofs

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get address proofs: {str(e)}")

@app.post("/candidates/{candidate_id}/background-verification/provident-fund", response_model=ProvidentFundResponse, status_code=status.HTTP_201_CREATED)
async def create_provident_fund(candidate_id: str, pf_data: ProvidentFundCreate, db: Session = Depends(get_db)):
    """🛡️ Create or update provident fund details"""
    try:
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        # Get or create background verification
        verification = db.query(BackgroundVerification).filter(
            BackgroundVerification.candidate_id == candidate_id
        ).first()

        if not verification:
            verification = BackgroundVerification(candidate_id=candidate_id)
            db.add(verification)
            db.flush()

        # Check if provident fund already exists
        existing_pf = db.query(ProvidentFund).filter(
            ProvidentFund.background_verification_id == verification.id
        ).first()

        if existing_pf:
            # Update existing PF details
            for key, value in pf_data.model_dump().items():
                if value is not None:  # Only update non-None values
                    setattr(existing_pf, key, value)
            existing_pf.updated_at = datetime.now(timezone.utc)
            db.commit()
            db.refresh(existing_pf)
            logger.info(f"🛡️ Updated provident fund details for {candidate_id}")
            return existing_pf
        else:
            # Create new PF record
            pf_record = ProvidentFund(
                background_verification_id=verification.id,
                **pf_data.model_dump()
            )
            db.add(pf_record)
            db.commit()
            db.refresh(pf_record)
            logger.info(f"🛡️ Created provident fund details for {candidate_id}")
            return pf_record

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to create provident fund details: {str(e)}")

@app.get("/candidates/{candidate_id}/background-verification/provident-fund", response_model=ProvidentFundResponse)
async def get_provident_fund(candidate_id: str, db: Session = Depends(get_db)):
    """🛡️ Get provident fund details"""
    try:
        verification = db.query(BackgroundVerification).filter(
            BackgroundVerification.candidate_id == candidate_id
        ).first()

        if not verification:
            raise HTTPException(status_code=404, detail=f"No background verification found for candidate {candidate_id}")

        pf_record = db.query(ProvidentFund).filter(
            ProvidentFund.background_verification_id == verification.id
        ).first()

        if not pf_record:
            raise HTTPException(status_code=404, detail=f"No provident fund details found for candidate {candidate_id}")

        return pf_record

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get provident fund details: {str(e)}")

# ============================================================================
#  INTERVIEW FEEDBACK ANALYSIS
# ============================================================================



def extract_technical_keywords(question: str) -> list:
    """Extract expected technical keywords from question"""
    question_lower = question.lower()

    keyword_map = {
        'react': ['virtual dom', 'components', 'jsx', 'state', 'props', 'hooks'],
        'javascript': ['function', 'variable', 'object', 'array', 'async', 'promise'],
        'python': ['function', 'class', 'module', 'list', 'dictionary', 'loop'],
        'database': ['sql', 'query', 'table', 'index', 'join', 'normalization'],
        'api': ['rest', 'http', 'endpoint', 'request', 'response', 'json']
    }

    keywords = []
    for tech, tech_keywords in keyword_map.items():
        if tech in question_lower:
            keywords.extend(tech_keywords)

    return keywords

def analyze_concept_coverage(answer: str, expected_keywords: list) -> int:
    """Analyze how well the answer covers expected concepts"""
    if not expected_keywords:
        return 75

    answer_lower = answer.lower()
    covered_keywords = sum(1 for keyword in expected_keywords if keyword in answer_lower)
    coverage_percentage = (covered_keywords / len(expected_keywords)) * 100

    return min(100, max(20, int(coverage_percentage)))

def evaluate_answer_clarity(answer: str) -> int:
    """Evaluate answer clarity and structure"""
    clarity_score = 50

    if any(word in answer.lower() for word in ['first', 'second', 'then', 'next', 'finally']):
        clarity_score += 20

    if any(word in answer.lower() for word in ['example', 'for instance', 'such as', 'like']):
        clarity_score += 15

    if any(word in answer.lower() for word in ['because', 'since', 'due to', 'reason']):
        clarity_score += 15

    return min(100, clarity_score)

def evaluate_answer_depth(answer: str) -> int:
    """Evaluate depth and practical knowledge in answer"""
    depth_score = 40

    if any(word in answer.lower() for word in ['project', 'experience', 'used', 'implemented']):
        depth_score += 25

    if any(word in answer.lower() for word in ['performance', 'optimization', 'best practice', 'pattern']):
        depth_score += 20



# ============================================================================
#  INTERVIEW FEEDBACK ENDPOINT (Simple Database Functions)
# ============================================================================

@app.get("/candidates/{candidate_id}/interview-feedback", response_model=InterviewFeedbackResponse)
async def get_interview_feedback(candidate_id: str, db: Session = Depends(get_db)):
    """Get interview feedback for a candidate from database"""
    try:
        feedback = db.query(InterviewFeedback).filter(InterviewFeedback.candidate_id == candidate_id).first()
        if not feedback:
            raise HTTPException(status_code=404, detail=f"No interview feedback found for candidate {candidate_id}")

        # Convert questions to simple dict format
        questions = []
        for question in feedback.questions:
            questions.append({
                "question": question.question,
                "answer": question.answer,
                "score": question.score,
                "feedback": question.question_feedback
            })

        return InterviewFeedbackResponse(
            candidate_id=candidate_id,
            overall_score=feedback.overall_score,
            max_score=feedback.max_score,
            general_comments=feedback.general_comments,
            questions=questions,
            created_at=feedback.created_at,
            updated_at=feedback.updated_at
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get interview feedback: {str(e)}")

# ============================================================================
#  INTERVIEW AVAILABILITY SYSTEM
# ============================================================================

@app.get("/candidates/{candidate_id}/availability")
async def get_all_availability_slots(candidate_id: str, db: Session = Depends(get_db)):
    """📅 Get all availability slots (general + job-specific) for a candidate"""
    try:
        # Check if candidate exists
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        # Get general availability slots
        general_slots = db.query(AvailabilitySlot).filter(
            AvailabilitySlot.candidate_id == candidate_id
        ).order_by(AvailabilitySlot.interview_date, AvailabilitySlot.start_time).all()

        # Get shortlisted job availability slots
        job_slots = db.query(ShortlistedJobAvailability).filter(
            ShortlistedJobAvailability.candidate_id == candidate_id
        ).order_by(ShortlistedJobAvailability.interview_date, ShortlistedJobAvailability.start_time).all()

        availability_data = []

        # Add general slots
        for slot in general_slots:
            availability_data.append({
                "id": slot.id,
                "candidate_id": slot.candidate_id,
                "slot_type": "general",
                "interview_date": slot.interview_date.strftime("%d/%m/%Y"),
                "start_time": slot.start_time.strftime("%I:%M %p"),
                "end_time": slot.end_time.strftime("%I:%M %p"),
                "company_name": None,
                "job_position": None,
                "created_at": slot.created_at.isoformat() if slot.created_at else None
            })

        # Add shortlisted job slots
        for slot in job_slots:
            availability_data.append({
                "id": slot.id,
                "candidate_id": slot.candidate_id,
                "slot_type": "job_specific",
                "interview_date": slot.interview_date.strftime("%d/%m/%Y"),
                "start_time": slot.start_time.strftime("%I:%M %p"),
                "end_time": slot.end_time.strftime("%I:%M %p"),
                "company_name": slot.company_name,
                "job_position": slot.job_position,
                "created_at": slot.created_at.isoformat() if slot.created_at else None
            })

        logger.info(f"📅 Retrieved {len(availability_data)} availability slots for {candidate_id}")
        return availability_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Error fetching availability slots for {candidate_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch availability slots: {str(e)}")

@app.post("/candidates/{candidate_id}/availability/general", status_code=status.HTTP_201_CREATED)
async def add_general_availability(
    candidate_id: str,
    availability_input: AvailabilitySlotCreate,
    db: Session = Depends(get_db)
):
    """📅 Add general availability slots with real calendar dates"""
    try:
        # Check if candidate exists
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        logger.info(f"📅 Adding {len(availability_input.slots)} general availability slots for {candidate_id}")

        created_slots = []
        for slot_input in availability_input.slots:
            # Parse and validate date/time
            interview_date = parse_date(slot_input.interview_date)
            start_time = parse_time(slot_input.start_time)
            end_time = parse_time(slot_input.end_time)

            # Validate business rules
            if not validate_weekday(interview_date):
                raise HTTPException(
                    status_code=400,
                    detail=f"Date {slot_input.interview_date} is not a weekday. Only Monday-Friday allowed."
                )

            if not validate_business_hours(start_time, end_time):
                raise HTTPException(
                    status_code=400,
                    detail=f"Time slot {slot_input.start_time}-{slot_input.end_time} is outside business hours (9 AM - 5 PM)."
                )

            if not validate_slot_duration(start_time, end_time):
                raise HTTPException(
                    status_code=400,
                    detail=f"Time slot must be exactly 2 hours. Current duration: {slot_input.start_time}-{slot_input.end_time}"
                )

            # Check for duplicate slots for this candidate (same date and time)
            existing_slot = db.query(AvailabilitySlot).filter(
                AvailabilitySlot.candidate_id == candidate_id,
                AvailabilitySlot.interview_date == interview_date,
                AvailabilitySlot.start_time == start_time,
                AvailabilitySlot.end_time == end_time
            ).first()

            if existing_slot:
                logger.warning(f"⚠️ Slot already exists for {candidate_id} on {slot_input.interview_date} {slot_input.start_time}-{slot_input.end_time}")
                continue

            # Create availability slot
            availability_slot = AvailabilitySlot(
                candidate_id=candidate_id,
                slot_type="general",
                interview_date=interview_date,
                start_time=start_time,
                end_time=end_time,
                is_booked=False
            )

            db.add(availability_slot)
            created_slots.append(availability_slot)

        db.commit()

        logger.info(f"✅ Created {len(created_slots)} availability slots for {candidate_id}")
        return {
            "message": f"Added {len(created_slots)} availability slots",
            "candidate_id": candidate_id,
            "slots_created": len(created_slots)
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"❌ Error adding availability for {candidate_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to add availability: {str(e)}")

@app.post("/candidates/{candidate_id}/availability/job-specific", status_code=status.HTTP_201_CREATED)
async def add_shortlisted_job_availability(
    candidate_id: str,
    availability_input: AvailabilitySlotCreate,
    db: Session = Depends(get_db)
):
    """📅 Add shortlisted job availability slots with real calendar dates"""
    try:
        # Check if candidate exists
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        # Validate job-specific requirements
        if not availability_input.company_name or not availability_input.job_position:
            raise HTTPException(
                status_code=400,
                detail="Company name and job position are required for shortlisted job availability"
            )

        logger.info(f"📅 Adding {len(availability_input.slots)} shortlisted job slots for {candidate_id} - {availability_input.company_name}")

        created_slots = []
        for slot_input in availability_input.slots:
            # Parse and validate date/time
            interview_date = parse_date(slot_input.interview_date)
            start_time = parse_time(slot_input.start_time)
            end_time = parse_time(slot_input.end_time)

            # Validate business rules
            if not validate_weekday(interview_date):
                raise HTTPException(
                    status_code=400,
                    detail=f"Date {slot_input.interview_date} is not a weekday. Only Monday-Friday allowed."
                )

            if not validate_business_hours(start_time, end_time):
                raise HTTPException(
                    status_code=400,
                    detail=f"Time slot {slot_input.start_time}-{slot_input.end_time} is outside business hours (9 AM - 5 PM)."
                )

            if not validate_slot_duration(start_time, end_time):
                raise HTTPException(
                    status_code=400,
                    detail=f"Time slot must be exactly 2 hours. Current duration: {slot_input.start_time}-{slot_input.end_time}"
                )

            # Check for duplicate slots (same date, time, company, and position)
            existing_job_slot = db.query(ShortlistedJobAvailability).filter(
                ShortlistedJobAvailability.candidate_id == candidate_id,
                ShortlistedJobAvailability.company_name == availability_input.company_name,
                ShortlistedJobAvailability.job_position == availability_input.job_position,
                ShortlistedJobAvailability.interview_date == interview_date,
                ShortlistedJobAvailability.start_time == start_time,
                ShortlistedJobAvailability.end_time == end_time
            ).first()

            if existing_job_slot:
                logger.warning(f"⚠️ Shortlisted job slot already exists for {candidate_id} - {availability_input.company_name} on {slot_input.interview_date} {slot_input.start_time}-{slot_input.end_time}")
                continue

            # Create shortlisted job availability slot
            availability_slot = ShortlistedJobAvailability(
                candidate_id=candidate_id,
                interview_date=interview_date,
                start_time=start_time,
                end_time=end_time,
                is_booked=False,
                company_name=availability_input.company_name,
                job_position=availability_input.job_position
            )

            db.add(availability_slot)
            created_slots.append(availability_slot)

        db.commit()

        logger.info(f"✅ Created {len(created_slots)} shortlisted job slots for {candidate_id}")
        return {
            "message": f"Added {len(created_slots)} shortlisted job availability slots",
            "candidate_id": candidate_id,
            "company_name": availability_input.company_name,
            "job_position": availability_input.job_position,
            "slots_created": len(created_slots)
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"❌ Error adding shortlisted job availability for {candidate_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to add shortlisted job availability: {str(e)}")

@app.get("/candidates/{candidate_id}/availability/general", response_model=List[AvailabilitySlotResponse])
async def get_general_availability(candidate_id: str, db: Session = Depends(get_db)):
    """📅 Get all general availability slots for a candidate"""
    try:
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        slots = db.query(AvailabilitySlot).filter(
            AvailabilitySlot.candidate_id == candidate_id,
            AvailabilitySlot.slot_type == "general"
        ).order_by(AvailabilitySlot.interview_date, AvailabilitySlot.start_time).all()

        # Convert to response format
        response_slots = []
        for slot in slots:
            response_slots.append(AvailabilitySlotResponse(
                id=slot.id,
                candidate_id=slot.candidate_id,
                slot_type=slot.slot_type,
                interview_date=format_date_for_response(slot.interview_date),
                start_time=format_time_for_response(slot.start_time),
                end_time=format_time_for_response(slot.end_time),
                is_booked=slot.is_booked,
                company_name=slot.company_name,
                job_position=slot.job_position,
                created_at=slot.created_at
            ))

        return response_slots

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get availability: {str(e)}")

@app.delete("/candidates/{candidate_id}/availability/{slot_id}", status_code=status.HTTP_200_OK)
async def delete_availability_slot(candidate_id: str, slot_id: int, db: Session = Depends(get_db)):
    """🗑️ Delete an availability slot"""
    try:
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        # Check if slot exists in general availability
        general_slot = db.query(AvailabilitySlot).filter(
            AvailabilitySlot.id == slot_id,
            AvailabilitySlot.candidate_id == candidate_id
        ).first()

        # Check if slot exists in shortlisted job availability
        job_slot = db.query(ShortlistedJobAvailability).filter(
            ShortlistedJobAvailability.id == slot_id,
            ShortlistedJobAvailability.candidate_id == candidate_id
        ).first()

        if not general_slot and not job_slot:
            raise HTTPException(status_code=404, detail=f"Availability slot {slot_id} not found for candidate {candidate_id}")

        # Delete the appropriate slot
        if general_slot:
            # Check if slot is booked
            if general_slot.is_booked:
                # Also delete the booking
                booking = db.query(InterviewBooking).filter(InterviewBooking.slot_id == slot_id).first()
                if booking:
                    db.delete(booking)
                    logger.info(f"🗑️ Deleted booking for general slot {slot_id}")

            db.delete(general_slot)
            slot_type = "general"
        else:
            # Delete shortlisted job slot
            if job_slot.is_booked:
                logger.info(f"🗑️ Shortlisted job slot {slot_id} was booked")

            db.delete(job_slot)
            slot_type = "shortlisted job"

        db.commit()

        logger.info(f"✅ Deleted {slot_type} availability slot {slot_id} for {candidate_id}")
        return {
            "message": f"Deleted {slot_type} availability slot {slot_id}",
            "candidate_id": candidate_id,
            "slot_id": slot_id,
            "slot_type": slot_type
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"❌ Error deleting slot {slot_id} for {candidate_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete availability slot: {str(e)}")

@app.post("/candidates/{candidate_id}/availability/book-slot", response_model=InterviewBookingResponse, status_code=status.HTTP_201_CREATED)
async def book_interview_slot(
    candidate_id: str,
    slot_id: int,
    booking_input: InterviewBookingCreate,
    db: Session = Depends(get_db)
):
    """📅 Book an interview slot with company conflict detection"""
    try:
        # Check if candidate exists
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        # Check if slot exists and is available
        slot = db.query(AvailabilitySlot).filter(
            AvailabilitySlot.id == slot_id,
            AvailabilitySlot.candidate_id == candidate_id
        ).first()

        if not slot:
            raise HTTPException(status_code=404, detail=f"Availability slot {slot_id} not found for candidate {candidate_id}")

        if slot.is_booked:
            raise HTTPException(status_code=400, detail=f"Slot {slot_id} is already booked")

        # Parse the booking date
        booking_date = parse_date(booking_input.interview_date)

        # Validate that booking date matches slot date
        if booking_date != slot.interview_date:
            raise HTTPException(
                status_code=400,
                detail=f"Booking date {booking_input.interview_date} does not match slot date {format_date_for_response(slot.interview_date)}"
            )

        # 🚨 CRITICAL: Check for company time conflicts
        has_conflict = check_company_time_conflict(
            db=db,
            company_name=booking_input.company_name,
            interview_date=booking_date,
            start_time=slot.start_time,
            end_time=slot.end_time
        )

        if has_conflict:
            raise HTTPException(
                status_code=409,  # Conflict status code
                detail=f"❌ CONFLICT: Company '{booking_input.company_name}' already has an interview scheduled on {booking_input.interview_date} from {format_time_for_response(slot.start_time)} to {format_time_for_response(slot.end_time)}. Companies cannot conduct multiple interviews at the same time."
            )

        # Create the booking
        booking = InterviewBooking(
            slot_id=slot_id,
            candidate_id=candidate_id,
            company_name=booking_input.company_name,
            job_position=booking_input.job_position,
            interview_date=booking_date,
            start_time=slot.start_time,
            end_time=slot.end_time,
            booking_status="confirmed"
        )

        # Mark slot as booked
        slot.is_booked = True

        db.add(booking)
        db.commit()
        db.refresh(booking)

        logger.info(f"✅ Booked interview: {booking_input.company_name} - {candidate_id} on {booking_input.interview_date} {format_time_for_response(slot.start_time)}-{format_time_for_response(slot.end_time)}")

        return InterviewBookingResponse(
            id=booking.id,
            slot_id=booking.slot_id,
            candidate_id=booking.candidate_id,
            company_name=booking.company_name,
            job_position=booking.job_position,
            interview_date=format_date_for_response(booking.interview_date),
            start_time=format_time_for_response(booking.start_time),
            end_time=format_time_for_response(booking.end_time),
            booking_status=booking.booking_status,
            created_at=booking.created_at,
            updated_at=booking.updated_at
        )

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"❌ Error booking slot for {candidate_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to book interview slot: {str(e)}")

@app.get("/candidates/{candidate_id}/bookings", response_model=List[InterviewBookingResponse])
async def get_candidate_bookings(candidate_id: str, db: Session = Depends(get_db)):
    """📅 Get all interview bookings for a candidate"""
    try:
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        bookings = db.query(InterviewBooking).filter(
            InterviewBooking.candidate_id == candidate_id
        ).order_by(InterviewBooking.interview_date, InterviewBooking.start_time).all()

        # Convert to response format
        response_bookings = []
        for booking in bookings:
            response_bookings.append(InterviewBookingResponse(
                id=booking.id,
                slot_id=booking.slot_id,
                candidate_id=booking.candidate_id,
                company_name=booking.company_name,
                job_position=booking.job_position,
                interview_date=format_date_for_response(booking.interview_date),
                start_time=format_time_for_response(booking.start_time),
                end_time=format_time_for_response(booking.end_time),
                booking_status=booking.booking_status,
                created_at=booking.created_at,
                updated_at=booking.updated_at
            ))

        return response_bookings

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get bookings: {str(e)}")

# ============================================================================
# 🎯 ENHANCED ROUTES FOR FRONTEND INTEGRATION
# ============================================================================

# Add job applications table to existing models
class JobApplication(Base):
    __tablename__ = "job_applications"
    id = Column(Integer, primary_key=True, index=True)
    candidate_id = Column(String, ForeignKey("candidates.candidate_id"), nullable=False)
    company = Column(String(255), nullable=False)
    position = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    required_skills = Column(JSON, nullable=True)
    applied_date = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    deadline = Column(DateTime, nullable=True)
    status = Column(String(50), default="Applied")
    match_score = Column(Integer, nullable=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    candidate = relationship("Candidate")

# Enhanced dashboard route using existing data structures
@app.get("/candidates/{candidate_id}/dashboard")
async def get_candidate_dashboard(candidate_id: str, db: Session = Depends(get_db)):
    """📊 Get complete candidate dashboard data for frontend"""
    try:
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        # Get all existing data
        resume = db.query(Resume).filter(Resume.candidate_id == candidate_id).first()
        notice_period = db.query(NoticePeriod).filter(NoticePeriod.candidate_id == candidate_id).first()
        feedback = db.query(InterviewFeedback).filter(InterviewFeedback.candidate_id == candidate_id).first()
        availability_slots = db.query(AvailabilitySlot).filter(AvailabilitySlot.candidate_id == candidate_id).all()
        applications = db.query(JobApplication).filter(JobApplication.candidate_id == candidate_id).all()
        payslips = db.query(Payslip).filter(Payslip.candidate_id == candidate_id).all()
        bg_verification = db.query(BackgroundVerification).filter(BackgroundVerification.candidate_id == candidate_id).first()

        # Format for frontend Apply & Share component
        dashboard_data = {
            "candidate": {
                "candidate_id": candidate.candidate_id,
                "name": f"{candidate.first_name or ''} {candidate.last_name or ''}".strip(),
                "email": candidate.email,
                "phone": candidate.phone
            },
            "resume": {
                "uploaded": resume is not None,
                "visible": resume.is_visible if resume else False,
                "filename": resume.original_filename if resume else None,
                "name": f"{candidate.first_name or ''} {candidate.last_name or ''}".strip(),
                "summary": "",
                "skills": [],
                "experience": [],
                "education": [],
                "certifications": []
            },
            "salary": {
                "currentCTC": f"${notice_period.current_salary:,}" if notice_period and notice_period.current_salary else "Not specified",
                "expectedCTC": f"${notice_period.expected_salary:,}" if notice_period and notice_period.expected_salary else "Not specified"
            },
            "noticePeriod": f"{notice_period.notice_period_days} days" if notice_period else "Not specified",
            "interviewFeedback": {
                "score": feedback.overall_score if feedback else 0,
                "comments": feedback.general_comments if feedback else "No feedback available",
                "mockInterview": [
                    {
                        "question": q.question,
                        "answer": q.answer,
                        "score": q.score,
                        "feedback": q.question_feedback
                    } for q in feedback.questions
                ] if feedback else []
            },
            "availabilitySlots": [
                {
                    "day": slot.interview_date.strftime("%A"),
                    "slots": [f"{slot.start_time.strftime('%I:%M %p')} - {slot.end_time.strftime('%I:%M %p')}"]
                } for slot in availability_slots
            ],
            "applications": [
                {
                    "id": app.id,
                    "company": app.company,
                    "position": app.position,
                    "appliedDate": app.applied_date.strftime("%Y-%m-%d"),
                    "deadline": app.deadline.strftime("%Y-%m-%d") if app.deadline else None,
                    "status": app.status,
                    "description": app.description or f"Position at {app.company}",
                    "companyLogo": f"https://via.placeholder.com/40?text={app.company[0]}",
                    "companyInfo": f"Company: {app.company}",
                    "matchScore": app.match_score or 0,
                    "requiredSkills": app.required_skills or [],
                    "rejected": app.status == "Rejected",
                    "interviews": [],
                    "learningGaps": []
                } for app in applications
            ],
            "payslips": [{"name": p.original_filename, "url": f"/candidates/{candidate_id}/payslips/{p.id}/download"} for p in payslips],
            "backgroundVerification": {
                "status": bg_verification.status if bg_verification else "Not Started",
                "details": bg_verification.verification_notes if bg_verification else "No verification started",
                "addressProof": None,
                "providentFund": None,
                "moonlightingDetails": None
            }
        }

        return dashboard_data

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get dashboard: {str(e)}")

# AI application route
@app.post("/candidates/{candidate_id}/apply-ai")
async def apply_with_ai(candidate_id: str, db: Session = Depends(get_db)):
    """🤖 Apply to jobs using AI (simulated)"""
    try:
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        # Create sample applications with different statuses
        sample_jobs = [
            {"company": "TechGrowth Inc", "position": "Senior Frontend Developer", "match_score": 87, "status": "Applied"},
            {"company": "DataSphere", "position": "Full Stack Engineer", "match_score": 82, "status": "Shortlisted"},
            {"company": "TechVision", "position": "React Developer", "match_score": 88, "status": "Shortlisted"}
        ]

        applied_count = 0
        for job in sample_jobs:
            existing = db.query(JobApplication).filter(
                JobApplication.candidate_id == candidate_id,
                JobApplication.company == job["company"]
            ).first()

            if not existing:
                from datetime import timedelta
                new_app = JobApplication(
                    candidate_id=candidate_id,
                    company=job["company"],
                    position=job["position"],
                    description=f"AI-matched position for {job['position']}",
                    required_skills=["React", "JavaScript", "TypeScript"],
                    deadline=datetime.now(timezone.utc) + timedelta(days=30),
                    status=job["status"],
                    match_score=job["match_score"]
                )
                db.add(new_app)
                applied_count += 1

        db.commit()
        return {"message": f"Applied to {applied_count} jobs using AI", "count": applied_count}

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to apply with AI: {str(e)}")

# Update application status
@app.put("/candidates/{candidate_id}/applications/{application_id}/status")
async def update_application_status(candidate_id: str, application_id: int, status: str, db: Session = Depends(get_db)):
    """🎯 Update job application status (Schedule Interview, etc.)"""
    try:
        valid_statuses = ["Applied", "Shortlisted", "Interview Scheduled", "Rejected"]
        if status not in valid_statuses:
            raise HTTPException(status_code=400, detail=f"Invalid status. Must be one of: {valid_statuses}")

        application = db.query(JobApplication).filter(
            JobApplication.id == application_id,
            JobApplication.candidate_id == candidate_id
        ).first()

        if not application:
            raise HTTPException(status_code=404, detail="Job application not found")

        application.status = status
        db.commit()

        return {"message": f"Application status updated to {status}"}

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to update application status: {str(e)}")



@app.get("/health")
async def health_check():
    """🏥 Health check endpoint"""
    try:
        db = SessionLocal()
        db.execute(text("SELECT 1"))
        db.close()
        return {"status": "Healthy", "database": "Connected"}
    except Exception as e:
        return {"status": "Error", "database": "Failed", "error": str(e)}

# ============================================================================
# 🔐 DIGILOCKER AUTHENTICATION ROUTES
# ============================================================================

@app.post("/candidates/{candidate_id}/digilocker/auth")
async def initiate_digilocker_auth(candidate_id: str, db: Session = Depends(get_db)):
    """🔐 Initiate DigiLocker authentication for background verification"""
    try:
        # Check if candidate exists
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        # Generate DigiLocker auth URL
        auth_url = await generate_digilocker_auth_url(candidate_id)

        # Create or update auth record with pending status
        auth_record = db.query(DigiLockerAuth).filter(
            DigiLockerAuth.candidate_id == candidate_id
        ).first()

        if auth_record:
            auth_record.auth_status = "pending"
            auth_record.updated_at = datetime.now(timezone.utc)
        else:
            auth_record = DigiLockerAuth(
                candidate_id=candidate_id,
                auth_status="pending"
            )
            db.add(auth_record)

        db.commit()

        return {
            "auth_url": auth_url,
            "candidate_id": candidate_id,
            "status": "pending",
            "message": "Please complete DigiLocker authentication"
        }

    except Exception as e:
        db.rollback()
        logger.error(f"Failed to initiate DigiLocker auth for {candidate_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to initiate DigiLocker authentication: {str(e)}")

@app.get("/auth/digilocker/callback")
async def digilocker_callback(request: Request, db: Session = Depends(get_db)):
    """🔐 Handle DigiLocker OAuth callback"""
    try:
        # Get query parameters
        code = request.query_params.get("code")
        state = request.query_params.get("state")  # This should be candidate_id
        error = request.query_params.get("error")

        if error:
            logger.error(f"DigiLocker auth error: {error}")
            # Update auth status to failed
            if state:
                auth_record = db.query(DigiLockerAuth).filter(
                    DigiLockerAuth.candidate_id == state
                ).first()
                if auth_record:
                    auth_record.auth_status = "failed"
                    auth_record.updated_at = datetime.now(timezone.utc)
                    db.commit()

            return RedirectResponse(url=f"http://localhost:3001?digilocker_error={error}")

        if not code or not state:
            raise HTTPException(status_code=400, detail="Missing authorization code or state")

        candidate_id = state

        # Exchange code for token
        token_data = await exchange_code_for_token(code, candidate_id)

        # Store authentication data
        auth_record = await store_digilocker_auth(db, candidate_id, token_data)

        # Fetch documents from DigiLocker
        try:
            documents = await fetch_digilocker_documents(token_data.get("access_token"))
            await store_digilocker_documents(db, candidate_id, documents)

            # Sync with Supabase
            await sync_with_supabase(candidate_id, {"user_id": auth_record.digilocker_user_id}, documents)

        except Exception as doc_error:
            logger.error(f"Failed to fetch/store documents for {candidate_id}: {str(doc_error)}")
            # Don't fail the entire process if document fetch fails

        # Redirect to frontend with success
        return RedirectResponse(url=f"http://localhost:3001?digilocker_success=true")

    except Exception as e:
        logger.error(f"DigiLocker callback error: {str(e)}")
        if state:
            # Update auth status to failed
            auth_record = db.query(DigiLockerAuth).filter(
                DigiLockerAuth.candidate_id == state
            ).first()
            if auth_record:
                auth_record.auth_status = "failed"
                auth_record.updated_at = datetime.now(timezone.utc)
                db.commit()

        return RedirectResponse(url=f"http://localhost:3001?digilocker_error=callback_failed")

@app.get("/auth/digilocker/demo")
async def digilocker_demo_auth(request: Request, db: Session = Depends(get_db)):
    """🔐 Dynamic DigiLocker authentication demo - simulates real DigiLocker flow"""
    try:
        state = request.query_params.get("state")  # candidate_id

        if not state:
            return RedirectResponse(url=f"http://localhost:3001?digilocker_error=missing_state")

        candidate_id = state

        # Get candidate information for dynamic authentication
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            return RedirectResponse(url=f"http://localhost:3001?digilocker_error=candidate_not_found")

        # Generate dynamic DigiLocker user ID based on candidate's phone number
        # In real DigiLocker, this would be the user's registered mobile number
        digilocker_user_id = candidate.phone.replace("+", "").replace("-", "").replace(" ", "")[-10:]  # Last 10 digits

        # If no phone, generate based on candidate ID and email
        if not digilocker_user_id or len(digilocker_user_id) < 10:
            import hashlib
            hash_input = f"{candidate.email}{candidate.candidate_id}".encode()
            hash_digest = hashlib.md5(hash_input).hexdigest()
            digilocker_user_id = "".join([str(ord(c) % 10) for c in hash_digest[:10]])

        # Create or update authentication record with dynamic user ID
        auth_record = db.query(DigiLockerAuth).filter(
            DigiLockerAuth.candidate_id == candidate_id
        ).first()

        if auth_record:
            auth_record.auth_status = "authenticated"
            auth_record.digilocker_user_id = digilocker_user_id
            auth_record.access_token = f"token_{digilocker_user_id}_{candidate_id}"
            auth_record.token_expires_at = datetime.now(timezone.utc) + timedelta(hours=24)
            auth_record.updated_at = datetime.now(timezone.utc)
        else:
            auth_record = DigiLockerAuth(
                candidate_id=candidate_id,
                auth_status="authenticated",
                digilocker_user_id=digilocker_user_id,
                access_token=f"token_{digilocker_user_id}_{candidate_id}",
                token_expires_at=datetime.now(timezone.utc) + timedelta(hours=24)
            )
            db.add(auth_record)

        # Generate dynamic documents based on candidate information
        candidate_name = f"{candidate.first_name} {candidate.last_name}"
        masked_aadhaar = f"XXXX-XXXX-{digilocker_user_id[-4:]}"

        # Generate PAN based on name initials and random numbers
        name_initials = f"{candidate.first_name[0]}{candidate.last_name[0]}" if candidate.first_name and candidate.last_name else "AB"
        pan_suffix = digilocker_user_id[-4:] + "F"
        masked_pan = f"{name_initials}CDE{pan_suffix}"

        # Generate license number based on location and user ID
        license_number = f"DL14{datetime.now().year}{digilocker_user_id[-5:]}"

        # Generate EPIC number
        epic_number = f"{name_initials.upper()}{digilocker_user_id[-7:]}"

        # Generate passport number
        passport_number = f"Z{digilocker_user_id[-7:]}"

        dynamic_documents = [
            {
                "id": f"aadhaar_{digilocker_user_id}",
                "type": "aadhaar",
                "name": "Aadhaar Card",
                "uri": f"/documents/aadhaar/{digilocker_user_id}",
                "issuer": "UIDAI (Unique Identification Authority of India)",
                "date": "2018-05-15",
                "data": {
                    "name": candidate_name,
                    "mobile_number": digilocker_user_id,
                    "masked_aadhaar": masked_aadhaar,
                    "email": candidate.email,
                    "verification_status": "Verified by UIDAI",
                    "address": "As per Aadhaar records"
                }
            },
            {
                "id": f"pan_{digilocker_user_id}",
                "type": "pan",
                "name": "PAN Card",
                "uri": f"/documents/pan/{digilocker_user_id}",
                "issuer": "Income Tax Department, Government of India",
                "date": "2017-11-20",
                "data": {
                    "name": candidate_name,
                    "linked_mobile": digilocker_user_id,
                    "masked_pan": masked_pan,
                    "verification_status": "Verified by Income Tax Department"
                }
            },
            {
                "id": f"dl_{digilocker_user_id}",
                "type": "driving_license",
                "name": "Driving License",
                "uri": f"/documents/dl/{digilocker_user_id}",
                "issuer": "Regional Transport Office",
                "date": "2020-03-10",
                "data": {
                    "name": candidate_name,
                    "license_number": license_number,
                    "vehicle_class": "LMV (Light Motor Vehicle)",
                    "valid_till": "2040-03-09",
                    "linked_mobile": digilocker_user_id
                }
            },
            {
                "id": f"voter_{digilocker_user_id}",
                "type": "voter_id",
                "name": "Voter ID Card",
                "uri": f"/documents/voter/{digilocker_user_id}",
                "issuer": "Election Commission of India",
                "date": "2019-01-25",
                "data": {
                    "name": candidate_name,
                    "epic_number": epic_number,
                    "constituency": "Demo Constituency",
                    "linked_mobile": digilocker_user_id
                }
            },
            {
                "id": f"passport_{digilocker_user_id}",
                "type": "passport",
                "name": "Passport",
                "uri": f"/documents/passport/{digilocker_user_id}",
                "issuer": "Ministry of External Affairs, Government of India",
                "date": "2021-07-15",
                "data": {
                    "name": candidate_name,
                    "passport_number": passport_number,
                    "valid_till": "2031-07-14",
                    "place_of_issue": "Regional Passport Office",
                    "linked_mobile": digilocker_user_id
                }
            }
        ]

        # Store dynamic documents for candidate
        for doc_data in dynamic_documents:
            existing_doc = db.query(DigiLockerDocument).filter(
                DigiLockerDocument.candidate_id == candidate_id,
                DigiLockerDocument.document_id == doc_data["id"]
            ).first()

            if not existing_doc:
                document_record = DigiLockerDocument(
                    candidate_id=candidate_id,
                    document_type=doc_data["type"],
                    document_id=doc_data["id"],
                    document_name=doc_data["name"],
                    document_uri=doc_data["uri"],
                    issuer=doc_data["issuer"],
                    issue_date=datetime.strptime(doc_data["date"], "%Y-%m-%d").date(),
                    verification_status="pending",
                    document_data=doc_data.get("data", {})
                )
                db.add(document_record)

        db.commit()

        # Sync authentication to Supabase
        await sync_digilocker_auth_to_supabase(candidate_id, auth_record)

        logger.info(f"✅ Dynamic DigiLocker authentication completed for candidate {candidate_id} with user ID {digilocker_user_id}")

        # Redirect to frontend with success
        return RedirectResponse(url=f"http://localhost:3001?digilocker_success=true")

    except Exception as e:
        logger.error(f"DigiLocker authentication error: {str(e)}")
        return RedirectResponse(url=f"http://localhost:3001?digilocker_error=auth_failed")

@app.get("/candidates/{candidate_id}/digilocker/status", response_model=DigiLockerAuthResponse)
async def get_digilocker_status(candidate_id: str, db: Session = Depends(get_db)):
    """🔐 Get DigiLocker authentication status"""
    try:
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        auth_record = db.query(DigiLockerAuth).filter(
            DigiLockerAuth.candidate_id == candidate_id
        ).first()

        if not auth_record:
            raise HTTPException(status_code=404, detail="No DigiLocker authentication found")

        return auth_record

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get DigiLocker status for {candidate_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get DigiLocker status: {str(e)}")

@app.get("/candidates/{candidate_id}/digilocker/documents", response_model=List[DigiLockerDocumentResponse])
async def get_digilocker_documents(candidate_id: str, db: Session = Depends(get_db)):
    """🔐 Get DigiLocker documents for background verification"""
    try:
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        documents = db.query(DigiLockerDocument).filter(
            DigiLockerDocument.candidate_id == candidate_id
        ).order_by(DigiLockerDocument.created_at.desc()).all()

        return documents

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get DigiLocker documents for {candidate_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get DigiLocker documents: {str(e)}")

@app.post("/candidates/{candidate_id}/digilocker/verify-document/{document_id}")
async def verify_digilocker_document(candidate_id: str, document_id: int, db: Session = Depends(get_db)):
    """🔐 Verify a specific DigiLocker document"""
    try:
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        document = db.query(DigiLockerDocument).filter(
            DigiLockerDocument.id == document_id,
            DigiLockerDocument.candidate_id == candidate_id
        ).first()

        if not document:
            raise HTTPException(status_code=404, detail="Document not found")

        # Update verification status
        document.verification_status = "verified"
        document.updated_at = datetime.now(timezone.utc)

        db.commit()

        # Sync with Supabase
        if supabase:
            try:
                supabase.table("digilocker_documents").update({
                    "verification_status": "verified",
                    "verified_at": datetime.now(timezone.utc).isoformat()
                }).eq("id", document_id).execute()
            except Exception as e:
                logger.error(f"Failed to sync verification status to Supabase: {str(e)}")

        return {
            "message": "Document verified successfully",
            "document_id": document_id,
            "verification_status": "verified"
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Failed to verify document {document_id} for {candidate_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to verify document: {str(e)}")

@app.post("/candidates/{candidate_id}/documents/{document_id}/verify-with-digilocker")
async def verify_uploaded_document_with_digilocker(candidate_id: str, document_id: int, db: Session = Depends(get_db)):
    """🔐 Verify an uploaded document against DigiLocker for authenticity"""
    try:
        # Check if candidate exists
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        # Get the uploaded document
        uploaded_doc = db.query(AddressProof).filter(
            AddressProof.id == document_id,
            AddressProof.candidate_id == candidate_id
        ).first()

        if not uploaded_doc:
            raise HTTPException(status_code=404, detail="Uploaded document not found")

        # Check if candidate has DigiLocker authentication
        auth_record = db.query(DigiLockerAuth).filter(
            DigiLockerAuth.candidate_id == candidate_id,
            DigiLockerAuth.auth_status == "authenticated"
        ).first()

        if not auth_record:
            raise HTTPException(status_code=400, detail="DigiLocker authentication required. Please authenticate first.")

        # Get DigiLocker documents for comparison
        digilocker_docs = db.query(DigiLockerDocument).filter(
            DigiLockerDocument.candidate_id == candidate_id
        ).all()

        # Determine document type mapping
        doc_type_mapping = {
            "Aadhar Card": "aadhaar",
            "PAN Card": "pan",
            "Driver's License": "driving_license",
            "Passport": "passport"
        }

        expected_digilocker_type = doc_type_mapping.get(uploaded_doc.document_type)
        if not expected_digilocker_type:
            raise HTTPException(status_code=400, detail=f"Document type '{uploaded_doc.document_type}' not supported for DigiLocker verification")

        # Find matching DigiLocker document
        matching_digilocker_doc = None
        for doc in digilocker_docs:
            if doc.document_type == expected_digilocker_type:
                matching_digilocker_doc = doc
                break

        if not matching_digilocker_doc:
            # Update status as failed - no matching document in DigiLocker
            uploaded_doc.verification_status = "failed"
            uploaded_doc.verification_details = {
                "error": "No matching document found in DigiLocker",
                "expected_type": expected_digilocker_type,
                "available_types": [doc.document_type for doc in digilocker_docs]
            }
            uploaded_doc.verified_at = datetime.now(timezone.utc)
            db.commit()

            # Sync with Supabase
            await sync_document_verification_to_supabase(candidate_id, uploaded_doc)

            return {
                "message": "Document verification failed",
                "status": "failed",
                "reason": "No matching document found in DigiLocker",
                "document_id": document_id
            }

        # Perform verification logic (in real implementation, this would involve more sophisticated checks)
        verification_result = await perform_document_verification(uploaded_doc, matching_digilocker_doc, auth_record.access_token)

        # Update the uploaded document with verification results
        uploaded_doc.digilocker_verified = verification_result["is_genuine"]
        uploaded_doc.digilocker_document_id = matching_digilocker_doc.document_id
        uploaded_doc.verification_status = "genuine" if verification_result["is_genuine"] else "fake"
        uploaded_doc.verification_details = verification_result
        uploaded_doc.verified_at = datetime.now(timezone.utc)

        db.commit()

        # Sync with Supabase
        await sync_document_verification_to_supabase(candidate_id, uploaded_doc)

        return {
            "message": f"Document verification completed - {'Genuine' if verification_result['is_genuine'] else 'Fake'}",
            "status": uploaded_doc.verification_status,
            "is_genuine": verification_result["is_genuine"],
            "verification_details": verification_result,
            "document_id": document_id,
            "digilocker_document_id": matching_digilocker_doc.document_id
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Failed to verify uploaded document {document_id} for {candidate_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to verify document: {str(e)}")

async def perform_document_verification(uploaded_doc: AddressProof, digilocker_doc: DigiLockerDocument, access_token: str) -> dict:
    """Perform actual document verification against DigiLocker"""
    try:
        # Dynamic verification based on document properties
        verification_checks = []
        confidence_factors = []

        # Check 1: Document type matching
        doc_type_match = uploaded_doc.document_type.lower().replace(" ", "_") in digilocker_doc.document_type.lower()
        verification_checks.append(f"Document type match: {'✓' if doc_type_match else '✗'}")
        confidence_factors.append(0.3 if doc_type_match else 0.0)

        # Check 2: File format validation
        valid_formats = ['.pdf', '.jpg', '.jpeg', '.png']
        file_format_valid = any(uploaded_doc.filename.lower().endswith(fmt) for fmt in valid_formats)
        verification_checks.append(f"File format validation: {'✓' if file_format_valid else '✗'}")
        confidence_factors.append(0.2 if file_format_valid else 0.0)

        # Check 3: File size reasonableness (not too small/large)
        reasonable_size = 10000 < uploaded_doc.file_size < 10000000  # 10KB to 10MB
        verification_checks.append(f"File size validation: {'✓' if reasonable_size else '✗'}")
        confidence_factors.append(0.1 if reasonable_size else 0.0)

        # Check 4: DigiLocker document availability
        digilocker_available = digilocker_doc is not None
        verification_checks.append(f"DigiLocker document found: {'✓' if digilocker_available else '✗'}")
        confidence_factors.append(0.3 if digilocker_available else 0.0)

        # Check 5: Issuer validation (if available)
        issuer_valid = digilocker_doc.issuer is not None if digilocker_doc else False
        verification_checks.append(f"Issuer validation: {'✓' if issuer_valid else '✗'}")
        confidence_factors.append(0.1 if issuer_valid else 0.0)

        # Calculate dynamic confidence score
        total_confidence = sum(confidence_factors)

        # Determine if document is genuine based on confidence
        is_genuine = total_confidence >= 0.7  # 70% threshold for genuine

        # Dynamic verification status
        if total_confidence >= 0.9:
            status = "genuine"
        elif total_confidence >= 0.7:
            status = "genuine"
        elif total_confidence >= 0.4:
            status = "suspicious"
        else:
            status = "fake"

        verification_details = {
            "is_genuine": is_genuine,
            "confidence_score": round(total_confidence, 2),
            "verification_status": status,
            "verification_method": "Dynamic DigiLocker Analysis",
            "checks_performed": verification_checks,
            "confidence_breakdown": {
                "document_type_match": confidence_factors[0],
                "file_format_valid": confidence_factors[1],
                "file_size_reasonable": confidence_factors[2],
                "digilocker_available": confidence_factors[3],
                "issuer_valid": confidence_factors[4]
            },
            "verified_fields": {
                "document_type": digilocker_doc.document_type if digilocker_doc else "unknown",
                "issuer": digilocker_doc.issuer if digilocker_doc else "unknown",
                "issue_date": digilocker_doc.issue_date.isoformat() if digilocker_doc and digilocker_doc.issue_date else None,
                "uploaded_filename": uploaded_doc.original_filename,
                "uploaded_size": uploaded_doc.file_size,
                "uploaded_type": uploaded_doc.document_type
            },
            "verification_timestamp": datetime.now(timezone.utc).isoformat()
        }

        logger.info(f"Dynamic verification completed: {status} (confidence: {total_confidence:.2f})")
        return verification_details

    except Exception as e:
        logger.error(f"Document verification failed: {str(e)}")
        return {
            "is_genuine": False,
            "confidence_score": 0.0,
            "verification_status": "failed",
            "error": str(e),
            "verification_timestamp": datetime.now(timezone.utc).isoformat()
        }

async def sync_document_verification_to_supabase(candidate_id: str, document: AddressProof):
    """Sync document verification results to Supabase"""
    if not supabase:
        logger.warning("Supabase client not initialized")
        return

    try:
        # Sync to document_verifications table
        supabase.table("document_verifications").upsert({
            "candidate_id": candidate_id,
            "document_id": document.id,
            "document_type": document.document_type,
            "verification_status": document.verification_status,
            "digilocker_verified": document.digilocker_verified,
            "digilocker_document_id": document.digilocker_document_id,
            "verification_details": document.verification_details,
            "verified_at": document.verified_at.isoformat() if document.verified_at else None,
            "synced_at": datetime.now(timezone.utc).isoformat()
        }).execute()

        # Also sync to address_proofs table to keep it updated
        supabase.table("address_proofs").upsert({
            "id": document.id,
            "candidate_id": candidate_id,
            "document_type": document.document_type,
            "filename": document.filename,
            "original_filename": document.original_filename,
            "file_size": document.file_size,
            "content_type": document.content_type,
            "is_verified": document.is_verified,
            "digilocker_verified": document.digilocker_verified,
            "digilocker_document_id": document.digilocker_document_id,
            "verification_status": document.verification_status,
            "verification_details": document.verification_details,
            "verified_at": document.verified_at.isoformat() if document.verified_at else None,
            "created_at": document.created_at.isoformat(),
            "synced_at": datetime.now(timezone.utc).isoformat()
        }).execute()

        logger.info(f"Successfully synced document verification to Supabase for candidate {candidate_id}")

    except Exception as e:
        logger.error(f"Failed to sync document verification to Supabase for {candidate_id}: {str(e)}")
        # Don't raise exception as this is not critical

async def sync_uploaded_document_to_supabase(candidate_id: str, document: AddressProof):
    """Sync uploaded document to Supabase immediately after upload"""
    if not supabase:
        logger.warning("⚠️ Supabase client not initialized - skipping sync")
        return

    try:
        logger.info(f"🔄 Syncing uploaded document to Supabase for {candidate_id}")
        logger.info(f"📄 Document details: ID={document.id}, Type={document.document_type}, File={document.filename}")

        # Prepare data for sync with proper timestamps
        current_time = datetime.now(timezone.utc)
        sync_data = {
            "id": document.id,
            "candidate_id": candidate_id,
            "background_verification_id": document.background_verification_id,
            "document_type": document.document_type,
            "filename": document.filename,
            "original_filename": document.original_filename,
            "file_path": document.file_path,
            "file_size": document.file_size,
            "content_type": document.content_type,
            "is_verified": document.is_verified,
            "digilocker_verified": document.digilocker_verified or False,
            "digilocker_document_id": document.digilocker_document_id,
            "verification_status": document.verification_status or "pending",
            "verification_details": document.verification_details,
            "verified_at": document.verified_at.isoformat() if document.verified_at else None,
            "created_at": document.created_at.isoformat(),
            "updated_at": current_time.isoformat(),
            "synced_at": current_time.isoformat()
        }

        logger.info(f"📤 Sending data to Supabase: {sync_data}")

        # Sync to address_proofs table in Supabase
        result = supabase.table("address_proofs").upsert(sync_data).execute()

        logger.info(f"✅ Successfully synced uploaded document to Supabase for candidate {candidate_id}")
        logger.info(f"📊 Supabase response: {result.data if hasattr(result, 'data') else 'No data'}")

    except Exception as e:
        logger.error(f"❌ Failed to sync uploaded document to Supabase for {candidate_id}: {str(e)}")
        logger.error(f"Error type: {type(e).__name__}")
        logger.error(f"Full error details: {repr(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        # Don't raise exception as this is not critical

async def sync_uploaded_document_to_supabase_simple(candidate_id: str, document: AddressProof):
    """Simple, reliable sync to Supabase using INSERT instead of UPSERT"""
    if not supabase:
        logger.warning("⚠️ Supabase client not initialized - skipping sync")
        return

    try:
        logger.info(f"🔄 Simple sync to Supabase for {candidate_id} - Document {document.id}")

        # Prepare minimal data for sync
        sync_data = {
            "candidate_id": candidate_id,
            "document_type": document.document_type,
            "filename": document.filename,
            "original_filename": document.original_filename,
            "file_size": document.file_size or 0,
            "content_type": document.content_type or "application/octet-stream",
            "is_verified": document.is_verified or False,
            "verification_status": document.verification_status or "pending",
            "created_at": document.created_at.isoformat() if document.created_at else datetime.now(timezone.utc).isoformat()
        }

        logger.info(f"📤 Inserting to Supabase: {sync_data}")

        # Use INSERT instead of UPSERT to avoid conflicts
        result = supabase.table("address_proofs").insert(sync_data).execute()

        logger.info(f"✅ Simple sync successful for {candidate_id}")
        logger.info(f"📊 Result: {result.data if hasattr(result, 'data') else 'Success'}")

    except Exception as e:
        logger.error(f"❌ Simple sync failed for {candidate_id}: {str(e)}")
        logger.error(f"Error details: {repr(e)}")
        # Don't raise exception as this is not critical

@app.post("/admin/sync-all-to-supabase")
async def sync_all_data_to_supabase(db: Session = Depends(get_db)):
    """🔄 Sync ALL existing data from PostgreSQL to Supabase"""
    try:
        if not supabase:
            return {
                "status": "error",
                "message": "Supabase client not initialized"
            }

        # Clear existing data in Supabase to avoid duplicates
        logger.info("🧹 Clearing existing Supabase data...")
        supabase.table("address_proofs").delete().neq("id", 0).execute()
        supabase.table("digilocker_auth").delete().neq("id", 0).execute()

        sync_results = {
            "documents_synced": 0,
            "auth_records_synced": 0,
            "errors": []
        }

        # Sync all documents
        all_documents = db.query(AddressProof).all()
        logger.info(f"📄 Found {len(all_documents)} documents to sync")

        for doc in all_documents:
            try:
                await sync_uploaded_document_to_supabase_simple(doc.candidate_id, doc)
                sync_results["documents_synced"] += 1
            except Exception as e:
                sync_results["errors"].append(f"Document {doc.id}: {str(e)}")

        # Sync all auth records
        all_auth = db.query(DigiLockerAuth).all()
        logger.info(f"🔐 Found {len(all_auth)} auth records to sync")

        for auth in all_auth:
            try:
                await sync_digilocker_auth_to_supabase_simple(auth.candidate_id, auth)
                sync_results["auth_records_synced"] += 1
            except Exception as e:
                sync_results["errors"].append(f"Auth {auth.id}: {str(e)}")

        return {
            "status": "success",
            "message": "Complete sync to Supabase finished",
            "results": sync_results,
            "total_documents": len(all_documents),
            "total_auth_records": len(all_auth)
        }

    except Exception as e:
        logger.error(f"❌ Complete sync failed: {str(e)}")
        return {
            "status": "error",
            "message": f"Complete sync failed: {str(e)}",
            "error_details": str(e)
        }

async def sync_digilocker_auth_to_supabase_simple(candidate_id: str, auth_record: DigiLockerAuth):
    """Simple DigiLocker auth sync to Supabase"""
    if not supabase:
        return

    try:
        auth_data = {
            "candidate_id": candidate_id,
            "auth_status": auth_record.auth_status,
            "digilocker_user_id": auth_record.digilocker_user_id,
            "created_at": auth_record.created_at.isoformat() if auth_record.created_at else datetime.now(timezone.utc).isoformat()
        }

        result = supabase.table("digilocker_auth").insert(auth_data).execute()
        logger.info(f"✅ Auth synced for {candidate_id}")

    except Exception as e:
        logger.error(f"❌ Auth sync failed for {candidate_id}: {str(e)}")
        raise

async def sync_digilocker_auth_to_supabase(candidate_id: str, auth_record: DigiLockerAuth):
    """Sync DigiLocker authentication status to Supabase"""
    if not supabase:
        logger.warning("⚠️ Supabase client not initialized - skipping auth sync")
        return

    try:
        logger.info(f"🔄 Syncing DigiLocker auth to Supabase for {candidate_id}")

        # Prepare auth data for sync
        auth_data = {
            "candidate_id": candidate_id,
            "auth_status": auth_record.auth_status,
            "digilocker_user_id": auth_record.digilocker_user_id,
            "created_at": auth_record.created_at.isoformat(),
            "updated_at": auth_record.updated_at.isoformat(),
            "synced_at": datetime.now(timezone.utc).isoformat()
        }

        # Sync to digilocker_auth table in Supabase
        result = supabase.table("digilocker_auth").upsert(auth_data).execute()

        logger.info(f"✅ Successfully synced DigiLocker auth to Supabase for candidate {candidate_id}")
        logger.debug(f"Supabase auth sync result: {result}")

    except Exception as e:
        logger.error(f"❌ Failed to sync DigiLocker auth to Supabase for {candidate_id}: {str(e)}")
        logger.error(f"Error type: {type(e).__name__}")
        # Don't raise exception as this is not critical

@app.get("/candidates/{candidate_id}/documents/verification-status")
async def get_document_verification_status(candidate_id: str, db: Session = Depends(get_db)):
    """📋 Get verification status of all uploaded documents"""
    try:
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        # Get all uploaded documents
        documents = db.query(AddressProof).filter(
            AddressProof.candidate_id == candidate_id
        ).order_by(AddressProof.created_at.desc()).all()

        # Get DigiLocker authentication status
        auth_record = db.query(DigiLockerAuth).filter(
            DigiLockerAuth.candidate_id == candidate_id
        ).first()

        return {
            "candidate_id": candidate_id,
            "digilocker_authenticated": auth_record.auth_status == "authenticated" if auth_record else False,
            "total_documents": len(documents),
            "verified_documents": len([d for d in documents if d.digilocker_verified]),
            "pending_verification": len([d for d in documents if d.verification_status == "pending"]),
            "documents": [
                {
                    "id": doc.id,
                    "document_type": doc.document_type,
                    "filename": doc.original_filename,
                    "verification_status": doc.verification_status,
                    "digilocker_verified": doc.digilocker_verified,
                    "digilocker_document_id": doc.digilocker_document_id,
                    "verified_at": doc.verified_at.isoformat() if doc.verified_at else None,
                    "uploaded_at": doc.created_at.isoformat(),
                    "verification_details": doc.verification_details
                }
                for doc in documents
            ]
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get document verification status for {candidate_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get verification status: {str(e)}")

@app.get("/test/supabase-connection")
async def test_supabase_connection():
    """🧪 Test Supabase connection and sync functionality"""
    try:
        if not supabase:
            return {
                "status": "error",
                "message": "Supabase client not initialized",
                "supabase_url": SUPABASE_URL,
                "has_anon_key": bool(SUPABASE_ANON_KEY)
            }

        # Test connection by trying to read from a table
        result = supabase.table("address_proofs").select("*").limit(1).execute()

        return {
            "status": "success",
            "message": "Supabase connection working",
            "supabase_url": SUPABASE_URL,
            "has_anon_key": bool(SUPABASE_ANON_KEY),
            "test_query_success": True,
            "tables_accessible": ["address_proofs", "digilocker_auth", "document_verifications"],
            "architecture": "PostgreSQL (Primary) → Supabase (Sync/Backup)",
            "data_flow": "Frontend → Backend API → PostgreSQL → Supabase Sync"
        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"Supabase connection failed: {str(e)}",
            "supabase_url": SUPABASE_URL,
            "has_anon_key": bool(SUPABASE_ANON_KEY),
            "error_details": str(e)
        }

@app.get("/test/data-flow")
async def test_data_flow(db: Session = Depends(get_db)):
    """🔄 Show the complete data flow architecture"""
    try:
        # Count records in PostgreSQL
        postgres_candidates = db.query(Candidate).count()
        postgres_auth_records = db.query(DigiLockerAuth).count()
        postgres_documents = db.query(AddressProof).count()

        # Count records in Supabase (if connected)
        supabase_data = {}
        if supabase:
            try:
                supabase_auth = supabase.table("digilocker_auth").select("*", count="exact").execute()
                supabase_docs = supabase.table("address_proofs").select("*", count="exact").execute()
                supabase_data = {
                    "digilocker_auth_count": supabase_auth.count,
                    "address_proofs_count": supabase_docs.count,
                    "sync_status": "✅ Connected"
                }
            except Exception as e:
                supabase_data = {"sync_status": f"❌ Error: {str(e)}"}
        else:
            supabase_data = {"sync_status": "⚠️ Not configured"}

        return {
            "architecture": {
                "primary_database": "PostgreSQL (localhost:5433)",
                "sync_database": "Supabase Cloud",
                "data_flow": [
                    "1. Frontend sends requests to Backend API",
                    "2. Backend processes and stores in PostgreSQL",
                    "3. Backend syncs data to Supabase for backup/analytics",
                    "4. DigiLocker auth creates dynamic user IDs based on candidate info"
                ]
            },
            "postgresql_data": {
                "candidates": postgres_candidates,
                "digilocker_auth": postgres_auth_records,
                "documents": postgres_documents
            },
            "supabase_data": supabase_data,
            "digilocker_demo": {
                "type": "Dynamic simulation",
                "user_id_generation": "Based on candidate phone/email",
                "documents": "Generated dynamically per candidate",
                "no_hardcoding": "✅ All data is candidate-specific"
            }
        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"Data flow test failed: {str(e)}",
            "error_details": str(e)
        }

@app.post("/test/force-sync/{candidate_id}")
async def force_sync_candidate_data(candidate_id: str, db: Session = Depends(get_db)):
    """🔄 Force sync all candidate data to Supabase for testing"""
    try:
        if not supabase:
            return {
                "status": "error",
                "message": "Supabase client not initialized"
            }

        # Get candidate data
        candidate = db.query(Candidate).filter(Candidate.candidate_id == candidate_id).first()
        if not candidate:
            raise HTTPException(status_code=404, detail=f"Candidate {candidate_id} not found")

        sync_results = {
            "candidate_id": candidate_id,
            "documents_synced": 0,
            "auth_synced": 0,
            "errors": []
        }

        # Sync all documents for this candidate
        documents = db.query(AddressProof).filter(AddressProof.candidate_id == candidate_id).all()
        for doc in documents:
            try:
                await sync_uploaded_document_to_supabase(candidate_id, doc)
                sync_results["documents_synced"] += 1
            except Exception as e:
                sync_results["errors"].append(f"Document {doc.id}: {str(e)}")

        # Sync DigiLocker auth if exists
        auth_record = db.query(DigiLockerAuth).filter(DigiLockerAuth.candidate_id == candidate_id).first()
        if auth_record:
            try:
                await sync_digilocker_auth_to_supabase(candidate_id, auth_record)
                sync_results["auth_synced"] = 1
            except Exception as e:
                sync_results["errors"].append(f"Auth sync: {str(e)}")

        return {
            "status": "success",
            "message": f"Force sync completed for {candidate_id}",
            "results": sync_results
        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"Force sync failed: {str(e)}",
            "error_details": str(e)
        }

@app.get("/test/supabase-data")
async def check_supabase_data():
    """🔍 Check what data is actually in Supabase tables"""
    try:
        if not supabase:
            return {
                "status": "error",
                "message": "Supabase client not initialized"
            }

        # Get data from Supabase tables
        address_proofs_result = supabase.table("address_proofs").select("*").execute()
        digilocker_auth_result = supabase.table("digilocker_auth").select("*").execute()

        return {
            "status": "success",
            "message": "Supabase data retrieved",
            "data": {
                "address_proofs": {
                    "count": len(address_proofs_result.data),
                    "records": address_proofs_result.data
                },
                "digilocker_auth": {
                    "count": len(digilocker_auth_result.data),
                    "records": digilocker_auth_result.data
                }
            }
        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"Failed to retrieve Supabase data: {str(e)}",
            "error_details": str(e)
        }

@app.post("/test/direct-supabase-sync")
async def test_direct_supabase_sync(db: Session = Depends(get_db)):
    """🧪 Test direct Supabase sync with a sample document"""
    try:
        if not supabase:
            return {
                "status": "error",
                "message": "Supabase client not initialized"
            }

        # Get a real document from PostgreSQL
        document = db.query(AddressProof).filter(AddressProof.candidate_id == "CAND001").first()
        if not document:
            return {
                "status": "error",
                "message": "No documents found for CAND001"
            }

        logger.info(f"🧪 Testing direct sync for document: {document.id}")

        # Test simple insert first
        test_data = {
            "candidate_id": "CAND001",
            "document_type": "Test Sync Document",
            "filename": "test_sync.pdf",
            "original_filename": "test_sync.pdf",
            "file_size": 1024,
            "content_type": "application/pdf",
            "is_verified": False,
            "verification_status": "pending",
            "created_at": datetime.now(timezone.utc).isoformat()
        }

        logger.info(f"📤 Inserting test data: {test_data}")
        result = supabase.table("address_proofs").insert(test_data).execute()
        logger.info(f"📊 Insert result: {result}")

        return {
            "status": "success",
            "message": "Direct sync test completed",
            "test_data": test_data,
            "supabase_result": result.data if hasattr(result, 'data') else str(result)
        }

    except Exception as e:
        logger.error(f"❌ Direct sync test failed: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {
            "status": "error",
            "message": f"Direct sync test failed: {str(e)}",
            "error_details": str(e)
        }

@app.get("/admin/dashboard")
async def admin_dashboard(db: Session = Depends(get_db)):
    """🔧 Developer Dashboard - Complete overview of all client activities"""
    try:
        # Get all authentication activities
        auth_activities = db.query(DigiLockerAuth).order_by(DigiLockerAuth.created_at.desc()).limit(50).all()

        # Get all document verification activities
        document_activities = db.query(AddressProof).order_by(AddressProof.created_at.desc()).limit(50).all()

        # Get verification statistics
        total_authentications = db.query(DigiLockerAuth).count()
        successful_authentications = db.query(DigiLockerAuth).filter(DigiLockerAuth.auth_status == "authenticated").count()
        total_documents = db.query(AddressProof).count()
        verified_documents = db.query(AddressProof).filter(AddressProof.digilocker_verified == True).count()

        # Get recent activities from Supabase (if connected)
        supabase_activities = {}
        if supabase:
            try:
                # Get recent auth records
                recent_auth = supabase.table("digilocker_auth").select("*").order("created_at", desc=True).limit(10).execute()
                recent_docs = supabase.table("address_proofs").select("*").order("created_at", desc=True).limit(10).execute()

                supabase_activities = {
                    "recent_authentications": recent_auth.data,
                    "recent_documents": recent_docs.data,
                    "sync_status": "✅ Real-time sync active"
                }
            except Exception as e:
                supabase_activities = {"sync_status": f"❌ Sync error: {str(e)}"}

        return {
            "dashboard_overview": {
                "total_candidates": db.query(Candidate).count(),
                "total_authentications": total_authentications,
                "successful_authentications": successful_authentications,
                "authentication_success_rate": f"{(successful_authentications/total_authentications*100):.1f}%" if total_authentications > 0 else "0%",
                "total_documents_uploaded": total_documents,
                "verified_documents": verified_documents,
                "verification_success_rate": f"{(verified_documents/total_documents*100):.1f}%" if total_documents > 0 else "0%"
            },
            "recent_authentication_activities": [
                {
                    "candidate_id": auth.candidate_id,
                    "digilocker_user_id": auth.digilocker_user_id,
                    "status": auth.auth_status,
                    "authenticated_at": auth.created_at.isoformat() if auth.created_at else None,
                    "last_updated": auth.updated_at.isoformat() if auth.updated_at else None
                }
                for auth in auth_activities
            ],
            "recent_document_activities": [
                {
                    "candidate_id": doc.candidate_id,
                    "document_type": doc.document_type,
                    "filename": doc.original_filename,
                    "verification_status": doc.verification_status,
                    "digilocker_verified": doc.digilocker_verified,
                    "uploaded_at": doc.created_at.isoformat() if doc.created_at else None,
                    "verified_at": doc.verified_at.isoformat() if doc.verified_at else None,
                    "file_size": doc.file_size
                }
                for doc in document_activities
            ],
            "supabase_real_time_data": supabase_activities,
            "system_health": {
                "postgresql_status": "✅ Connected",
                "supabase_status": "✅ Connected" if supabase else "❌ Not configured",
                "digilocker_demo_status": "✅ Active",
                "last_updated": datetime.now(timezone.utc).isoformat()
            }
        }

    except Exception as e:
        logger.error(f"Admin dashboard error: {str(e)}")
        return {
            "status": "error",
            "message": f"Failed to load admin dashboard: {str(e)}",
            "error_details": str(e)
        }

if __name__ == "__main__":
    logger.info("🚀 Starting Job Application Dashboard API...")

    # Update database schema for payslips month_year column
    try:
        with engine.connect() as conn:
            # Check if column needs to be updated
            result = conn.execute(text("SELECT character_maximum_length FROM information_schema.columns WHERE table_name = 'payslips' AND column_name = 'month_year'"))
            current_length = result.fetchone()

            if current_length and current_length[0] == 7:
                logger.info("🔄 Updating payslips month_year column size...")
                conn.execute(text("ALTER TABLE payslips ALTER COLUMN month_year TYPE VARCHAR(50)"))
                conn.commit()
                logger.info("✅ Payslips column updated successfully")
            else:
                logger.info("✅ Payslips column already correct size")
    except Exception as e:
        logger.warning(f"⚠️ Could not update payslips column: {e}")

    # Update address_proofs table for DigiLocker verification
    try:
        with engine.connect() as conn:
            # Check if DigiLocker columns exist
            result = conn.execute(text("""
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = 'address_proofs'
                AND column_name IN ('digilocker_verified', 'digilocker_document_id', 'verification_status', 'verification_details', 'verified_at')
                AND table_schema = 'public'
            """))

            existing_columns = [row[0] for row in result.fetchall()]

            # Add missing columns
            if 'digilocker_verified' not in existing_columns:
                conn.execute(text("ALTER TABLE address_proofs ADD COLUMN digilocker_verified BOOLEAN DEFAULT FALSE"))
                logger.info("✅ Added digilocker_verified column")

            if 'digilocker_document_id' not in existing_columns:
                conn.execute(text("ALTER TABLE address_proofs ADD COLUMN digilocker_document_id VARCHAR(100)"))
                logger.info("✅ Added digilocker_document_id column")

            if 'verification_status' not in existing_columns:
                conn.execute(text("ALTER TABLE address_proofs ADD COLUMN verification_status VARCHAR(20) DEFAULT 'pending'"))
                logger.info("✅ Added verification_status column")

            if 'verification_details' not in existing_columns:
                conn.execute(text("ALTER TABLE address_proofs ADD COLUMN verification_details JSON"))
                logger.info("✅ Added verification_details column")

            if 'verified_at' not in existing_columns:
                conn.execute(text("ALTER TABLE address_proofs ADD COLUMN verified_at TIMESTAMP WITH TIME ZONE"))
                logger.info("✅ Added verified_at column")

            conn.commit()
            logger.info("✅ Address proofs table updated for DigiLocker verification")

    except Exception as e:
        logger.warning(f"⚠️ Could not update address_proofs table: {e}")

    # Ensure all tables are created including JobApplication
    Base.metadata.create_all(bind=engine)
    logger.info("✅ All database tables ready (including DigiLocker tables)")

    # Create test candidate and sample data for frontend testing
    try:
        with SessionLocal() as db:
            existing = db.query(Candidate).filter(Candidate.candidate_id == "CAND001").first()
            if not existing:
                test_candidate = Candidate(
                    candidate_id="CAND001",
                    first_name="John",
                    last_name="Doe",
                    email="<EMAIL>",
                    phone="******-123-4567",
                    position="Frontend Developer"
                )
                db.add(test_candidate)
                db.commit()
                logger.info("✅ Created test candidate CAND001")

                # Create sample interview feedback
                existing_feedback = db.query(InterviewFeedback).filter(InterviewFeedback.candidate_id == "CAND001").first()
                if not existing_feedback:
                    feedback = InterviewFeedback(
                        candidate_id="CAND001",
                        overall_score=82,
                        general_comments="Good technical knowledge with room for improvement in system design."
                    )
                    db.add(feedback)
                    db.flush()

                    # Add sample questions
                    questions = [
                        {
                            "question": "Explain React components and their lifecycle.",
                            "answer": "React components are reusable pieces of UI that can have state and lifecycle methods.",
                            "score": 85,
                            "feedback": "Good understanding of React fundamentals."
                        },
                        {
                            "question": "How do you handle state management in large applications?",
                            "answer": "I use Redux or Context API for global state and local state for component-specific data.",
                            "score": 78,
                            "feedback": "Solid approach, could mention more about state normalization."
                        }
                    ]

                    for q in questions:
                        question = InterviewQuestion(
                            feedback_id=feedback.id,
                            question=q["question"],
                            answer=q["answer"],
                            score=q["score"],
                            question_feedback=q["feedback"]
                        )
                        db.add(question)

                    db.commit()
                    logger.info("✅ Created sample interview feedback for CAND001")
    except Exception as e:
        logger.error(f"❌ Error creating test data: {str(e)}")

    uvicorn.run("apply:app", host="0.0.0.0", port=8000, reload=True)
