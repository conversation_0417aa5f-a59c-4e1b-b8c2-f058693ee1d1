#!/usr/bin/env python3
"""
Create Employment Documents Tables
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database.connection import engine
from app.models.candidate import Base
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_employment_tables():
    """Create employment document tables"""
    try:
        logger.info("🏗️ Creating employment document tables...")
        
        # Create all tables (this will only create missing ones)
        Base.metadata.create_all(bind=engine)
        
        logger.info("✅ Employment document tables created successfully!")
        
        # Verify tables exist
        with engine.connect() as conn:
            result = conn.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name IN ('offer_letters', 'contract_agreements', 'salary_negotiation_terms')
                ORDER BY table_name;
            """)
            
            tables = [row[0] for row in result]
            logger.info(f"📊 Created tables: {tables}")
            
            if len(tables) == 3:
                logger.info("🎉 All employment document tables created successfully!")
            else:
                logger.warning(f"⚠️ Expected 3 tables, found {len(tables)}")
                
    except Exception as e:
        logger.error(f"❌ Failed to create employment document tables: {e}")
        raise

if __name__ == "__main__":
    create_employment_tables()
