{"version": 3, "sources": ["../../../../src/client/components/router-reducer/ppr-navigations.ts"], "names": ["abortTask", "listenForDynamicRequest", "updateCacheNodeOnNavigation", "updateCacheNodeOnPopstateRestoration", "oldCacheNode", "oldRouterState", "newRouterState", "prefetchData", "prefetchHead", "oldRouterStateChildren", "newRouterStateChildren", "prefetchDataChildren", "oldParallelRoutes", "parallelRoutes", "prefetchParallelRoutes", "Map", "patchedRouterStateChildren", "task<PERSON><PERSON><PERSON><PERSON>", "parallelRouteKey", "newRouterStateChild", "oldRouterStateChild", "oldSegmentMapChild", "get", "prefetchDataChild", "newSegmentChild", "newSegmentKeyChild", "createRouterCache<PERSON>ey", "oldSegment<PERSON>hild", "undefined", "oldCacheNodeChild", "task<PERSON><PERSON><PERSON>", "PAGE_SEGMENT_KEY", "spawnPendingTask", "DEFAULT_SEGMENT_KEY", "spawnReusedTask", "matchSegment", "spawnTaskForMissingData", "set", "newCacheNodeChild", "node", "newSegmentMapChild", "route", "newCacheNode", "lazyData", "rsc", "prefetchRsc", "head", "loading", "lazyDataResolved", "patchRouterStateWithNewChildren", "children", "baseRouterState", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clone", "routerState", "pendingCacheNode", "createPendingCacheNode", "reusedRouterState", "task", "responsePromise", "then", "response", "flightData", "flightDataPath", "segmentPath", "slice", "serverRouterState", "length", "dynamicData", "dynamicHead", "writeDynamicDataIntoPendingTask", "error", "rootTask", "i", "segment", "taskSegment", "finishTaskUsingDynamicDataPayload", "taskNode", "finishPendingCacheNode", "serverChildren", "dynamicDataChildren", "serverRouterStateChild", "dynamicDataChild", "routerStateChildren", "routerStateChild", "segmentChild", "segmentKeyChild", "isLeafSegment", "size", "maybePrefetchRsc", "maybePrefetchLoading", "createDeferredRsc", "cacheNode", "taskState", "serverState", "taskStateChildren", "serverStateChildren", "dataChildren", "taskStateChild", "serverStateChild", "dataChild", "segmentMapChild", "taskSegmentChild", "taskSegmentKeyChild", "cacheNodeChild", "abortPendingCacheNode", "dynamicSegmentData", "isDeferredRsc", "resolve", "values", "reject", "newParallelRoutes", "shouldUsePrefetch", "status", "DEFERRED", "Symbol", "value", "tag", "pendingRsc", "Promise", "res", "rej", "fulfilledRsc", "rejectedRsc", "reason"], "mappings": ";;;;;;;;;;;;;;;;;IA2nBgBA,SAAS;eAATA;;IAnTAC,uBAAuB;eAAvBA;;IAxQAC,2BAA2B;eAA3BA;;IAuoBAC,oCAAoC;eAApCA;;;yBAzrBT;+BACsB;sCACQ;AAgD9B,SAASD,4BACdE,YAAuB,EACvBC,cAAiC,EACjCC,cAAiC,EACjCC,YAA+B,EAC/BC,YAA6B;IAE7B,0DAA0D;IAC1D,MAAMC,yBAAyBJ,cAAc,CAAC,EAAE;IAChD,MAAMK,yBAAyBJ,cAAc,CAAC,EAAE;IAChD,MAAMK,uBAAuBJ,YAAY,CAAC,EAAE;IAE5C,MAAMK,oBAAoBR,aAAaS,cAAc;IAErD,2EAA2E;IAC3E,gBAAgB;IAChB,0EAA0E;IAC1E,0EAA0E;IAC1E,4EAA4E;IAC5E,2EAA2E;IAC3E,0EAA0E;IAC1E,uEAAuE;IACvE,yEAAyE;IACzE,wEAAwE;IACxE,+BAA+B;IAC/B,MAAMC,yBAAyB,IAAIC,IAAIH;IAEvC,4EAA4E;IAC5E,4EAA4E;IAC5E,2EAA2E;IAC3E,6EAA6E;IAC7E,mBAAmB;IACnB,IAAII,6BAEA,CAAC;IACL,IAAIC,eAAe;IACnB,IAAK,IAAIC,oBAAoBR,uBAAwB;QACnD,MAAMS,sBACJT,sBAAsB,CAACQ,iBAAiB;QAC1C,MAAME,sBACJX,sBAAsB,CAACS,iBAAiB;QAC1C,MAAMG,qBAAqBT,kBAAkBU,GAAG,CAACJ;QACjD,MAAMK,oBACJZ,oBAAoB,CAACO,iBAAiB;QAExC,MAAMM,kBAAkBL,mBAAmB,CAAC,EAAE;QAC9C,MAAMM,qBAAqBC,IAAAA,0CAAoB,EAACF;QAEhD,MAAMG,kBACJP,wBAAwBQ,YAAYR,mBAAmB,CAAC,EAAE,GAAGQ;QAE/D,MAAMC,oBACJR,uBAAuBO,YACnBP,mBAAmBC,GAAG,CAACG,sBACvBG;QAEN,IAAIE;QACJ,IAAIN,oBAAoBO,yBAAgB,EAAE;YACxC,wEAAwE;YACxE,YAAY;YACZD,YAAYE,iBACVb,qBACAI,sBAAsBK,YAAYL,oBAAoB,MACtDf;QAEJ,OAAO,IAAIgB,oBAAoBS,4BAAmB,EAAE;YAClD,0DAA0D;YAC1D,EAAE;YACF,yEAAyE;YACzE,uEAAuE;YACvE,sEAAsE;YACtE,oEAAoE;YACpE,WAAW;YACX,IAAIb,wBAAwBQ,WAAW;gBACrC,sEAAsE;gBACtE,oEAAoE;gBACpE,mEAAmE;gBACnEE,YAAYI,gBAAgBd;YAC9B,OAAO;gBACL,oEAAoE;gBACpEU,YAAYE,iBACVb,qBACAI,sBAAsBK,YAAYL,oBAAoB,MACtDf;YAEJ;QACF,OAAO,IACLmB,oBAAoBC,aACpBO,IAAAA,2BAAY,EAACX,iBAAiBG,kBAC9B;YACA,IACEE,sBAAsBD,aACtBR,wBAAwBQ,WACxB;gBACA,qDAAqD;gBACrD,IAAIL,sBAAsBK,aAAaL,sBAAsB,MAAM;oBACjE,mCAAmC;oBACnCO,YAAY5B,4BACV2B,mBACAT,qBACAD,qBACAI,mBACAf;gBAEJ,OAAO;oBACL,kEAAkE;oBAClE,iEAAiE;oBACjE,kEAAkE;oBAClE,kEAAkE;oBAClE,4BAA4B;oBAC5BsB,YAAYM,wBAAwBjB;gBACtC;YACF,OAAO;gBACL,kEAAkE;gBAClE,oEAAoE;gBACpE,iBAAiB;gBACjBW,YAAYE,iBACVb,qBACAI,sBAAsBK,YAAYL,oBAAoB,MACtDf;YAEJ;QACF,OAAO;YACL,mDAAmD;YACnDsB,YAAYE,iBACVb,qBACAI,sBAAsBK,YAAYL,oBAAoB,MACtDf;QAEJ;QAEA,IAAIsB,cAAc,MAAM;YACtB,qEAAqE;YACrE,IAAIb,iBAAiB,MAAM;gBACzBA,eAAe,IAAIF;YACrB;YACAE,aAAaoB,GAAG,CAACnB,kBAAkBY;YACnC,MAAMQ,oBAAoBR,UAAUS,IAAI;YACxC,IAAID,sBAAsB,MAAM;gBAC9B,MAAME,qBAAsC,IAAIzB,IAAIM;gBACpDmB,mBAAmBH,GAAG,CAACZ,oBAAoBa;gBAC3CxB,uBAAuBuB,GAAG,CAACnB,kBAAkBsB;YAC/C;YAEA,oEAAoE;YACpE,uEAAuE;YACvE,YAAY;YACZxB,0BAA0B,CAACE,iBAAiB,GAAGY,UAAUW,KAAK;QAChE,OAAO;YACL,mEAAmE;YACnEzB,0BAA0B,CAACE,iBAAiB,GAAGC;QACjD;IACF;IAEA,IAAIF,iBAAiB,MAAM;QACzB,6BAA6B;QAC7B,OAAO;IACT;IAEA,MAAMyB,eAA+B;QACnCC,UAAU;QACVC,KAAKxC,aAAawC,GAAG;QACrB,0EAA0E;QAC1E,qEAAqE;QACrE,2EAA2E;QAC3E,0EAA0E;QAC1E,2EAA2E;QAC3E,qCAAqC;QACrCC,aAAazC,aAAayC,WAAW;QACrCC,MAAM1C,aAAa0C,IAAI;QACvBtC,cAAcJ,aAAaI,YAAY;QACvCuC,SAAS3C,aAAa2C,OAAO;QAE7B,yEAAyE;QACzElC,gBAAgBC;QAChBkC,kBAAkB;IACpB;IAEA,OAAO;QACL,kEAAkE;QAClEP,OAAOQ,gCACL3C,gBACAU;QAEFuB,MAAMG;QACNQ,UAAUjC;IACZ;AACF;AAEA,SAASgC,gCACPE,eAAkC,EAClCC,WAA8D;IAE9D,MAAMC,QAA2B;QAACF,eAAe,CAAC,EAAE;QAAEC;KAAY;IAClE,4EAA4E;IAC5E,2EAA2E;IAC3E,uCAAuC;IACvC,IAAI,KAAKD,iBAAiB;QACxBE,KAAK,CAAC,EAAE,GAAGF,eAAe,CAAC,EAAE;IAC/B;IACA,IAAI,KAAKA,iBAAiB;QACxBE,KAAK,CAAC,EAAE,GAAGF,eAAe,CAAC,EAAE;IAC/B;IACA,IAAI,KAAKA,iBAAiB;QACxBE,KAAK,CAAC,EAAE,GAAGF,eAAe,CAAC,EAAE;IAC/B;IACA,OAAOE;AACT;AAEA,SAASrB,iBACPsB,WAA8B,EAC9B/C,YAAsC,EACtCC,YAA6B;IAE7B,sEAAsE;IACtE,MAAM+C,mBAAmBC,uBACvBF,aACA/C,cACAC;IAEF,OAAO;QACLiC,OAAOa;QACPf,MAAMgB;QACNL,UAAU;IACZ;AACF;AAEA,SAAShB,gBAAgBuB,iBAAoC;IAC3D,mEAAmE;IACnE,0DAA0D;IAC1D,OAAO;QACLhB,OAAOgB;QACPlB,MAAM;QACNW,UAAU;IACZ;AACF;AAEA,SAASd,wBAAwBkB,WAA8B;IAC7D,wEAAwE;IACxE,6EAA6E;IAC7E,wDAAwD;IACxD,MAAMC,mBAAmBC,uBAAuBF,aAAa,MAAM;IACnE,OAAO;QACLb,OAAOa;QACPf,MAAMgB;QACNL,UAAU;IACZ;AACF;AAiBO,SAASjD,wBACdyD,IAAU,EACVC,eAAmD;IAEnDA,gBAAgBC,IAAI,CAClB,CAACC;QACC,MAAMC,aAAaD,QAAQ,CAAC,EAAE;QAC9B,KAAK,MAAME,kBAAkBD,WAAY;YACvC,MAAME,cAAcD,eAAeE,KAAK,CAAC,GAAG,CAAC;YAC7C,MAAMC,oBAAoBH,cAAc,CAACA,eAAeI,MAAM,GAAG,EAAE;YACnE,MAAMC,cAAcL,cAAc,CAACA,eAAeI,MAAM,GAAG,EAAE;YAC7D,MAAME,cAAcN,cAAc,CAACA,eAAeI,MAAM,GAAG,EAAE;YAE7D,IAAI,OAAOH,gBAAgB,UAAU;gBAInC;YACF;YAEAM,gCACEZ,MACAM,aACAE,mBACAE,aACAC;QAEJ;QAEA,wEAAwE;QACxE,qEAAqE;QACrE,6DAA6D;QAC7DrE,UAAU0D,MAAM;IAClB,GACA,CAACa;QACC,2CAA2C;QAC3CvE,UAAU0D,MAAMa;IAClB;AAEJ;AAEA,SAASD,gCACPE,QAAc,EACdR,WAA8B,EAC9BE,iBAAoC,EACpCE,WAA8B,EAC9BC,WAA4B;IAE5B,4EAA4E;IAC5E,0EAA0E;IAC1E,qCAAqC;IACrC,EAAE;IACF,8EAA8E;IAC9E,qCAAqC;IACrC,EAAE;IACF,6DAA6D;IAC7D,EAAE;IACF,yEAAyE;IACzE,IAAIX,OAAOc;IACX,IAAK,IAAIC,IAAI,GAAGA,IAAIT,YAAYG,MAAM,EAAEM,KAAK,EAAG;QAC9C,MAAMvD,mBAA2B8C,WAAW,CAACS,EAAE;QAC/C,MAAMC,UAAmBV,WAAW,CAACS,IAAI,EAAE;QAC3C,MAAMxD,eAAeyC,KAAKR,QAAQ;QAClC,IAAIjC,iBAAiB,MAAM;YACzB,MAAMa,YAAYb,aAAaK,GAAG,CAACJ;YACnC,IAAIY,cAAcF,WAAW;gBAC3B,MAAM+C,cAAc7C,UAAUW,KAAK,CAAC,EAAE;gBACtC,IAAIN,IAAAA,2BAAY,EAACuC,SAASC,cAAc;oBACtC,mEAAmE;oBACnEjB,OAAO5B;oBACP;gBACF;YACF;QACF;QACA,2EAA2E;QAC3E,4EAA4E;QAC5E,wEAAwE;QACxE,8BAA8B;QAC9B;IACF;IAEA8C,kCACElB,MACAQ,mBACAE,aACAC;AAEJ;AAEA,SAASO,kCACPlB,IAAU,EACVQ,iBAAoC,EACpCE,WAA8B,EAC9BC,WAA4B;IAE5B,0EAA0E;IAC1E,4CAA4C;IAC5C,MAAMpD,eAAeyC,KAAKR,QAAQ;IAClC,MAAM2B,WAAWnB,KAAKnB,IAAI;IAC1B,IAAItB,iBAAiB,MAAM;QACzB,wEAAwE;QACxE,iEAAiE;QACjE,oBAAoB;QACpB,IAAI4D,aAAa,MAAM;YACrBC,uBACED,UACAnB,KAAKjB,KAAK,EACVyB,mBACAE,aACAC;YAEF,uDAAuD;YACvDX,KAAKnB,IAAI,GAAG;QACd;QACA;IACF;IACA,2EAA2E;IAC3E,wDAAwD;IACxD,MAAMwC,iBAAiBb,iBAAiB,CAAC,EAAE;IAC3C,MAAMc,sBAAsBZ,WAAW,CAAC,EAAE;IAE1C,IAAK,MAAMlD,oBAAoBgD,kBAAmB;QAChD,MAAMe,yBACJF,cAAc,CAAC7D,iBAAiB;QAClC,MAAMgE,mBACJF,mBAAmB,CAAC9D,iBAAiB;QAEvC,MAAMY,YAAYb,aAAaK,GAAG,CAACJ;QACnC,IAAIY,cAAcF,WAAW;YAC3B,MAAM+C,cAAc7C,UAAUW,KAAK,CAAC,EAAE;YACtC,IACEN,IAAAA,2BAAY,EAAC8C,sBAAsB,CAAC,EAAE,EAAEN,gBACxCO,qBAAqB,QACrBA,qBAAqBtD,WACrB;gBACA,mEAAmE;gBACnE,OAAOgD,kCACL9C,WACAmD,wBACAC,kBACAb;YAEJ;QACF;IACA,2EAA2E;IAC3E,sEAAsE;IACtE,wEAAwE;IACxE,8BAA8B;IAChC;AACF;AAEA,SAASb,uBACPF,WAA8B,EAC9B/C,YAAsC,EACtCC,YAA6B;IAE7B,MAAM2E,sBAAsB7B,WAAW,CAAC,EAAE;IAC1C,MAAM3C,uBAAuBJ,iBAAiB,OAAOA,YAAY,CAAC,EAAE,GAAG;IAEvE,MAAMM,iBAAiB,IAAIE;IAC3B,IAAK,IAAIG,oBAAoBiE,oBAAqB;QAChD,MAAMC,mBACJD,mBAAmB,CAACjE,iBAAiB;QACvC,MAAMK,oBACJZ,yBAAyB,OACrBA,oBAAoB,CAACO,iBAAiB,GACtC;QAEN,MAAMmE,eAAeD,gBAAgB,CAAC,EAAE;QACxC,MAAME,kBAAkB5D,IAAAA,0CAAoB,EAAC2D;QAE7C,MAAM/C,oBAAoBkB,uBACxB4B,kBACA7D,sBAAsBK,YAAY,OAAOL,mBACzCf;QAGF,MAAMgC,qBAAsC,IAAIzB;QAChDyB,mBAAmBH,GAAG,CAACiD,iBAAiBhD;QACxCzB,eAAewB,GAAG,CAACnB,kBAAkBsB;IACvC;IAEA,4EAA4E;IAC5E,mEAAmE;IACnE,MAAM+C,gBAAgB1E,eAAe2E,IAAI,KAAK;IAE9C,MAAMC,mBAAmBlF,iBAAiB,OAAOA,YAAY,CAAC,EAAE,GAAG;IACnE,MAAMmF,uBAAuBnF,iBAAiB,OAAOA,YAAY,CAAC,EAAE,GAAG;IACvE,OAAO;QACLoC,UAAU;QACV9B,gBAAgBA;QAEhBgC,aAAa4C,qBAAqB7D,YAAY6D,mBAAmB;QACjEjF,cAAc+E,gBAAgB/E,eAAe;QAC7CuC,SAAS2C,yBAAyB9D,YAAY8D,uBAAuB;QAErE,qEAAqE;QACrE,wCAAwC;QACxC9C,KAAK+C;QACL7C,MAAMyC,gBAAgBI,sBAAsB;QAC5C3C,kBAAkB;IACpB;AACF;AAEA,SAAS8B,uBACPc,SAAoB,EACpBC,SAA4B,EAC5BC,WAA8B,EAC9B1B,WAA8B,EAC9BC,WAA4B;IAE5B,8EAA8E;IAC9E,8EAA8E;IAC9E,4EAA4E;IAC5E,8EAA8E;IAC9E,8DAA8D;IAC9D,6BAA6B;IAC7B,EAAE;IACF,qEAAqE;IACrE,8EAA8E;IAC9E,gEAAgE;IAChE,MAAM0B,oBAAoBF,SAAS,CAAC,EAAE;IACtC,MAAMG,sBAAsBF,WAAW,CAAC,EAAE;IAC1C,MAAMG,eAAe7B,WAAW,CAAC,EAAE;IAEnC,8EAA8E;IAC9E,6EAA6E;IAC7E,uCAAuC;IACvC,MAAMvD,iBAAiB+E,UAAU/E,cAAc;IAC/C,IAAK,IAAIK,oBAAoB6E,kBAAmB;QAC9C,MAAMG,iBACJH,iBAAiB,CAAC7E,iBAAiB;QACrC,MAAMiF,mBACJH,mBAAmB,CAAC9E,iBAAiB;QACvC,MAAMkF,YACJH,YAAY,CAAC/E,iBAAiB;QAEhC,MAAMmF,kBAAkBxF,eAAeS,GAAG,CAACJ;QAC3C,MAAMoF,mBAAmBJ,cAAc,CAAC,EAAE;QAC1C,MAAMK,sBAAsB7E,IAAAA,0CAAoB,EAAC4E;QAEjD,MAAME,iBACJH,oBAAoBzE,YAChByE,gBAAgB/E,GAAG,CAACiF,uBACpB3E;QAEN,IAAI4E,mBAAmB5E,WAAW;YAChC,IACEuE,qBAAqBvE,aACrBO,IAAAA,2BAAY,EAACmE,kBAAkBH,gBAAgB,CAAC,EAAE,GAClD;gBACA,IAAIC,cAAcxE,aAAawE,cAAc,MAAM;oBACjD,+DAA+D;oBAC/DtB,uBACE0B,gBACAN,gBACAC,kBACAC,WACA/B;gBAEJ,OAAO;oBACL,kEAAkE;oBAClE,oEAAoE;oBACpE,sEAAsE;oBACtE,+CAA+C;oBAC/CoC,sBAAsBP,gBAAgBM,gBAAgB;gBACxD;YACF,OAAO;gBACL,kEAAkE;gBAClE,uBAAuB;gBACvBC,sBAAsBP,gBAAgBM,gBAAgB;YACxD;QACF,OAAO;QACL,wEAAwE;QACxE,gEAAgE;QAChE,iEAAiE;QACjE,wDAAwD;QAC1D;IACF;IAEA,2EAA2E;IAC3E,qBAAqB;IACrB,MAAM5D,MAAMgD,UAAUhD,GAAG;IACzB,MAAM8D,qBAAqBtC,WAAW,CAAC,EAAE;IACzC,IAAIxB,QAAQ,MAAM;QAChB,oEAAoE;QACpE,qEAAqE;QACrEgD,UAAUhD,GAAG,GAAG8D;IAClB,OAAO,IAAIC,cAAc/D,MAAM;QAC7B,0EAA0E;QAC1E,sEAAsE;QACtE,sEAAsE;QACtEA,IAAIgE,OAAO,CAACF;IACd,OAAO;IACL,uEAAuE;IACvE,sEAAsE;IACxE;IAEA,8EAA8E;IAC9E,yEAAyE;IACzE,cAAc;IACd,MAAM5D,OAAO8C,UAAU9C,IAAI;IAC3B,IAAI6D,cAAc7D,OAAO;QACvBA,KAAK8D,OAAO,CAACvC;IACf;AACF;AAEO,SAASrE,UAAU0D,IAAU,EAAEa,KAAU;IAC9C,MAAMqB,YAAYlC,KAAKnB,IAAI;IAC3B,IAAIqD,cAAc,MAAM;QACtB,+CAA+C;QAC/C;IACF;IAEA,MAAM3E,eAAeyC,KAAKR,QAAQ;IAClC,IAAIjC,iBAAiB,MAAM;QACzB,kEAAkE;QAClE,aAAa;QACbwF,sBAAsB/C,KAAKjB,KAAK,EAAEmD,WAAWrB;IAC/C,OAAO;QACL,sEAAsE;QACtE,2EAA2E;QAC3E,6BAA6B;QAC7B,KAAK,MAAMzC,aAAab,aAAa4F,MAAM,GAAI;YAC7C7G,UAAU8B,WAAWyC;QACvB;IACF;IAEA,uDAAuD;IACvDb,KAAKnB,IAAI,GAAG;AACd;AAEA,SAASkE,sBACPnD,WAA8B,EAC9BsC,SAAoB,EACpBrB,KAAU;IAEV,6EAA6E;IAC7E,yCAAyC;IACzC,EAAE;IACF,6DAA6D;IAC7D,MAAMY,sBAAsB7B,WAAW,CAAC,EAAE;IAC1C,MAAMzC,iBAAiB+E,UAAU/E,cAAc;IAC/C,IAAK,IAAIK,oBAAoBiE,oBAAqB;QAChD,MAAMC,mBACJD,mBAAmB,CAACjE,iBAAiB;QACvC,MAAMmF,kBAAkBxF,eAAeS,GAAG,CAACJ;QAC3C,IAAImF,oBAAoBzE,WAAW;YAGjC;QACF;QACA,MAAMyD,eAAeD,gBAAgB,CAAC,EAAE;QACxC,MAAME,kBAAkB5D,IAAAA,0CAAoB,EAAC2D;QAC7C,MAAMmB,iBAAiBH,gBAAgB/E,GAAG,CAACgE;QAC3C,IAAIkB,mBAAmB5E,WAAW;YAChC6E,sBAAsBrB,kBAAkBoB,gBAAgBjC;QAC1D,OAAO;QACL,wEAAwE;QACxE,wDAAwD;QAC1D;IACF;IACA,MAAM3B,MAAMgD,UAAUhD,GAAG;IACzB,IAAI+D,cAAc/D,MAAM;QACtB,IAAI2B,UAAU,MAAM;YAClB,gDAAgD;YAChD3B,IAAIgE,OAAO,CAAC;QACd,OAAO;YACL,+CAA+C;YAC/ChE,IAAIkE,MAAM,CAACvC;QACb;IACF;IAEA,8EAA8E;IAC9E,4EAA4E;IAC5E,2EAA2E;IAC3E,6DAA6D;IAC7D,MAAMzB,OAAO8C,UAAU9C,IAAI;IAC3B,IAAI6D,cAAc7D,OAAO;QACvBA,KAAK8D,OAAO,CAAC;IACf;AACF;AAEO,SAASzG,qCACdC,YAAuB,EACvBkD,WAA8B;IAE9B,2EAA2E;IAC3E,4EAA4E;IAC5E,4EAA4E;IAC5E,4EAA4E;IAC5E,0CAA0C;IAC1C,EAAE;IACF,6EAA6E;IAC7E,8EAA8E;IAC9E,wDAAwD;IAExD,MAAM6B,sBAAsB7B,WAAW,CAAC,EAAE;IAC1C,MAAM1C,oBAAoBR,aAAaS,cAAc;IACrD,MAAMkG,oBAAoB,IAAIhG,IAAIH;IAClC,IAAK,IAAIM,oBAAoBiE,oBAAqB;QAChD,MAAMC,mBACJD,mBAAmB,CAACjE,iBAAiB;QACvC,MAAMmE,eAAeD,gBAAgB,CAAC,EAAE;QACxC,MAAME,kBAAkB5D,IAAAA,0CAAoB,EAAC2D;QAC7C,MAAMhE,qBAAqBT,kBAAkBU,GAAG,CAACJ;QACjD,IAAIG,uBAAuBO,WAAW;YACpC,MAAMC,oBAAoBR,mBAAmBC,GAAG,CAACgE;YACjD,IAAIzD,sBAAsBD,WAAW;gBACnC,MAAMU,oBAAoBnC,qCACxB0B,mBACAuD;gBAEF,MAAM5C,qBAAqB,IAAIzB,IAAIM;gBACnCmB,mBAAmBH,GAAG,CAACiD,iBAAiBhD;gBACxCyE,kBAAkB1E,GAAG,CAACnB,kBAAkBsB;YAC1C;QACF;IACF;IAEA,kEAAkE;IAClE,EAAE;IACF,0EAA0E;IAC1E,4EAA4E;IAC5E,2EAA2E;IAC3E,8EAA8E;IAC9E,6EAA6E;IAC7E,sBAAsB;IACtB,MAAMI,MAAMxC,aAAawC,GAAG;IAC5B,MAAMoE,oBAAoBL,cAAc/D,QAAQA,IAAIqE,MAAM,KAAK;IAE/D,OAAO;QACLtE,UAAU;QACVC;QACAE,MAAM1C,aAAa0C,IAAI;QAEvBtC,cAAcwG,oBAAoB5G,aAAaI,YAAY,GAAG;QAC9DqC,aAAamE,oBAAoB5G,aAAayC,WAAW,GAAG;QAC5DE,SAASiE,oBAAoB5G,aAAa2C,OAAO,GAAG;QAEpD,kDAAkD;QAClDlC,gBAAgBkG;QAChB/D,kBAAkB;IACpB;AACF;AAEA,MAAMkE,WAAWC;AA8BjB,8EAA8E;AAC9E,gFAAgF;AAChF,8EAA8E;AAC9E,mEAAmE;AACnE,SAASR,cAAcS,KAAU;IAC/B,OAAOA,SAASA,MAAMC,GAAG,KAAKH;AAChC;AAEA,SAASvB;IACP,IAAIiB;IACJ,IAAIE;IACJ,MAAMQ,aAAa,IAAIC,QAAyB,CAACC,KAAKC;QACpDb,UAAUY;QACVV,SAASW;IACX;IACAH,WAAWL,MAAM,GAAG;IACpBK,WAAWV,OAAO,GAAG,CAACQ;QACpB,IAAIE,WAAWL,MAAM,KAAK,WAAW;YACnC,MAAMS,eAAqCJ;YAC3CI,aAAaT,MAAM,GAAG;YACtBS,aAAaN,KAAK,GAAGA;YACrBR,QAAQQ;QACV;IACF;IACAE,WAAWR,MAAM,GAAG,CAACvC;QACnB,IAAI+C,WAAWL,MAAM,KAAK,WAAW;YACnC,MAAMU,cAAmCL;YACzCK,YAAYV,MAAM,GAAG;YACrBU,YAAYC,MAAM,GAAGrD;YACrBuC,OAAOvC;QACT;IACF;IACA+C,WAAWD,GAAG,GAAGH;IACjB,OAAOI;AACT"}